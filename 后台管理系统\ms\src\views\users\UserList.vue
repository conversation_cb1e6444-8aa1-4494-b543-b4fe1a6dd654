<template>
  <div class="user-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable></el-input>
        </el-form-item>
    
      
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div>
            <el-button type="success" @click="handleImport">
              <el-icon><Upload /></el-icon> 批量导入
            </el-button>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增用户
            </el-button>
            <el-button type="danger" :disabled="multipleSelection.length === 0" @click="handleBatchDelete">
              <el-icon><Delete /></el-icon> 批量删除
            </el-button>
            <el-button @click="refreshTable">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.role === 'admin'" type="danger">管理员</el-tag>
            <el-tag v-else-if="scope.row.role === 'teacher'" type="warning">带教老师</el-tag>
            <el-tag v-else-if="scope.row.role === 'student'" type="success">学生</el-tag>
            <el-tag v-else type="info">{{ scope.row.role }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="student_id" label="学生ID" width="120" />
       
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        :model="userForm"
        :rules="userFormRules"
        ref="userFormRef"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="dialogType === 'edit'"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item v-if="dialogType === 'add'" label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="带教老师" value="teacher"></el-option>
            <el-option label="学生" value="student"></el-option>
          </el-select>
        </el-form-item>

        <!-- 当角色是学生时，显示实习生选择器 -->
        <el-form-item v-if="userForm.role === 'student'" label="实习生" prop="student_id">
          <el-select 
            v-model="userForm.student_id" 
            placeholder="请选择关联的实习生" 
            filterable
            :loading="studentListLoading"
          >
            <el-option
              v-for="item in studentList"
              :key="item.id"
              :label="`${item.name} - ${item.school}`"
              :value="item.id"
            >
              <div class="student-option">
                <span>{{ item.name }}</span>
                <span class="student-school">{{ item.school }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">将用户账号关联到现有实习生</div>
        </el-form-item>

        <!-- 当角色不是学生时，隐藏学号字段 -->
        <el-form-item v-if="userForm.role !== 'student'" label="学号" prop="student_id">
          <el-input v-model="userForm.student_id" placeholder="请输入学号（选填）"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入用户"
      width="600px"
    >
      <div class="import-container">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <p>1. 请下载Excel模板，按照模板格式填写数据</p>
            <p>2. 支持的文件格式：.xls, .xlsx</p>
            <p>3. 文件大小不超过10MB</p>
            <p>4. 必填字段：用户名、密码、姓名</p>
            <p>5. 角色字段：管理员、教师、学生（默认为学生）</p>
            <p>6. 注意：用户导入时不会自动关联实习生，需要后续手动关联</p>
          </template>
        </el-alert>

        <div class="template-download" style="margin-bottom: 20px">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon> 下载Excel模板
          </el-button>
        </div>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
          accept=".xls,.xlsx"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 .xls/.xlsx 文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :loading="importLoading">
            确认导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Refresh, Upload, Download, UploadFilled } from '@element-plus/icons-vue'
import userService from '@/services/userService'
import studentService from '@/services/studentService'

const loading = ref(false)
const studentListLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' or 'edit'
const userFormRef = ref(null)
const studentList = ref([]) // 学生列表

// 导入相关变量
const importDialogVisible = ref(false)
const importLoading = ref(false)
const uploadRef = ref(null)
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  phone: '',
  status: ''
})

// 用户表单
const userForm = reactive({
  id: '',
  username: '',
  name: '',
  password: '',
  phone: '',
  email: '',
  role: 'student',
  student_id: '',
  status: 1
})

// 获取学生列表
const fetchStudentList = async () => {
  studentListLoading.value = true
  try {
    const response = await studentService.getStudents()
    studentList.value = response.data.data
  } catch (error) {
    console.error('获取学生列表失败:', error)
    ElMessage.error('获取学生列表失败')
  } finally {
    studentListLoading.value = false
  }
}

// 当角色选择为学生时，加载学生列表
watch(() => userForm.role, (newRole) => {
  if (newRole === 'student' && studentList.value.length === 0) {
    fetchStudentList()
  }
})

// 当对话框打开时，如果角色是学生且学生列表为空，则获取学生列表
watch(() => dialogVisible.value, (newVal) => {
  if (newVal && userForm.role === 'student' && studentList.value.length === 0) {
    fetchStudentList()
  }
})

// 表单校验规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 用户数据
const userList = ref([])

onMounted(() => {
  fetchData()
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await userService.getUsers({
      page: currentPage.value,
      limit: pageSize.value,
      username: searchForm.username || undefined,
      phone: searchForm.phone || undefined,
      status: searchForm.status || undefined
    })
    userList.value = response.data.data
    total.value = response.data.count || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置表单
const resetForm = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 刷新表格
const refreshTable = () => {
  fetchData()
}

// 多选变化
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增用户
const handleAdd = () => {
  dialogType.value = 'add'
  resetUserForm()
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  dialogType.value = 'edit'
  resetUserForm()
  Object.keys(userForm).forEach(key => {
    if (key !== 'password') {
      userForm[key] = row[key]
    }
  })
  dialogVisible.value = true
}

// 重置用户表单
const resetUserForm = () => {
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
  Object.assign(userForm, {
    id: '',
    username: '',
    name: '',
    password: '',
    phone: '',
    email: '',
    role: 'student',
    student_id: '',
    status: 1
  })
}

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 新增用户
          await userService.createUser(userForm)
          ElMessage.success('新增用户成功')
        } else {
          // 编辑用户
          await userService.updateUser(userForm.id, userForm)
          ElMessage.success('编辑用户成功')
        }
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        console.error('保存用户失败:', error)
        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))
      }
    } else {
      return false
    }
  })
}

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.deleteUser(row.id)
      ElMessage.success(`用户 ${row.username} 已删除`)
      fetchData()
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  const names = multipleSelection.value.map(item => item.username).join('、')
  const ids = multipleSelection.value.map(item => item.id)
  
  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.batchDeleteUsers(ids)
      ElMessage.success('批量删除成功')
      fetchData()
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {})
}

// 修改状态
const handleStatusChange = async (val, row) => {
  try {
    await userService.updateUserStatus(row.id, val)
    const status = val === 1 ? '启用' : '禁用'
    ElMessage.success(`已${status}用户 ${row.username}`)
  } catch (error) {
    console.error('修改状态失败:', error)
    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message))
    // 回滚状态
    row.status = val === 1 ? 0 : 1
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchData()
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchData()
}

// 导入相关方法
const handleImport = () => {
  importDialogVisible.value = true
  fileList.value = []
}

const downloadTemplate = () => {
  // 动态导入XLSX库
  import('xlsx').then(XLSX => {
    // 创建Excel模板数据
    const templateData = [
      {
        '用户名': 'zhangsan',
        '密码': '123456',
        '姓名': '张三',
        '角色': '学生',
        '邮箱': '<EMAIL>',
        '电话': '13800138000'
      }
    ]

    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '用户信息')

    // 下载文件
    XLSX.writeFile(wb, '用户导入模板.xlsx')
  }).catch(error => {
    console.error('XLSX库加载失败:', error)
    ElMessage.error('下载模板失败，请刷新页面重试')
  })
}

const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.ms-excel' ||
                 file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

  if (!isExcel) {
    ElMessage.error('请上传Excel格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }

  return true
}

const handleFileChange = (file, uploadFileList) => {
  fileList.value = uploadFileList
}

const submitImport = async () => {
  if (fileList.value.length === 0) {
    ElMessage.error('请选择要上传的文件')
    return
  }

  importLoading.value = true

  try {
    const formData = new FormData()
    const fileObject = fileList.value[0]
    const rawFile = fileObject.raw || fileObject

    formData.append('file', rawFile)

    const response = await userService.importUsers(formData)

    ElMessage.success(`导入成功！共导入 ${response.data.data.imported} 条记录`)
    importDialogVisible.value = false

    // 重新加载用户列表
    fetchData()

  } catch (error) {
    console.error('导入失败:', error)
    let errorMsg = '导入失败'

    if (error.response && error.response.data) {
      if (error.response.data.errors && error.response.data.errors.length > 0) {
        errorMsg += '：\n' + error.response.data.errors.join('\n')
      } else if (error.response.data.message) {
        errorMsg += '：' + error.response.data.message
      }
    }

    ElMessage.error(errorMsg)
  } finally {
    importLoading.value = false
  }
}
</script>

<style scoped>
.user-list-container {
  padding: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.student-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.student-school {
  color: #909399;
  font-size: 0.9em;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 
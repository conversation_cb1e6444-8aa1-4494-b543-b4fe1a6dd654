{"ast": null, "code": "import axios from 'axios';\nimport { onMounted } from 'vue';\nexport default {\n  name: 'App',\n  setup() {\n    onMounted(() => {\n      // 初始化检查 - 确保学生ID存在\n      checkStudentId();\n    });\n  }\n};\n\n// 检查并尝试获取学生ID\nfunction checkStudentId() {\n  const userId = localStorage.getItem('userId');\n  const userRole = localStorage.getItem('userRole');\n  if (userRole === 'student' && !localStorage.getItem('studentId') && userId) {\n    console.log('尝试获取学生ID...');\n\n    // 获取API URL\n    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n\n    // 获取token\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('未找到登录令牌，无法获取学生ID');\n      return;\n    }\n\n    // 设置请求头\n    const headers = {\n      'Authorization': `Bearer ${token}`\n    };\n\n    // 发送请求获取学生ID\n    axios.get(`${API_URL}/students/by-user/${userId}`, {\n      headers\n    }).then(response => {\n      if (response.data.success && response.data.data) {\n        localStorage.setItem('studentId', response.data.data.id);\n        console.log('成功获取并保存学生ID:', response.data.data.id);\n      } else {\n        console.error('无法获取学生ID');\n      }\n    }).catch(error => {\n      console.error('获取学生ID失败:', error);\n    });\n  }\n}", "map": {"version": 3, "names": ["axios", "onMounted", "name", "setup", "checkStudentId", "userId", "localStorage", "getItem", "userRole", "console", "log", "API_URL", "process", "env", "VUE_APP_API_URL", "token", "error", "headers", "get", "then", "response", "data", "success", "setItem", "id", "catch"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios'\nimport { onMounted } from 'vue'\n\nexport default {\n  name: 'App',\n  setup() {\n    onMounted(() => {\n      // 初始化检查 - 确保学生ID存在\n      checkStudentId()\n    })\n  }\n}\n\n// 检查并尝试获取学生ID\nfunction checkStudentId() {\n  const userId = localStorage.getItem('userId')\n  const userRole = localStorage.getItem('userRole')\n  \n  if (userRole === 'student' && !localStorage.getItem('studentId') && userId) {\n    console.log('尝试获取学生ID...')\n    \n    // 获取API URL\n    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\n    \n    // 获取token\n    const token = localStorage.getItem('token')\n    if (!token) {\n      console.error('未找到登录令牌，无法获取学生ID')\n      return\n    }\n    \n    // 设置请求头\n    const headers = {\n      'Authorization': `Bearer ${token}`\n    }\n    \n    // 发送请求获取学生ID\n    axios.get(`${API_URL}/students/by-user/${userId}`, { headers })\n      .then(response => {\n        if (response.data.success && response.data.data) {\n          localStorage.setItem('studentId', response.data.data.id)\n          console.log('成功获取并保存学生ID:', response.data.data.id)\n        } else {\n          console.error('无法获取学生ID')\n        }\n      })\n      .catch(error => {\n        console.error('获取学生ID失败:', error)\n      })\n  }\n}\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;\n}\n\n#app {\n  height: 100%;\n}\n\n.el-main {\n  padding: 0 !important;\n}\n\n/* 覆盖Element Plus默认样式，减少边距 */\n.el-card__body {\n  padding: 10px !important;\n}\n\n.el-form--inline .el-form-item {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n\n/* 设置表格最大高度，避免出现滚动条 */\n.el-table {\n  max-height: calc(100vh - 300px) !important;\n  overflow: hidden !important;\n}\n\n/* 确保表格内容适应容器 */\n.el-table__body-wrapper {\n  overflow: hidden !important;\n}\n\n/* 表格卡片样式 */\n.table-card {\n  overflow: hidden !important;\n}\n\n/* 表格卡片内容区域 */\n.table-card .el-card__body {\n  overflow: hidden !important;\n}\n</style>\n"], "mappings": "AAOA,OAAOA,KAAI,MAAO,OAAM;AACxB,SAASC,SAAQ,QAAS,KAAI;AAE9B,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,KAAKA,CAAA,EAAG;IACNF,SAAS,CAAC,MAAM;MACd;MACAG,cAAc,CAAC;IACjB,CAAC;EACH;AACF;;AAEA;AACA,SAASA,cAAcA,CAAA,EAAG;EACxB,MAAMC,MAAK,GAAIC,YAAY,CAACC,OAAO,CAAC,QAAQ;EAC5C,MAAMC,QAAO,GAAIF,YAAY,CAACC,OAAO,CAAC,UAAU;EAEhD,IAAIC,QAAO,KAAM,SAAQ,IAAK,CAACF,YAAY,CAACC,OAAO,CAAC,WAAW,KAAKF,MAAM,EAAE;IAC1EI,OAAO,CAACC,GAAG,CAAC,aAAa;;IAEzB;IACA,MAAMC,OAAM,GAAIC,OAAO,CAACC,GAAG,CAACC,eAAc,IAAK,2BAA0B;;IAEzE;IACA,MAAMC,KAAI,GAAIT,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAI,CAACQ,KAAK,EAAE;MACVN,OAAO,CAACO,KAAK,CAAC,kBAAkB;MAChC;IACF;;IAEA;IACA,MAAMC,OAAM,GAAI;MACd,eAAe,EAAE,UAAUF,KAAK;IAClC;;IAEA;IACAf,KAAK,CAACkB,GAAG,CAAC,GAAGP,OAAO,qBAAqBN,MAAM,EAAE,EAAE;MAAEY;IAAQ,CAAC,EAC3DE,IAAI,CAACC,QAAO,IAAK;MAChB,IAAIA,QAAQ,CAACC,IAAI,CAACC,OAAM,IAAKF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;QAC/Cf,YAAY,CAACiB,OAAO,CAAC,WAAW,EAAEH,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACG,EAAE;QACvDf,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEU,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACG,EAAE;MACnD,OAAO;QACLf,OAAO,CAACO,KAAK,CAAC,UAAU;MAC1B;IACF,CAAC,EACAS,KAAK,CAACT,KAAI,IAAK;MACdP,OAAO,CAACO,KAAK,CAAC,WAAW,EAAEA,KAAK;IAClC,CAAC;EACL;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, vShow as _vShow, withDirectives as _withDirectives, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, resolveDynamicComponent as _resolveDynamicComponent, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '../assets/logo.png';\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"logo\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  class: \"header-right\"\n};\nconst _hoisted_5 = {\n  class: \"user-info\"\n};\nconst _hoisted_6 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_User = _resolveComponent(\"User\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_el_sub_menu = _resolveComponent(\"el-sub-menu\");\n  const _component_Reading = _resolveComponent(\"Reading\");\n  const _component_DocumentChecked = _resolveComponent(\"DocumentChecked\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_UserFilled = _resolveComponent(\"UserFilled\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  const _component_el_aside = _resolveComponent(\"el-aside\");\n  const _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  const _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_CaretBottom = _resolveComponent(\"CaretBottom\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_container, {\n    class: \"layout-container\"\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 左侧菜单 \"), _createVNode(_component_el_aside, {\n      width: $setup.isCollapse ? '64px' : '220px',\n      class: \"aside\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[5] || (_cache[5] = _createElementVNode(\"img\", {\n        src: _imports_0,\n        alt: \"logo\"\n      }, null, -1 /* CACHED */)), _withDirectives(_createElementVNode(\"h1\", null, \"实习生学籍管理系统\", 512 /* NEED_PATCH */), [[_vShow, !$setup.isCollapse]])]), _createVNode(_component_el_scrollbar, null, {\n        default: _withCtx(() => [_createVNode(_component_el_menu, {\n          \"default-active\": $setup.activeMenu,\n          class: \"el-menu-vertical\",\n          collapse: $setup.isCollapse,\n          \"background-color\": \"#304156\",\n          \"text-color\": \"#bfcbd9\",\n          \"active-text-color\": \"#409EFF\",\n          router: \"\",\n          \"collapse-transition\": false\n        }, {\n          default: _withCtx(() => [!$setup.isStudent ? (_openBlock(), _createBlock(_component_el_sub_menu, {\n            key: 0,\n            index: \"/students\"\n          }, {\n            title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_User)]),\n              _: 1 /* STABLE */\n            }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"实习生管理\", -1 /* CACHED */))]),\n            default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n              index: \"/students/list\"\n            }, {\n              default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"实习生列表\")])),\n              _: 1 /* STABLE */,\n              __: [7]\n            })]),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_sub_menu, {\n            index: \"/courses\"\n          }, {\n            title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_Reading)]),\n              _: 1 /* STABLE */\n            }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"岗前培训\", -1 /* CACHED */))]),\n            default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n              index: \"/courses/list\"\n            }, {\n              default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"课程管理\")])),\n              _: 1 /* STABLE */,\n              __: [9]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_sub_menu, {\n            index: \"/exams\"\n          }, {\n            title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_DocumentChecked)]),\n              _: 1 /* STABLE */\n            }), _cache[10] || (_cache[10] = _createElementVNode(\"span\", null, \"考核管理\", -1 /* CACHED */))]),\n            default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n              index: \"/exams/list\"\n            }, {\n              default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"考试列表\")])),\n              _: 1 /* STABLE */,\n              __: [11]\n            }), $setup.isStudent ? (_openBlock(), _createBlock(_component_el_menu_item, {\n              key: 0,\n              index: \"/exams/my-results\"\n            }, {\n              default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"我的考试成绩\")])),\n              _: 1 /* STABLE */,\n              __: [12]\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          }), !$setup.isStudent ? (_openBlock(), _createBlock(_component_el_sub_menu, {\n            key: 1,\n            index: \"/rotations\"\n          }, {\n            title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_Refresh)]),\n              _: 1 /* STABLE */\n            }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"轮转管理\", -1 /* CACHED */))]),\n            default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n              index: \"/rotations/list\"\n            }, {\n              default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"轮转记录\")])),\n              _: 1 /* STABLE */,\n              __: [14]\n            }), _createVNode(_component_el_menu_item, {\n              index: \"/rotations/graduation\"\n            }, {\n              default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"结业考核\")])),\n              _: 1 /* STABLE */,\n              __: [15]\n            })]),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), !$setup.isStudent ? (_openBlock(), _createBlock(_component_el_sub_menu, {\n            key: 2,\n            index: \"/users\"\n          }, {\n            title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_UserFilled)]),\n              _: 1 /* STABLE */\n            }), _cache[16] || (_cache[16] = _createElementVNode(\"span\", null, \"用户管理\", -1 /* CACHED */))]),\n            default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n              index: \"/users/list\"\n            }, {\n              default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"用户列表\")])),\n              _: 1 /* STABLE */,\n              __: [17]\n            })]),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"default-active\", \"collapse\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"width\"]), _createCommentVNode(\" 右侧内容 \"), _createVNode(_component_el_container, {\n      class: \"main-container\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 顶部导航 \"), _createVNode(_component_el_header, {\n        class: \"header\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_icon, {\n          class: \"fold-icon\",\n          onClick: $setup.toggleSidebar\n        }, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.isCollapse ? 'Expand' : 'Fold')))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_breadcrumb, {\n          separator: \"/\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_breadcrumb_item, {\n            to: {\n              path: '/'\n            }\n          }, {\n            default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"首页\")])),\n            _: 1 /* STABLE */,\n            __: [18]\n          }), _createVNode(_component_el_breadcrumb_item, null, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentRoute), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_dropdown, {\n          trigger: \"click\"\n        }, {\n          dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, null, {\n            default: _withCtx(() => [_createVNode(_component_el_dropdown_item, {\n              onClick: $setup.showChangePassword\n            }, {\n              default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"修改密码\")])),\n              _: 1 /* STABLE */,\n              __: [19]\n            }), _createVNode(_component_el_dropdown_item, {\n              onClick: $setup.handleLogout\n            }, {\n              default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"退出登录\")])),\n              _: 1 /* STABLE */,\n              __: [20]\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_avatar, {\n            size: 30,\n            src: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\n          }), _createElementVNode(\"span\", null, _toDisplayString($setup.username), 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_CaretBottom)]),\n            _: 1 /* STABLE */\n          })])]),\n          _: 1 /* STABLE */\n        })])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 内容区域 \"), _createVNode(_component_el_main, {\n        class: \"main\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_router_view)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"ElDialog\"], {\n    title: \"修改密码\",\n    modelValue: $setup.changePasswordVisible,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.changePasswordVisible = $event),\n    width: \"400px\",\n    center: \"\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_6, [_createVNode($setup[\"ElButton\"], {\n      onClick: _cache[3] || (_cache[3] = $event => $setup.changePasswordVisible = false)\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }), _createVNode($setup[\"ElButton\"], {\n      type: \"primary\",\n      loading: $setup.changePasswordLoading,\n      onClick: $setup.handleChangePassword\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [22]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode($setup[\"ElForm\"], {\n      model: $setup.changePasswordForm,\n      rules: $setup.changePasswordRules,\n      ref: \"changePasswordFormRef\",\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ElFormItem\"], {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"ElInput\"], {\n          modelValue: $setup.changePasswordForm.phone,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.changePasswordForm.phone = $event),\n          placeholder: \"请输入注册时的手机号\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"ElFormItem\"], {\n        label: \"新密码\",\n        prop: \"newPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"ElInput\"], {\n          modelValue: $setup.changePasswordForm.newPassword,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.changePasswordForm.newPassword = $event),\n          type: \"password\",\n          placeholder: \"请输入新密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"ElFormItem\"], {\n        label: \"确认密码\",\n        prop: \"confirmPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"ElInput\"], {\n          modelValue: $setup.changePasswordForm.confirmPassword,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.changePasswordForm.confirmPassword = $event),\n          type: \"password\",\n          placeholder: \"请再次输入新密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_container", "_createCommentVNode", "_component_el_aside", "width", "$setup", "isCollapse", "_createElementVNode", "_hoisted_2", "src", "alt", "_component_el_scrollbar", "_component_el_menu", "activeMenu", "collapse", "router", "isStudent", "_createBlock", "_component_el_sub_menu", "index", "title", "_withCtx", "_component_el_icon", "_component_User", "_component_el_menu_item", "_cache", "_component_Reading", "_component_DocumentChecked", "_component_Refresh", "_component_UserFilled", "_component_el_header", "_hoisted_3", "onClick", "toggleSidebar", "_resolveDynamicComponent", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "to", "path", "currentRoute", "_hoisted_4", "_component_el_dropdown", "trigger", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "showChangePassword", "handleLogout", "_hoisted_5", "_component_el_avatar", "size", "_toDisplayString", "username", "_component_CaretBottom", "_component_el_main", "_component_router_view", "changePasswordVisible", "$event", "center", "footer", "_hoisted_6", "type", "loading", "changePasswordLoading", "handleChangePassword", "model", "changePasswordForm", "rules", "changePasswordRules", "ref", "label", "prop", "phone", "placeholder", "newPassword", "confirmPassword"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\layout\\AppLayout.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-container class=\"layout-container\">\r\n      <!-- 左侧菜单 -->\r\n      <el-aside :width=\"isCollapse ? '64px' : '220px'\" class=\"aside\">\r\n        <div class=\"logo\">\r\n          <img src=\"../assets/logo.png\" alt=\"logo\" />\r\n          <h1 v-show=\"!isCollapse\">实习生学籍管理系统</h1>\r\n        </div>\r\n        <el-scrollbar>\r\n          <el-menu\r\n            :default-active=\"activeMenu\"\r\n            class=\"el-menu-vertical\"\r\n            :collapse=\"isCollapse\"\r\n            background-color=\"#304156\"\r\n            text-color=\"#bfcbd9\"\r\n            active-text-color=\"#409EFF\"\r\n            router\r\n            :collapse-transition=\"false\"\r\n          >            \r\n            <el-sub-menu index=\"/students\" v-if=\"!isStudent\">\r\n              <template #title>\r\n                <el-icon><User /></el-icon>\r\n                <span>实习生管理</span>\r\n              </template>\r\n              <el-menu-item index=\"/students/list\">实习生列表</el-menu-item>\r\n            </el-sub-menu>\r\n            \r\n            <el-sub-menu index=\"/courses\">\r\n              <template #title>\r\n                <el-icon><Reading /></el-icon>\r\n                <span>岗前培训</span>\r\n              </template>\r\n              <el-menu-item index=\"/courses/list\">课程管理</el-menu-item>\r\n            </el-sub-menu>\r\n            \r\n            <el-sub-menu index=\"/exams\">\r\n              <template #title>\r\n                <el-icon><DocumentChecked /></el-icon>\r\n                <span>考核管理</span>\r\n              </template>\r\n              <el-menu-item index=\"/exams/list\">考试列表</el-menu-item>\r\n              <el-menu-item index=\"/exams/my-results\" v-if=\"isStudent\">我的考试成绩</el-menu-item>\r\n            </el-sub-menu>\r\n            \r\n            <el-sub-menu index=\"/rotations\" v-if=\"!isStudent\">\r\n              <template #title>\r\n                <el-icon><Refresh /></el-icon>\r\n                <span>轮转管理</span>\r\n              </template>\r\n              <el-menu-item index=\"/rotations/list\">轮转记录</el-menu-item>\r\n              <el-menu-item index=\"/rotations/graduation\">结业考核</el-menu-item>\r\n            </el-sub-menu>\r\n            \r\n            <el-sub-menu index=\"/users\" v-if=\"!isStudent\">\r\n              <template #title>\r\n                <el-icon><UserFilled /></el-icon>\r\n                <span>用户管理</span>\r\n              </template>\r\n              <el-menu-item index=\"/users/list\">用户列表</el-menu-item>\r\n            </el-sub-menu>\r\n          </el-menu>\r\n        </el-scrollbar>\r\n      </el-aside>\r\n      \r\n      <!-- 右侧内容 -->\r\n      <el-container class=\"main-container\">\r\n        <!-- 顶部导航 -->\r\n        <el-header class=\"header\">\r\n          <div class=\"header-left\">\r\n            <el-icon class=\"fold-icon\" @click=\"toggleSidebar\">\r\n              <component :is=\"isCollapse ? 'Expand' : 'Fold'\"></component>\r\n            </el-icon>\r\n            <el-breadcrumb separator=\"/\">\r\n              <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n              <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n          </div>\r\n          <div class=\"header-right\">\r\n            <el-dropdown trigger=\"click\">\r\n              <div class=\"user-info\">\r\n                <el-avatar :size=\"30\" src=\"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"></el-avatar>\r\n                <span>{{ username }}</span>\r\n                <el-icon><CaretBottom /></el-icon>\r\n              </div>\r\n              <template #dropdown>\r\n                <el-dropdown-menu>\r\n                  <el-dropdown-item @click=\"showChangePassword\">修改密码</el-dropdown-item>\r\n                  <el-dropdown-item @click=\"handleLogout\">退出登录</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </div>\r\n        </el-header>\r\n        \r\n        <!-- 内容区域 -->\r\n        <el-main class=\"main\">\r\n          <router-view />\r\n        </el-main>\r\n      </el-container>\r\n    </el-container>\r\n    <el-dialog\r\n  title=\"修改密码\"\r\n  v-model=\"changePasswordVisible\"\r\n  width=\"400px\"\r\n  center\r\n  destroy-on-close\r\n>\r\n  <el-form :model=\"changePasswordForm\" :rules=\"changePasswordRules\" ref=\"changePasswordFormRef\" label-width=\"80px\">\r\n    <el-form-item label=\"手机号\" prop=\"phone\">\r\n      <el-input v-model=\"changePasswordForm.phone\" placeholder=\"请输入注册时的手机号\"></el-input>\r\n    </el-form-item>\r\n    <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n      <el-input v-model=\"changePasswordForm.newPassword\" type=\"password\" placeholder=\"请输入新密码\" show-password></el-input>\r\n    </el-form-item>\r\n    <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n      <el-input v-model=\"changePasswordForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入新密码\" show-password></el-input>\r\n    </el-form-item>\r\n  </el-form>\r\n  <template #footer>\r\n    <span class=\"dialog-footer\">\r\n      <el-button @click=\"changePasswordVisible = false\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"changePasswordLoading\" @click=\"handleChangePassword\">确定</el-button>\r\n    </span>\r\n  </template>\r\n</el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, reactive } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { ElMessage, ElMessageBox, ElDialog, ElForm, ElFormItem, ElInput, ElButton } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nconst API_URL = 'http://***********:3000/api'\r\n\r\n// 修改密码相关\r\nconst changePasswordVisible = ref(false)\r\nconst changePasswordFormRef = ref(null)\r\nconst changePasswordLoading = ref(false)\r\n\r\nconst changePasswordForm = reactive({\r\n  phone: '',\r\n  newPassword: '',\r\n  confirmPassword: ''\r\n})\r\n\r\nconst validateConfirmPassword = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入密码'))\r\n  } else if (value !== changePasswordForm.newPassword) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\nconst changePasswordRules = {\r\n  phone: [\r\n    { required: true, message: '请输入手机号', trigger: 'blur' },\r\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n  ],\r\n  newPassword: [\r\n    { required: true, message: '请输入新密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  confirmPassword: [\r\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\r\n    { validator: validateConfirmPassword, trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showChangePassword = () => {\r\n  changePasswordVisible.value = true\r\n  // 重置表单\r\n  Object.assign(changePasswordForm, {\r\n    phone: '',\r\n    newPassword: '',\r\n    confirmPassword: ''\r\n  })\r\n}\r\n\r\nconst handleChangePassword = async () => {\r\n  if (!changePasswordFormRef.value) return\r\n  \r\n  try {\r\n    await changePasswordFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        changePasswordLoading.value = true\r\n        \r\n        try {\r\n          // 先验证手机号是否存在\r\n          const checkResponse = await axios.get(`${API_URL}/auth/check-phone/${changePasswordForm.phone}`)\r\n          \r\n          if (!checkResponse.data.success) {\r\n            ElMessage.error('该手机号未注册或不存在')\r\n            return\r\n          }\r\n          \r\n          // 修改密码\r\n          const response = await axios.post(`${API_URL}/auth/change-password-by-phone`, {\r\n            phone: changePasswordForm.phone,\r\n            newPassword: changePasswordForm.newPassword\r\n          })\r\n          \r\n          ElMessage.success('密码修改成功')\r\n          changePasswordVisible.value = false\r\n        } catch (error) {\r\n          console.error('修改密码失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '修改密码失败，请稍后重试')\r\n        } finally {\r\n          changePasswordLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    changePasswordLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst isCollapse = ref(false)\r\nconst username = ref(localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).name : '用户')\r\n\r\nconst activeMenu = computed(() => {\r\n  return route.path\r\n})\r\n\r\nconst currentRoute = computed(() => {\r\n  return route.meta.title || '实习生列表'\r\n})\r\n\r\nconst isStudent = computed(() => {\r\n  const userRole = localStorage.getItem('userRole')\r\n  return userRole === 'student'\r\n})\r\n\r\nconst toggleSidebar = () => {\r\n  isCollapse.value = !isCollapse.value\r\n}\r\n\r\nconst handleLogout = () => {\r\n  ElMessageBox.confirm('确定要退出登录吗?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => {\r\n    // 退出登录逻辑\r\n    localStorage.clear()\r\n    router.push('/login')\r\n    ElMessage.success('已退出登录')\r\n  }).catch(() => {})\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.layout-container {\r\n  height: 100%;\r\n}\r\n\r\n.aside {\r\n  background-color: #304156;\r\n  transition: width 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.logo {\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #2b3649;\r\n  color: #fff;\r\n}\r\n\r\n.logo img {\r\n  width: 30px;\r\n  height: 30px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo h1 {\r\n  display: inline-block;\r\n  margin: 0;\r\n  color: #fff;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n.el-menu-vertical:not(.el-menu--collapse) {\r\n  width: 220px;\r\n}\r\n\r\n.header {\r\n  background-color: #fff;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 10px;\r\n  height: 60px;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.fold-icon {\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n  margin-right: 10px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-icon {\r\n  font-size: 20px;\r\n  padding: 0 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  padding: 0 10px;\r\n}\r\n\r\n.user-info span {\r\n  margin: 0 5px;\r\n}\r\n\r\n.main {\r\n  padding: 0 !important;\r\n  background-color: #f0f2f5;\r\n}\r\n</style> \r\n<!-- 修改密码对话框 -->\r\n"], "mappings": ";OAMeA,UAAwB;;EALhCC,KAAK,EAAC;AAAe;;EAIfA,KAAK,EAAC;AAAM;;EAgEVA,KAAK,EAAC;AAAa;;EASnBA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAW;;EAwC1BA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;;;uBAvH7BC,mBAAA,CA6HM,OA7HNC,UA6HM,GA5HJC,YAAA,CAkGeC,uBAAA;IAlGDJ,KAAK,EAAC;EAAkB;sBACpC,MAAa,CAAbK,mBAAA,UAAa,EACbF,YAAA,CA2DWG,mBAAA;MA3DAC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAqBT,KAAK,EAAC;;wBACrD,MAGM,CAHNU,mBAAA,CAGM,OAHNC,UAGM,G,0BAFJD,mBAAA,CAA2C;QAAtCE,GAAwB,EAAxBb,UAAwB;QAACc,GAAG,EAAC;kDAClCH,mBAAA,CAAuC,YAAd,WAAS,0B,UAArBF,MAAA,CAAAC,UAAU,E,KAEzBN,YAAA,CAqDeW,uBAAA;0BApDb,MAmDU,CAnDVX,YAAA,CAmDUY,kBAAA;UAlDP,gBAAc,EAAEP,MAAA,CAAAQ,UAAU;UAC3BhB,KAAK,EAAC,kBAAkB;UACvBiB,QAAQ,EAAET,MAAA,CAAAC,UAAU;UACrB,kBAAgB,EAAC,SAAS;UAC1B,YAAU,EAAC,SAAS;UACpB,mBAAiB,EAAC,SAAS;UAC3BS,MAAM,EAAN,EAAM;UACL,qBAAmB,EAAE;;4BAFT,MAYpB,C,CAR6CV,MAAA,CAAAW,SAAS,I,cAA/CC,YAAA,CAMcC,sBAAA;;YANDC,KAAK,EAAC;;YACNC,KAAK,EAAAC,QAAA,CACd,MAA2B,CAA3BrB,YAAA,CAA2BsB,kBAAA;gCAAlB,MAAQ,CAARtB,YAAA,CAAQuB,eAAA,E;;0CACjBhB,mBAAA,CAAkB,cAAZ,OAAK,oB;8BAEb,MAAyD,CAAzDP,YAAA,CAAyDwB,uBAAA;cAA3CL,KAAK,EAAC;YAAgB;gCAAC,MAAKM,MAAA,QAAAA,MAAA,O,iBAAL,OAAK,E;;;;;mDAG5CzB,YAAA,CAMckB,sBAAA;YANDC,KAAK,EAAC;UAAU;YAChBC,KAAK,EAAAC,QAAA,CACd,MAA8B,CAA9BrB,YAAA,CAA8BsB,kBAAA;gCAArB,MAAW,CAAXtB,YAAA,CAAW0B,kBAAA,E;;0CACpBnB,mBAAA,CAAiB,cAAX,MAAI,oB;8BAEZ,MAAuD,CAAvDP,YAAA,CAAuDwB,uBAAA;cAAzCL,KAAK,EAAC;YAAe;gCAAC,MAAIM,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;;cAG1CzB,YAAA,CAOckB,sBAAA;YAPDC,KAAK,EAAC;UAAQ;YACdC,KAAK,EAAAC,QAAA,CACd,MAAsC,CAAtCrB,YAAA,CAAsCsB,kBAAA;gCAA7B,MAAmB,CAAnBtB,YAAA,CAAmB2B,0BAAA,E;;4CAC5BpB,mBAAA,CAAiB,cAAX,MAAI,oB;8BAEZ,MAAqD,CAArDP,YAAA,CAAqDwB,uBAAA;cAAvCL,KAAK,EAAC;YAAa;gCAAC,MAAIM,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;gBACQpB,MAAA,CAAAW,SAAS,I,cAAvDC,YAAA,CAA8EO,uBAAA;;cAAhEL,KAAK,EAAC;;gCAAqC,MAAMM,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;;;eAG1BpB,MAAA,CAAAW,SAAS,I,cAAhDC,YAAA,CAOcC,sBAAA;;YAPDC,KAAK,EAAC;;YACNC,KAAK,EAAAC,QAAA,CACd,MAA8B,CAA9BrB,YAAA,CAA8BsB,kBAAA;gCAArB,MAAW,CAAXtB,YAAA,CAAW4B,kBAAA,E;;4CACpBrB,mBAAA,CAAiB,cAAX,MAAI,oB;8BAEZ,MAAyD,CAAzDP,YAAA,CAAyDwB,uBAAA;cAA3CL,KAAK,EAAC;YAAiB;gCAAC,MAAIM,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;gBAC1CzB,YAAA,CAA+DwB,uBAAA;cAAjDL,KAAK,EAAC;YAAuB;gCAAC,MAAIM,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;oDAGfpB,MAAA,CAAAW,SAAS,I,cAA5CC,YAAA,CAMcC,sBAAA;;YANDC,KAAK,EAAC;;YACNC,KAAK,EAAAC,QAAA,CACd,MAAiC,CAAjCrB,YAAA,CAAiCsB,kBAAA;gCAAxB,MAAc,CAAdtB,YAAA,CAAc6B,qBAAA,E;;4CACvBtB,mBAAA,CAAiB,cAAX,MAAI,oB;8BAEZ,MAAqD,CAArDP,YAAA,CAAqDwB,uBAAA;cAAvCL,KAAK,EAAC;YAAa;gCAAC,MAAIM,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;;;;;;;kCAM9CvB,mBAAA,UAAa,EACbF,YAAA,CAiCeC,uBAAA;MAjCDJ,KAAK,EAAC;IAAgB;wBAClC,MAAa,CAAbK,mBAAA,UAAa,EACbF,YAAA,CAyBY8B,oBAAA;QAzBDjC,KAAK,EAAC;MAAQ;0BACvB,MAQM,CARNU,mBAAA,CAQM,OARNwB,UAQM,GAPJ/B,YAAA,CAEUsB,kBAAA;UAFDzB,KAAK,EAAC,WAAW;UAAEmC,OAAK,EAAE3B,MAAA,CAAA4B;;4BACjC,MAA4D,E,cAA5DhB,YAAA,CAA4DiB,wBAAA,CAA5C7B,MAAA,CAAAC,UAAU,wB;;YAE5BN,YAAA,CAGgBmC,wBAAA;UAHDC,SAAS,EAAC;QAAG;4BAC1B,MAA+D,CAA/DpC,YAAA,CAA+DqC,6BAAA;YAA1CC,EAAE,EAAE;cAAAC,IAAA;YAAA;UAAa;8BAAE,MAAEd,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;cAC1CzB,YAAA,CAA2DqC,6BAAA;8BAAvC,MAAkB,C,kCAAfhC,MAAA,CAAAmC,YAAY,iB;;;;cAGvCjC,mBAAA,CAcM,OAdNkC,UAcM,GAbJzC,YAAA,CAYc0C,sBAAA;UAZDC,OAAO,EAAC;QAAO;UAMfC,QAAQ,EAAAvB,QAAA,CACjB,MAGmB,CAHnBrB,YAAA,CAGmB6C,2BAAA;8BAFjB,MAAqE,CAArE7C,YAAA,CAAqE8C,2BAAA;cAAlDd,OAAK,EAAE3B,MAAA,CAAA0C;YAAkB;gCAAE,MAAItB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;gBAClDzB,YAAA,CAA+D8C,2BAAA;cAA5Cd,OAAK,EAAE3B,MAAA,CAAA2C;YAAY;gCAAE,MAAIvB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;;4BARhD,MAIM,CAJNlB,mBAAA,CAIM,OAJN0C,UAIM,GAHJjD,YAAA,CAA4GkD,oBAAA;YAAhGC,IAAI,EAAE,EAAE;YAAE1C,GAAG,EAAC;cAC1BF,mBAAA,CAA2B,cAAA6C,gBAAA,CAAlB/C,MAAA,CAAAgD,QAAQ,kBACjBrD,YAAA,CAAkCsB,kBAAA;8BAAzB,MAAe,CAAftB,YAAA,CAAesD,sBAAA,E;;;;;;UAYhCpD,mBAAA,UAAa,EACbF,YAAA,CAEUuD,kBAAA;QAFD1D,KAAK,EAAC;MAAM;0BACnB,MAAe,CAAfG,YAAA,CAAewD,sBAAA,E;;;;;;MAIrBxD,YAAA,CAwBQK,MAAA;IAvBVe,KAAK,EAAC,MAAM;gBACHf,MAAA,CAAAoD,qBAAqB;+DAArBpD,MAAA,CAAAoD,qBAAqB,GAAAC,MAAA;IAC9BtD,KAAK,EAAC,OAAO;IACbuD,MAAM,EAAN,EAAM;IACN,kBAAgB,EAAhB;;IAaWC,MAAM,EAAAvC,QAAA,CACf,MAGO,CAHPd,mBAAA,CAGO,QAHPsD,UAGO,GAFL7D,YAAA,CAAgEK,MAAA;MAApD2B,OAAK,EAAAP,MAAA,QAAAA,MAAA,MAAAiC,MAAA,IAAErD,MAAA,CAAAoD,qBAAqB;;wBAAU,MAAEhC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACpDzB,YAAA,CAAuGK,MAAA;MAA5FyD,IAAI,EAAC,SAAS;MAAEC,OAAO,EAAE1D,MAAA,CAAA2D,qBAAqB;MAAGhC,OAAK,EAAE3B,MAAA,CAAA4D;;wBAAsB,MAAExC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAd/F,MAUU,CAVVzB,YAAA,CAUUK,MAAA;MAVA6D,KAAK,EAAE7D,MAAA,CAAA8D,kBAAkB;MAAGC,KAAK,EAAE/D,MAAA,CAAAgE,mBAAmB;MAAEC,GAAG,EAAC,uBAAuB;MAAC,aAAW,EAAC;;wBACxG,MAEe,CAFftE,YAAA,CAEeK,MAAA;QAFDkE,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;;0BAC7B,MAAiF,CAAjFxE,YAAA,CAAiFK,MAAA;sBAA9DA,MAAA,CAAA8D,kBAAkB,CAACM,KAAK;qEAAxBpE,MAAA,CAAA8D,kBAAkB,CAACM,KAAK,GAAAf,MAAA;UAAEgB,WAAW,EAAC;;;UAE3D1E,YAAA,CAEeK,MAAA;QAFDkE,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;;0BAC7B,MAAiH,CAAjHxE,YAAA,CAAiHK,MAAA;sBAA9FA,MAAA,CAAA8D,kBAAkB,CAACQ,WAAW;qEAA9BtE,MAAA,CAAA8D,kBAAkB,CAACQ,WAAW,GAAAjB,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACY,WAAW,EAAC,QAAQ;UAAC,eAAa,EAAb;;;UAE1F1E,YAAA,CAEeK,MAAA;QAFDkE,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAAuH,CAAvHxE,YAAA,CAAuHK,MAAA;sBAApGA,MAAA,CAAA8D,kBAAkB,CAACS,eAAe;qEAAlCvE,MAAA,CAAA8D,kBAAkB,CAACS,eAAe,GAAAlB,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACY,WAAW,EAAC,UAAU;UAAC,eAAa,EAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
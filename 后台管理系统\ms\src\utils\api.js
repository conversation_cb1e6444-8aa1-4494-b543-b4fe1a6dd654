import axios from 'axios'
import store from '../store'
import router from '../router'
import { ElMessage } from 'element-plus'

// 配置axios默认值
const api = axios.create({
  baseURL: 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证头
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理常见错误
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    const { response } = error
    if (response) {
      // 处理不同的错误状态码
      switch (response.status) {
        case 401: // 未授权
          // 清除本地存储的用户信息和token
          localStorage.removeItem('token')
          localStorage.removeItem('userId')
          localStorage.removeItem('userRole')
          localStorage.removeItem('studentId')
          
          // 清除Vuex中的状态
          store.dispatch('logout')
          
          // 判断当前是否在登录页
          if (router.currentRoute.value.path !== '/login') {
            router.push('/login')
            // 显示错误提示
            ElMessage({
              message: '登录已过期，请重新登录',
              type: 'error',
              duration: 3000
            })
          }
          break
          
        case 403: // 禁止访问
          ElMessage({
            message: '您没有权限执行此操作',
            type: 'error',
            duration: 3000
          })
          break
          
        default:
          // 处理其他错误
          ElMessage({
            message: response.data.message || '请求失败',
            type: 'error',
            duration: 3000
          })
      }
    } else {
      // 处理网络错误
      ElMessage({
        message: '网络错误，请检查您的网络连接',
        type: 'error',
        duration: 3000
      })
    }
    
    return Promise.reject(error)
  }
)

// 添加刷新token的功能（可选，如果后端支持）
export const refreshToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken')
    if (!refreshToken) {
      return null
    }
    
    const response = await axios.post('/api/auth/refresh-token', {
      refreshToken
    })
    
    const { token } = response.data
    localStorage.setItem('token', token)
    return token
  } catch (error) {
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    return null
  }
}

export default api 
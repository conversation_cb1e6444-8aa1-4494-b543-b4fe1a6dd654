{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_router_view)]);\n}", "map": {"version": 3, "names": ["id", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_router_view"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios'\nimport { onMounted } from 'vue'\n\nexport default {\n  name: 'App',\n  setup() {\n    onMounted(() => {\n      // 初始化检查 - 确保学生ID存在\n      checkStudentId()\n    })\n  }\n}\n\n// 检查并尝试获取学生ID\nfunction checkStudentId() {\n  const userId = localStorage.getItem('userId')\n  const userRole = localStorage.getItem('userRole')\n  \n  if (userRole === 'student' && !localStorage.getItem('studentId') && userId) {\n    console.log('尝试获取学生ID...')\n    \n    // 获取API URL\n    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\n    \n    // 获取token\n    const token = localStorage.getItem('token')\n    if (!token) {\n      console.error('未找到登录令牌，无法获取学生ID')\n      return\n    }\n    \n    // 设置请求头\n    const headers = {\n      'Authorization': `Bearer ${token}`\n    }\n    \n    // 发送请求获取学生ID\n    axios.get(`${API_URL}/students/by-user/${userId}`, { headers })\n      .then(response => {\n        if (response.data.success && response.data.data) {\n          localStorage.setItem('studentId', response.data.data.id)\n          console.log('成功获取并保存学生ID:', response.data.data.id)\n        } else {\n          console.error('无法获取学生ID')\n        }\n      })\n      .catch(error => {\n        console.error('获取学生ID失败:', error)\n      })\n  }\n}\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;\n}\n\n#app {\n  height: 100%;\n}\n\n.el-main {\n  padding: 0 !important;\n}\n\n/* 覆盖Element Plus默认样式，减少边距 */\n.el-card__body {\n  padding: 10px !important;\n}\n\n.el-form--inline .el-form-item {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n\n/* 设置表格最大高度，避免出现滚动条 */\n.el-table {\n  max-height: calc(100vh - 300px) !important;\n  overflow: hidden !important;\n}\n\n/* 确保表格内容适应容器 */\n.el-table__body-wrapper {\n  overflow: hidden !important;\n}\n\n/* 表格卡片样式 */\n.table-card {\n  overflow: hidden !important;\n}\n\n/* 表格卡片内容区域 */\n.table-card .el-card__body {\n  overflow: hidden !important;\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;;uBAAbC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAcC,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
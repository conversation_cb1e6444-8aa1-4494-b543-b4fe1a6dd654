const Student = require('../models/studentModel');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const util = require('util');
const unlinkFile = util.promisify(fs.unlink);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/students/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

// 文件过滤，只允许 Excel 文件
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    const ext = path.extname(file.originalname).toLowerCase();
    if (ext === '.xls' || ext === '.xlsx') {
      cb(null, true);
    } else {
      cb(new Error('文件扩展名必须是 .xls 或 .xlsx'), false);
    }
  } else {
    cb(new Error('仅支持 Excel 格式的文件'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

// 获取所有学生信息
exports.getAllStudents = async (req, res) => {
  try {
    const students = await Student.findAll();
    res.status(200).json({
      success: true,
      data: students
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取学生列表失败',
      error: error.message
    });
  }
};

// 获取单个学生信息
exports.getStudentById = async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生信息'
      });
    }
    
    res.status(200).json({
      success: true,
      data: student
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取学生信息失败',
      error: error.message
    });
  }
};

// 创建学生信息
exports.createStudent = async (req, res) => {
  try {
    // 验证必要字段
    const requiredFields = [
      'name', 'gender', 'school', 'major', 
      'start_date', 'end_date', 'phone', 
      'school_contact_name', 'school_contact_phone',
      'family_contact_name', 'family_contact_phone',
      'home_address', 'current_address'
    ];
    
    for (const field of requiredFields) {
      if (!req.body[field]) {
        return res.status(400).json({
          success: false,
          message: `请提供 ${field} 字段`
        });
      }
    }
    
    const newStudentId = await Student.create(req.body);
    
    const student = await Student.findById(newStudentId);
    
    res.status(201).json({
      success: true,
      message: '学生信息创建成功',
      data: student
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建学生信息失败',
      error: error.message
    });
  }
};

// 更新学生信息
exports.updateStudent = async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生信息'
      });
    }
    
    const updated = await Student.update(req.params.id, req.body);
    
    if (updated) {
      const updatedStudent = await Student.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '学生信息更新成功',
        data: updatedStudent
      });
    } else {
      res.status(500).json({
        success: false,
        message: '学生信息更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新学生信息失败',
      error: error.message
    });
  }
};

// 删除学生信息
exports.deleteStudent = async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生信息'
      });
    }
    
    const deleted = await Student.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '学生信息删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '学生信息删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除学生信息失败',
      error: error.message
    });
  }
};

// 搜索学生
exports.searchStudents = async (req, res) => {
  try {
    const { name, school } = req.query;
    
    if (!name && !school) {
      return res.status(400).json({
        success: false,
        message: '请提供搜索条件：姓名或学校'
      });
    }
    
    const students = await Student.search(name, school);
    
    res.status(200).json({
      success: true,
      count: students.length,
      data: students
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '搜索学生失败',
      error: error.message
    });
  }
}; 

// 根据用户ID查找学生
exports.getStudentByUserId = async (req, res) => {
  try {
    const userId = req.params.userId;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '请提供用户ID'
      });
    }
    
    const student = await Student.findByUserId(userId);
    
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户关联的学生信息'
      });
    }
    
    res.status(200).json({
      success: true,
      data: student
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '根据用户ID查找学生失败',
      error: error.message
    });
  }
};

// 批量导入学生信息
exports.importStudents = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      });
    }

    // 动态导入xlsx库
    let XLSX;
    try {
      XLSX = require('xlsx');
    } catch (error) {
      await unlinkFile(req.file.path);
      return res.status(500).json({
        success: false,
        message: '服务器缺少Excel处理库，请联系管理员安装xlsx依赖'
      });
    }

    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(req.file.path);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表转换为JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // 删除上传的文件
      await unlinkFile(req.file.path);

      if (jsonData.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Excel文件中没有数据'
        });
      }

      // 验证和转换数据
      const validStudents = [];
      const errors = [];

      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];
        const rowNum = i + 2; // Excel行号从2开始（第1行是标题）

        try {
          // 验证必填字段
          const requiredFields = {
            '姓名': 'name',
            '性别': 'gender',
            '学校': 'school',
            '专业': 'major',
            '实习开始时间': 'start_date',
            '实习结束时间': 'end_date',
            '联系方式': 'phone',
            '学校紧急联系人': 'school_contact_name',
            '学校紧急联系人电话': 'school_contact_phone',
            '家庭紧急联系人': 'family_contact_name',
            '家庭紧急联系人电话': 'family_contact_phone',
            '家庭住址': 'home_address',
            '现住址': 'current_address'
          };

          const studentData = {};

          for (const [chineseField, englishField] of Object.entries(requiredFields)) {
            if (!row[chineseField] || row[chineseField].toString().trim() === '') {
              throw new Error(`第${rowNum}行缺少必填字段：${chineseField}`);
            }
            studentData[englishField] = row[chineseField].toString().trim();
          }

          // 验证性别
          if (!['男', '女'].includes(studentData.gender)) {
            throw new Error(`第${rowNum}行性别必须是"男"或"女"`);
          }

          // 处理日期格式
          studentData.start_date = formatExcelDate(row['实习开始时间']);
          studentData.end_date = formatExcelDate(row['实习结束时间']);

          // 处理可选字段
          studentData.notes = row['备注'] ? row['备注'].toString().trim() : '';
          studentData.is_leader = row['是否组长'] === '是' || row['是否组长'] === '1' || row['是否组长'] === 1 ? 1 : 0;
          studentData.is_living_outside = row['是否外宿'] === '是' || row['是否外宿'] === '1' || row['是否外宿'] === 1 ? 1 : 0;
          studentData.roommate_contact = row['同宿紧急联系人'] ? row['同宿紧急联系人'].toString().trim() : '';

          validStudents.push(studentData);

        } catch (error) {
          errors.push(error.message);
        }
      }

      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors
        });
      }

      // 批量插入数据库
      const insertedStudents = [];
      const insertErrors = [];

      for (let i = 0; i < validStudents.length; i++) {
        try {
          const studentId = await Student.create(validStudents[i]);
          const student = await Student.findById(studentId);
          insertedStudents.push(student);
        } catch (error) {
          insertErrors.push(`第${i + 2}行插入失败：${error.message}`);
        }
      }

      res.status(201).json({
        success: true,
        message: `成功导入 ${insertedStudents.length} 条学生记录`,
        data: {
          imported: insertedStudents.length,
          total: jsonData.length,
          errors: insertErrors
        }
      });

    } catch (error) {
      // 确保删除临时文件
      try {
        await unlinkFile(req.file.path);
      } catch (unlinkError) {
        console.error('删除临时文件失败:', unlinkError);
      }

      throw error;
    }

  } catch (error) {
    console.error('导入学生信息失败:', error);
    res.status(500).json({
      success: false,
      message: '导入学生信息失败',
      error: error.message
    });
  }
};

// 格式化Excel日期
function formatExcelDate(dateValue) {
  if (!dateValue) return null;

  // 如果已经是字符串格式的日期
  if (typeof dateValue === 'string') {
    const date = new Date(dateValue);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }
  }

  // 如果是Excel的数字日期格式
  if (typeof dateValue === 'number') {
    const date = new Date((dateValue - 25569) * 86400 * 1000);
    return date.toISOString().split('T')[0];
  }

  // 如果是Date对象
  if (dateValue instanceof Date) {
    return dateValue.toISOString().split('T')[0];
  }

  return null;
}
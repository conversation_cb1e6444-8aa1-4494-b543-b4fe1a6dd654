{"ast": null, "code": "import { defineComponent, useAttrs, useSlots, computed, shallowRef, ref, watch, nextTick, onMounted, toRef, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, createCommentVNode, Fragment, renderSlot, createElementVNode, createBlock, withCtx, resolveDynamicComponent, mergeProps, withModifiers, createVNode, toDisplayString } from 'vue';\nimport { useResizeObserver, isClient } from '@vueuse/core';\nimport { isNil } from 'lodash-unified';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { View, Hide, CircleClose } from '@element-plus/icons-vue';\nimport { calcTextareaHeight } from './utils.mjs';\nimport { inputProps, inputEmits } from './input.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useAttrs as useAttrs$1 } from '../../../hooks/use-attrs/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { useFormSize, useFormDisabled } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useFocusController } from '../../../hooks/use-focus-controller/index.mjs';\nimport { ValidateComponentsMap } from '../../../utils/vue/icon.mjs';\nimport { useComposition } from '../../../hooks/use-composition/index.mjs';\nimport { UPDATE_MODEL_EVENT, INPUT_EVENT, CHANGE_EVENT } from '../../../constants/event.mjs';\nimport { useCursor } from '../../../hooks/use-cursor/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { NOOP, isObject } from '@vue/shared';\nconst COMPONENT_NAME = \"ElInput\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME,\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: inputProps,\n  emits: inputEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const rawAttrs = useAttrs();\n    const attrs = useAttrs$1();\n    const slots = useSlots();\n    const containerKls = computed(() => [props.type === \"textarea\" ? nsTextarea.b() : nsInput.b(), nsInput.m(inputSize.value), nsInput.is(\"disabled\", inputDisabled.value), nsInput.is(\"exceed\", inputExceed.value), {\n      [nsInput.b(\"group\")]: slots.prepend || slots.append,\n      [nsInput.m(\"prefix\")]: slots.prefix || props.prefixIcon,\n      [nsInput.m(\"suffix\")]: slots.suffix || props.suffixIcon || props.clearable || props.showPassword,\n      [nsInput.bm(\"suffix\", \"password-clear\")]: showClear.value && showPwdVisible.value,\n      [nsInput.b(\"hidden\")]: props.type === \"hidden\"\n    }, rawAttrs.class]);\n    const wrapperKls = computed(() => [nsInput.e(\"wrapper\"), nsInput.is(\"focus\", isFocused.value)]);\n    const {\n      form: elForm,\n      formItem: elFormItem\n    } = useFormItem();\n    const {\n      inputId\n    } = useFormItemInputId(props, {\n      formItemContext: elFormItem\n    });\n    const inputSize = useFormSize();\n    const inputDisabled = useFormDisabled();\n    const nsInput = useNamespace(\"input\");\n    const nsTextarea = useNamespace(\"textarea\");\n    const input = shallowRef();\n    const textarea = shallowRef();\n    const hovering = ref(false);\n    const passwordVisible = ref(false);\n    const countStyle = ref();\n    const textareaCalcStyle = shallowRef(props.inputStyle);\n    const _ref = computed(() => input.value || textarea.value);\n    const {\n      wrapperRef,\n      isFocused,\n      handleFocus,\n      handleBlur\n    } = useFocusController(_ref, {\n      disabled: inputDisabled,\n      afterBlur() {\n        var _a;\n        if (props.validateEvent) {\n          (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"blur\").catch(err => debugWarn(err));\n        }\n      }\n    });\n    const needStatusIcon = computed(() => {\n      var _a;\n      return (_a = elForm == null ? void 0 : elForm.statusIcon) != null ? _a : false;\n    });\n    const validateState = computed(() => (elFormItem == null ? void 0 : elFormItem.validateState) || \"\");\n    const validateIcon = computed(() => validateState.value && ValidateComponentsMap[validateState.value]);\n    const passwordIcon = computed(() => passwordVisible.value ? View : Hide);\n    const containerStyle = computed(() => [rawAttrs.style]);\n    const textareaStyle = computed(() => [props.inputStyle, textareaCalcStyle.value, {\n      resize: props.resize\n    }]);\n    const nativeInputValue = computed(() => isNil(props.modelValue) ? \"\" : String(props.modelValue));\n    const showClear = computed(() => props.clearable && !inputDisabled.value && !props.readonly && !!nativeInputValue.value && (isFocused.value || hovering.value));\n    const showPwdVisible = computed(() => props.showPassword && !inputDisabled.value && !!nativeInputValue.value);\n    const isWordLimitVisible = computed(() => props.showWordLimit && !!props.maxlength && (props.type === \"text\" || props.type === \"textarea\") && !inputDisabled.value && !props.readonly && !props.showPassword);\n    const textLength = computed(() => nativeInputValue.value.length);\n    const inputExceed = computed(() => !!isWordLimitVisible.value && textLength.value > Number(props.maxlength));\n    const suffixVisible = computed(() => !!slots.suffix || !!props.suffixIcon || showClear.value || props.showPassword || isWordLimitVisible.value || !!validateState.value && needStatusIcon.value);\n    const [recordCursor, setCursor] = useCursor(input);\n    useResizeObserver(textarea, entries => {\n      onceInitSizeTextarea();\n      if (!isWordLimitVisible.value || props.resize !== \"both\") return;\n      const entry = entries[0];\n      const {\n        width\n      } = entry.contentRect;\n      countStyle.value = {\n        right: `calc(100% - ${width + 15 + 6}px)`\n      };\n    });\n    const resizeTextarea = () => {\n      const {\n        type,\n        autosize\n      } = props;\n      if (!isClient || type !== \"textarea\" || !textarea.value) return;\n      if (autosize) {\n        const minRows = isObject(autosize) ? autosize.minRows : void 0;\n        const maxRows = isObject(autosize) ? autosize.maxRows : void 0;\n        const textareaStyle2 = calcTextareaHeight(textarea.value, minRows, maxRows);\n        textareaCalcStyle.value = {\n          overflowY: \"hidden\",\n          ...textareaStyle2\n        };\n        nextTick(() => {\n          textarea.value.offsetHeight;\n          textareaCalcStyle.value = textareaStyle2;\n        });\n      } else {\n        textareaCalcStyle.value = {\n          minHeight: calcTextareaHeight(textarea.value).minHeight\n        };\n      }\n    };\n    const createOnceInitResize = resizeTextarea2 => {\n      let isInit = false;\n      return () => {\n        var _a;\n        if (isInit || !props.autosize) return;\n        const isElHidden = ((_a = textarea.value) == null ? void 0 : _a.offsetParent) === null;\n        if (!isElHidden) {\n          resizeTextarea2();\n          isInit = true;\n        }\n      };\n    };\n    const onceInitSizeTextarea = createOnceInitResize(resizeTextarea);\n    const setNativeInputValue = () => {\n      const input2 = _ref.value;\n      const formatterValue = props.formatter ? props.formatter(nativeInputValue.value) : nativeInputValue.value;\n      if (!input2 || input2.value === formatterValue) return;\n      input2.value = formatterValue;\n    };\n    const handleInput = async event => {\n      recordCursor();\n      let {\n        value\n      } = event.target;\n      if (props.formatter && props.parser) {\n        value = props.parser(value);\n      }\n      if (isComposing.value) return;\n      if (value === nativeInputValue.value) {\n        setNativeInputValue();\n        return;\n      }\n      emit(UPDATE_MODEL_EVENT, value);\n      emit(INPUT_EVENT, value);\n      await nextTick();\n      setNativeInputValue();\n      setCursor();\n    };\n    const handleChange = event => {\n      let {\n        value\n      } = event.target;\n      if (props.formatter && props.parser) {\n        value = props.parser(value);\n      }\n      emit(CHANGE_EVENT, value);\n    };\n    const {\n      isComposing,\n      handleCompositionStart,\n      handleCompositionUpdate,\n      handleCompositionEnd\n    } = useComposition({\n      emit,\n      afterComposition: handleInput\n    });\n    const handlePasswordVisible = () => {\n      recordCursor();\n      passwordVisible.value = !passwordVisible.value;\n      setTimeout(setCursor);\n    };\n    const focus = () => {\n      var _a;\n      return (_a = _ref.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a;\n      return (_a = _ref.value) == null ? void 0 : _a.blur();\n    };\n    const handleMouseLeave = evt => {\n      hovering.value = false;\n      emit(\"mouseleave\", evt);\n    };\n    const handleMouseEnter = evt => {\n      hovering.value = true;\n      emit(\"mouseenter\", evt);\n    };\n    const handleKeydown = evt => {\n      emit(\"keydown\", evt);\n    };\n    const select = () => {\n      var _a;\n      (_a = _ref.value) == null ? void 0 : _a.select();\n    };\n    const clear = () => {\n      emit(UPDATE_MODEL_EVENT, \"\");\n      emit(CHANGE_EVENT, \"\");\n      emit(\"clear\");\n      emit(INPUT_EVENT, \"\");\n    };\n    watch(() => props.modelValue, () => {\n      var _a;\n      nextTick(() => resizeTextarea());\n      if (props.validateEvent) {\n        (_a = elFormItem == null ? void 0 : elFormItem.validate) == null ? void 0 : _a.call(elFormItem, \"change\").catch(err => debugWarn(err));\n      }\n    });\n    watch(nativeInputValue, () => setNativeInputValue());\n    watch(() => props.type, async () => {\n      await nextTick();\n      setNativeInputValue();\n      resizeTextarea();\n    });\n    onMounted(() => {\n      if (!props.formatter && props.parser) {\n        debugWarn(COMPONENT_NAME, \"If you set the parser, you also need to set the formatter.\");\n      }\n      setNativeInputValue();\n      nextTick(resizeTextarea);\n    });\n    expose({\n      input,\n      textarea,\n      ref: _ref,\n      textareaStyle,\n      autosize: toRef(props, \"autosize\"),\n      isComposing,\n      focus,\n      blur,\n      select,\n      clear,\n      resizeTextarea\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(containerKls), {\n          [unref(nsInput).bm(\"group\", \"append\")]: _ctx.$slots.append,\n          [unref(nsInput).bm(\"group\", \"prepend\")]: _ctx.$slots.prepend\n        }]),\n        style: normalizeStyle(unref(containerStyle)),\n        onMouseenter: handleMouseEnter,\n        onMouseleave: handleMouseLeave\n      }, [createCommentVNode(\" input \"), _ctx.type !== \"textarea\" ? (openBlock(), createElementBlock(Fragment, {\n        key: 0\n      }, [createCommentVNode(\" prepend slot \"), _ctx.$slots.prepend ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(nsInput).be(\"group\", \"prepend\"))\n      }, [renderSlot(_ctx.$slots, \"prepend\")], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass(unref(wrapperKls))\n      }, [createCommentVNode(\" prefix slot \"), _ctx.$slots.prefix || _ctx.prefixIcon ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        class: normalizeClass(unref(nsInput).e(\"prefix\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(nsInput).e(\"prefix-inner\"))\n      }, [renderSlot(_ctx.$slots, \"prefix\"), _ctx.prefixIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(nsInput).e(\"icon\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.prefixIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"input\", mergeProps({\n        id: unref(inputId),\n        ref_key: \"input\",\n        ref: input,\n        class: unref(nsInput).e(\"inner\")\n      }, unref(attrs), {\n        name: _ctx.name,\n        minlength: _ctx.minlength,\n        maxlength: _ctx.maxlength,\n        type: _ctx.showPassword ? passwordVisible.value ? \"text\" : \"password\" : _ctx.type,\n        disabled: unref(inputDisabled),\n        readonly: _ctx.readonly,\n        autocomplete: _ctx.autocomplete,\n        tabindex: _ctx.tabindex,\n        \"aria-label\": _ctx.ariaLabel,\n        placeholder: _ctx.placeholder,\n        style: _ctx.inputStyle,\n        form: _ctx.form,\n        autofocus: _ctx.autofocus,\n        role: _ctx.containerRole,\n        inputmode: _ctx.inputmode,\n        onCompositionstart: unref(handleCompositionStart),\n        onCompositionupdate: unref(handleCompositionUpdate),\n        onCompositionend: unref(handleCompositionEnd),\n        onInput: handleInput,\n        onChange: handleChange,\n        onKeydown: handleKeydown\n      }), null, 16, [\"id\", \"name\", \"minlength\", \"maxlength\", \"type\", \"disabled\", \"readonly\", \"autocomplete\", \"tabindex\", \"aria-label\", \"placeholder\", \"form\", \"autofocus\", \"role\", \"inputmode\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\"]), createCommentVNode(\" suffix slot \"), unref(suffixVisible) ? (openBlock(), createElementBlock(\"span\", {\n        key: 1,\n        class: normalizeClass(unref(nsInput).e(\"suffix\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(nsInput).e(\"suffix-inner\"))\n      }, [!unref(showClear) || !unref(showPwdVisible) || !unref(isWordLimitVisible) ? (openBlock(), createElementBlock(Fragment, {\n        key: 0\n      }, [renderSlot(_ctx.$slots, \"suffix\"), _ctx.suffixIcon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(nsInput).e(\"icon\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.suffixIcon)))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 64)) : createCommentVNode(\"v-if\", true), unref(showClear) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"clear\")]),\n        onMousedown: withModifiers(unref(NOOP), [\"prevent\"]),\n        onClick: clear\n      }, {\n        default: withCtx(() => [createVNode(unref(CircleClose))]),\n        _: 1\n      }, 8, [\"class\", \"onMousedown\"])) : createCommentVNode(\"v-if\", true), unref(showPwdVisible) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 2,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"password\")]),\n        onClick: handlePasswordVisible\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(passwordIcon))))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true), unref(isWordLimitVisible) ? (openBlock(), createElementBlock(\"span\", {\n        key: 3,\n        class: normalizeClass(unref(nsInput).e(\"count\"))\n      }, [createElementVNode(\"span\", {\n        class: normalizeClass(unref(nsInput).e(\"count-inner\"))\n      }, toDisplayString(unref(textLength)) + \" / \" + toDisplayString(_ctx.maxlength), 3)], 2)) : createCommentVNode(\"v-if\", true), unref(validateState) && unref(validateIcon) && unref(needStatusIcon) ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 4,\n        class: normalizeClass([unref(nsInput).e(\"icon\"), unref(nsInput).e(\"validateIcon\"), unref(nsInput).is(\"loading\", unref(validateState) === \"validating\")])\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(validateIcon))))]),\n        _: 1\n      }, 8, [\"class\"])) : createCommentVNode(\"v-if\", true)], 2)], 2)) : createCommentVNode(\"v-if\", true)], 2), createCommentVNode(\" append slot \"), _ctx.$slots.append ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(nsInput).be(\"group\", \"append\"))\n      }, [renderSlot(_ctx.$slots, \"append\")], 2)) : createCommentVNode(\"v-if\", true)], 64)) : (openBlock(), createElementBlock(Fragment, {\n        key: 1\n      }, [createCommentVNode(\" textarea \"), createElementVNode(\"textarea\", mergeProps({\n        id: unref(inputId),\n        ref_key: \"textarea\",\n        ref: textarea,\n        class: [unref(nsTextarea).e(\"inner\"), unref(nsInput).is(\"focus\", unref(isFocused))]\n      }, unref(attrs), {\n        minlength: _ctx.minlength,\n        maxlength: _ctx.maxlength,\n        tabindex: _ctx.tabindex,\n        disabled: unref(inputDisabled),\n        readonly: _ctx.readonly,\n        autocomplete: _ctx.autocomplete,\n        style: unref(textareaStyle),\n        \"aria-label\": _ctx.ariaLabel,\n        placeholder: _ctx.placeholder,\n        form: _ctx.form,\n        autofocus: _ctx.autofocus,\n        rows: _ctx.rows,\n        role: _ctx.containerRole,\n        onCompositionstart: unref(handleCompositionStart),\n        onCompositionupdate: unref(handleCompositionUpdate),\n        onCompositionend: unref(handleCompositionEnd),\n        onInput: handleInput,\n        onFocus: unref(handleFocus),\n        onBlur: unref(handleBlur),\n        onChange: handleChange,\n        onKeydown: handleKeydown\n      }), null, 16, [\"id\", \"minlength\", \"maxlength\", \"tabindex\", \"disabled\", \"readonly\", \"autocomplete\", \"aria-label\", \"placeholder\", \"form\", \"autofocus\", \"rows\", \"role\", \"onCompositionstart\", \"onCompositionupdate\", \"onCompositionend\", \"onFocus\", \"onBlur\"]), unref(isWordLimitVisible) ? (openBlock(), createElementBlock(\"span\", {\n        key: 0,\n        style: normalizeStyle(countStyle.value),\n        class: normalizeClass(unref(nsInput).e(\"count\"))\n      }, toDisplayString(unref(textLength)) + \" / \" + toDisplayString(_ctx.maxlength), 7)) : createCommentVNode(\"v-if\", true)], 64))], 38);\n    };\n  }\n});\nvar Input = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"input.vue\"]]);\nexport { Input as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "inheritAttrs", "rawAttrs", "useAttrs", "attrs", "useAttrs$1", "slots", "useSlots", "containerKls", "computed", "props", "type", "nsTextarea", "b", "nsInput", "m", "inputSize", "value", "is", "inputDisabled", "inputExceed", "prepend", "append", "prefix", "prefixIcon", "suffix", "suffixIcon", "clearable", "showPassword", "bm", "showClear", "showPwdVisible", "class", "wrapperKls", "e", "isFocused", "form", "elForm", "formItem", "elFormItem", "useFormItem", "inputId", "useFormItemInputId", "formItemContext", "useFormSize", "useFormDisabled", "useNamespace", "input", "shallowRef", "textarea", "hovering", "ref", "passwordVisible", "countStyle", "textareaCalcStyle", "inputStyle", "_ref", "wrapperRef", "handleFocus", "handleBlur", "useFocusController", "disabled", "after<PERSON><PERSON>r", "_a", "validateEvent", "validate", "call", "catch", "err", "debugWarn", "needStatusIcon", "statusIcon", "validateState", "validateIcon", "ValidateComponentsMap", "passwordIcon", "View", "<PERSON>de", "containerStyle", "style", "textareaStyle", "resize", "nativeInputValue", "isNil", "modelValue", "String", "readonly", "isWordLimitVisible", "showWordLimit", "maxlength", "textLength", "length", "Number", "suffixVisible", "recordCursor", "setCursor", "useCursor", "useResizeObserver", "entries", "onceInitSizeTextarea", "entry", "width", "contentRect", "right", "resizeTextarea", "autosize", "isClient", "minRows", "isObject", "maxRows", "textareaStyle2", "calcTextareaHeight", "overflowY", "nextTick", "offsetHeight", "minHeight", "createOnceInitResize", "resizeTextarea2", "isInit", "isElHidden", "offsetParent", "setNativeInputValue", "input2", "formatterValue", "formatter", "handleInput", "event", "target", "parser", "isComposing", "emit", "UPDATE_MODEL_EVENT", "INPUT_EVENT", "handleChange", "CHANGE_EVENT", "handleCompositionStart", "handleCompositionUpdate", "handleCompositionEnd", "useComposition", "afterComposition", "handlePasswordVisible", "setTimeout", "focus", "blur", "handleMouseLeave", "evt", "handleMouseEnter", "handleKeydown", "select", "clear", "watch", "onMounted", "expose", "toRef", "_ctx", "_cache", "openBlock", "createElementBlock", "normalizeClass", "unref", "$slots", "normalizeStyle", "onMouseenter", "onMouseleave", "createCommentVNode", "Fragment", "key", "be", "renderSlot", "createElementVNode", "ref_key"], "sources": ["../../../../../../packages/components/input/src/input.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      containerKls,\n      {\n        [nsInput.bm('group', 'append')]: $slots.append,\n        [nsInput.bm('group', 'prepend')]: $slots.prepend,\n      },\n    ]\"\n    :style=\"containerStyle\"\n    @mouseenter=\"handleMouseEnter\"\n    @mouseleave=\"handleMouseLeave\"\n  >\n    <!-- input -->\n    <template v-if=\"type !== 'textarea'\">\n      <!-- prepend slot -->\n      <div v-if=\"$slots.prepend\" :class=\"nsInput.be('group', 'prepend')\">\n        <slot name=\"prepend\" />\n      </div>\n\n      <div ref=\"wrapperRef\" :class=\"wrapperKls\">\n        <!-- prefix slot -->\n        <span v-if=\"$slots.prefix || prefixIcon\" :class=\"nsInput.e('prefix')\">\n          <span :class=\"nsInput.e('prefix-inner')\">\n            <slot name=\"prefix\" />\n            <el-icon v-if=\"prefixIcon\" :class=\"nsInput.e('icon')\">\n              <component :is=\"prefixIcon\" />\n            </el-icon>\n          </span>\n        </span>\n\n        <input\n          :id=\"inputId\"\n          ref=\"input\"\n          :class=\"nsInput.e('inner')\"\n          v-bind=\"attrs\"\n          :name=\"name\"\n          :minlength=\"minlength\"\n          :maxlength=\"maxlength\"\n          :type=\"showPassword ? (passwordVisible ? 'text' : 'password') : type\"\n          :disabled=\"inputDisabled\"\n          :readonly=\"readonly\"\n          :autocomplete=\"autocomplete\"\n          :tabindex=\"tabindex\"\n          :aria-label=\"ariaLabel\"\n          :placeholder=\"placeholder\"\n          :style=\"inputStyle\"\n          :form=\"form\"\n          :autofocus=\"autofocus\"\n          :role=\"containerRole\"\n          :inputmode=\"inputmode\"\n          @compositionstart=\"handleCompositionStart\"\n          @compositionupdate=\"handleCompositionUpdate\"\n          @compositionend=\"handleCompositionEnd\"\n          @input=\"handleInput\"\n          @change=\"handleChange\"\n          @keydown=\"handleKeydown\"\n        />\n\n        <!-- suffix slot -->\n        <span v-if=\"suffixVisible\" :class=\"nsInput.e('suffix')\">\n          <span :class=\"nsInput.e('suffix-inner')\">\n            <template\n              v-if=\"!showClear || !showPwdVisible || !isWordLimitVisible\"\n            >\n              <slot name=\"suffix\" />\n              <el-icon v-if=\"suffixIcon\" :class=\"nsInput.e('icon')\">\n                <component :is=\"suffixIcon\" />\n              </el-icon>\n            </template>\n            <el-icon\n              v-if=\"showClear\"\n              :class=\"[nsInput.e('icon'), nsInput.e('clear')]\"\n              @mousedown.prevent=\"NOOP\"\n              @click=\"clear\"\n            >\n              <circle-close />\n            </el-icon>\n            <el-icon\n              v-if=\"showPwdVisible\"\n              :class=\"[nsInput.e('icon'), nsInput.e('password')]\"\n              @click=\"handlePasswordVisible\"\n            >\n              <component :is=\"passwordIcon\" />\n            </el-icon>\n            <span v-if=\"isWordLimitVisible\" :class=\"nsInput.e('count')\">\n              <span :class=\"nsInput.e('count-inner')\">\n                {{ textLength }} / {{ maxlength }}\n              </span>\n            </span>\n            <el-icon\n              v-if=\"validateState && validateIcon && needStatusIcon\"\n              :class=\"[\n                nsInput.e('icon'),\n                nsInput.e('validateIcon'),\n                nsInput.is('loading', validateState === 'validating'),\n              ]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </span>\n        </span>\n      </div>\n\n      <!-- append slot -->\n      <div v-if=\"$slots.append\" :class=\"nsInput.be('group', 'append')\">\n        <slot name=\"append\" />\n      </div>\n    </template>\n\n    <!-- textarea -->\n    <template v-else>\n      <textarea\n        :id=\"inputId\"\n        ref=\"textarea\"\n        :class=\"[nsTextarea.e('inner'), nsInput.is('focus', isFocused)]\"\n        v-bind=\"attrs\"\n        :minlength=\"minlength\"\n        :maxlength=\"maxlength\"\n        :tabindex=\"tabindex\"\n        :disabled=\"inputDisabled\"\n        :readonly=\"readonly\"\n        :autocomplete=\"autocomplete\"\n        :style=\"textareaStyle\"\n        :aria-label=\"ariaLabel\"\n        :placeholder=\"placeholder\"\n        :form=\"form\"\n        :autofocus=\"autofocus\"\n        :rows=\"rows\"\n        :role=\"containerRole\"\n        @compositionstart=\"handleCompositionStart\"\n        @compositionupdate=\"handleCompositionUpdate\"\n        @compositionend=\"handleCompositionEnd\"\n        @input=\"handleInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @change=\"handleChange\"\n        @keydown=\"handleKeydown\"\n      />\n      <span\n        v-if=\"isWordLimitVisible\"\n        :style=\"countStyle\"\n        :class=\"nsInput.e('count')\"\n      >\n        {{ textLength }} / {{ maxlength }}\n      </span>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  ref,\n  shallowRef,\n  toRef,\n  useAttrs as useRawAttrs,\n  useSlots,\n  watch,\n} from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { isNil } from 'lodash-unified'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  CircleClose,\n  Hide as IconHide,\n  View as IconView,\n} from '@element-plus/icons-vue'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport {\n  NOOP,\n  ValidateComponentsMap,\n  debugWarn,\n  isClient,\n  isObject,\n} from '@element-plus/utils'\nimport {\n  useAttrs,\n  useComposition,\n  useCursor,\n  useFocusController,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { calcTextareaHeight } from './utils'\nimport { inputEmits, inputProps } from './input'\n\nimport type { StyleValue } from 'vue'\n\ntype TargetElement = HTMLInputElement | HTMLTextAreaElement\n\nconst COMPONENT_NAME = 'ElInput'\ndefineOptions({\n  name: COMPONENT_NAME,\n  inheritAttrs: false,\n})\nconst props = defineProps(inputProps)\nconst emit = defineEmits(inputEmits)\n\nconst rawAttrs = useRawAttrs()\nconst attrs = useAttrs()\nconst slots = useSlots()\n\nconst containerKls = computed(() => [\n  props.type === 'textarea' ? nsTextarea.b() : nsInput.b(),\n  nsInput.m(inputSize.value),\n  nsInput.is('disabled', inputDisabled.value),\n  nsInput.is('exceed', inputExceed.value),\n  {\n    [nsInput.b('group')]: slots.prepend || slots.append,\n    [nsInput.m('prefix')]: slots.prefix || props.prefixIcon,\n    [nsInput.m('suffix')]:\n      slots.suffix || props.suffixIcon || props.clearable || props.showPassword,\n    [nsInput.bm('suffix', 'password-clear')]:\n      showClear.value && showPwdVisible.value,\n    [nsInput.b('hidden')]: props.type === 'hidden',\n  },\n  rawAttrs.class,\n])\n\nconst wrapperKls = computed(() => [\n  nsInput.e('wrapper'),\n  nsInput.is('focus', isFocused.value),\n])\n\nconst { form: elForm, formItem: elFormItem } = useFormItem()\nconst { inputId } = useFormItemInputId(props, {\n  formItemContext: elFormItem,\n})\nconst inputSize = useFormSize()\nconst inputDisabled = useFormDisabled()\nconst nsInput = useNamespace('input')\nconst nsTextarea = useNamespace('textarea')\n\nconst input = shallowRef<HTMLInputElement>()\nconst textarea = shallowRef<HTMLTextAreaElement>()\n\nconst hovering = ref(false)\nconst passwordVisible = ref(false)\nconst countStyle = ref<StyleValue>()\nconst textareaCalcStyle = shallowRef(props.inputStyle)\n\nconst _ref = computed(() => input.value || textarea.value)\n\n// wrapperRef for type=\"text\", handleFocus and handleBlur for type=\"textarea\"\nconst { wrapperRef, isFocused, handleFocus, handleBlur } = useFocusController(\n  _ref,\n  {\n    disabled: inputDisabled,\n    afterBlur() {\n      if (props.validateEvent) {\n        elFormItem?.validate?.('blur').catch((err) => debugWarn(err))\n      }\n    },\n  }\n)\n\nconst needStatusIcon = computed(() => elForm?.statusIcon ?? false)\nconst validateState = computed(() => elFormItem?.validateState || '')\nconst validateIcon = computed(\n  () => validateState.value && ValidateComponentsMap[validateState.value]\n)\nconst passwordIcon = computed(() =>\n  passwordVisible.value ? IconView : IconHide\n)\nconst containerStyle = computed<StyleValue>(() => [\n  rawAttrs.style as StyleValue,\n])\nconst textareaStyle = computed<StyleValue>(() => [\n  props.inputStyle,\n  textareaCalcStyle.value,\n  { resize: props.resize },\n])\nconst nativeInputValue = computed(() =>\n  isNil(props.modelValue) ? '' : String(props.modelValue)\n)\nconst showClear = computed(\n  () =>\n    props.clearable &&\n    !inputDisabled.value &&\n    !props.readonly &&\n    !!nativeInputValue.value &&\n    (isFocused.value || hovering.value)\n)\nconst showPwdVisible = computed(\n  () => props.showPassword && !inputDisabled.value && !!nativeInputValue.value\n)\nconst isWordLimitVisible = computed(\n  () =>\n    props.showWordLimit &&\n    !!props.maxlength &&\n    (props.type === 'text' || props.type === 'textarea') &&\n    !inputDisabled.value &&\n    !props.readonly &&\n    !props.showPassword\n)\nconst textLength = computed(() => nativeInputValue.value.length)\nconst inputExceed = computed(\n  () =>\n    // show exceed style if length of initial value greater then maxlength\n    !!isWordLimitVisible.value && textLength.value > Number(props.maxlength)\n)\nconst suffixVisible = computed(\n  () =>\n    !!slots.suffix ||\n    !!props.suffixIcon ||\n    showClear.value ||\n    props.showPassword ||\n    isWordLimitVisible.value ||\n    (!!validateState.value && needStatusIcon.value)\n)\n\nconst [recordCursor, setCursor] = useCursor(input)\n\nuseResizeObserver(textarea, (entries) => {\n  onceInitSizeTextarea()\n  if (!isWordLimitVisible.value || props.resize !== 'both') return\n  const entry = entries[0]\n  const { width } = entry.contentRect\n  countStyle.value = {\n    /** right: 100% - width + padding(15) + right(6) */\n    right: `calc(100% - ${width + 15 + 6}px)`,\n  }\n})\n\nconst resizeTextarea = () => {\n  const { type, autosize } = props\n\n  if (!isClient || type !== 'textarea' || !textarea.value) return\n\n  if (autosize) {\n    const minRows = isObject(autosize) ? autosize.minRows : undefined\n    const maxRows = isObject(autosize) ? autosize.maxRows : undefined\n    const textareaStyle = calcTextareaHeight(textarea.value, minRows, maxRows)\n\n    // If the scrollbar is displayed, the height of the textarea needs more space than the calculated height.\n    // If set textarea height in this case, the scrollbar will not hide.\n    // So we need to hide scrollbar first, and reset it in next tick.\n    // see https://github.com/element-plus/element-plus/issues/8825\n    textareaCalcStyle.value = {\n      overflowY: 'hidden',\n      ...textareaStyle,\n    }\n\n    nextTick(() => {\n      // NOTE: Force repaint to make sure the style set above is applied.\n      textarea.value!.offsetHeight\n      textareaCalcStyle.value = textareaStyle\n    })\n  } else {\n    textareaCalcStyle.value = {\n      minHeight: calcTextareaHeight(textarea.value).minHeight,\n    }\n  }\n}\n\nconst createOnceInitResize = (resizeTextarea: () => void) => {\n  let isInit = false\n  return () => {\n    if (isInit || !props.autosize) return\n    const isElHidden = textarea.value?.offsetParent === null\n    if (!isElHidden) {\n      resizeTextarea()\n      isInit = true\n    }\n  }\n}\n// fix: https://github.com/element-plus/element-plus/issues/12074\nconst onceInitSizeTextarea = createOnceInitResize(resizeTextarea)\n\nconst setNativeInputValue = () => {\n  const input = _ref.value\n  const formatterValue = props.formatter\n    ? props.formatter(nativeInputValue.value)\n    : nativeInputValue.value\n  if (!input || input.value === formatterValue) return\n  input.value = formatterValue\n}\n\nconst handleInput = async (event: Event) => {\n  recordCursor()\n\n  let { value } = event.target as TargetElement\n\n  if (props.formatter && props.parser) {\n    value = props.parser(value)\n  }\n\n  // should not emit input during composition\n  // see: https://github.com/ElemeFE/element/issues/10516\n  if (isComposing.value) return\n\n  // hack for https://github.com/ElemeFE/element/issues/8548\n  // should remove the following line when we don't support IE\n  if (value === nativeInputValue.value) {\n    setNativeInputValue()\n    return\n  }\n\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(INPUT_EVENT, value)\n\n  // ensure native input value is controlled\n  // see: https://github.com/ElemeFE/element/issues/12850\n  await nextTick()\n  setNativeInputValue()\n  setCursor()\n}\n\nconst handleChange = (event: Event) => {\n  let { value } = event.target as TargetElement\n\n  if (props.formatter && props.parser) {\n    value = props.parser(value)\n  }\n  emit(CHANGE_EVENT, value)\n}\n\nconst {\n  isComposing,\n  handleCompositionStart,\n  handleCompositionUpdate,\n  handleCompositionEnd,\n} = useComposition({ emit, afterComposition: handleInput })\n\nconst handlePasswordVisible = () => {\n  recordCursor()\n  passwordVisible.value = !passwordVisible.value\n  // The native input needs a little time to regain focus\n  setTimeout(setCursor)\n}\n\nconst focus = () => _ref.value?.focus()\n\nconst blur = () => _ref.value?.blur()\n\nconst handleMouseLeave = (evt: MouseEvent) => {\n  hovering.value = false\n  emit('mouseleave', evt)\n}\n\nconst handleMouseEnter = (evt: MouseEvent) => {\n  hovering.value = true\n  emit('mouseenter', evt)\n}\n\nconst handleKeydown = (evt: KeyboardEvent) => {\n  emit('keydown', evt)\n}\n\nconst select = () => {\n  _ref.value?.select()\n}\n\nconst clear = () => {\n  emit(UPDATE_MODEL_EVENT, '')\n  emit(CHANGE_EVENT, '')\n  emit('clear')\n  emit(INPUT_EVENT, '')\n}\n\nwatch(\n  () => props.modelValue,\n  () => {\n    nextTick(() => resizeTextarea())\n    if (props.validateEvent) {\n      elFormItem?.validate?.('change').catch((err) => debugWarn(err))\n    }\n  }\n)\n\n// native input value is set explicitly\n// do not use v-model / :value in template\n// see: https://github.com/ElemeFE/element/issues/14521\nwatch(nativeInputValue, () => setNativeInputValue())\n\n// when change between <input> and <textarea>,\n// update DOM dependent value and styles\n// https://github.com/ElemeFE/element/issues/14857\nwatch(\n  () => props.type,\n  async () => {\n    await nextTick()\n    setNativeInputValue()\n    resizeTextarea()\n  }\n)\n\nonMounted(() => {\n  if (!props.formatter && props.parser) {\n    debugWarn(\n      COMPONENT_NAME,\n      'If you set the parser, you also need to set the formatter.'\n    )\n  }\n  setNativeInputValue()\n  nextTick(resizeTextarea)\n})\n\ndefineExpose({\n  /** @description HTML input element */\n  input,\n  /** @description HTML textarea element */\n  textarea,\n  /** @description HTML element, input or textarea */\n  ref: _ref,\n  /** @description style of textarea. */\n  textareaStyle,\n\n  /** @description from props (used on unit test) */\n  autosize: toRef(props, 'autosize'),\n\n  /** @description is input composing */\n  isComposing,\n\n  /** @description HTML input element native method */\n  focus,\n  /** @description HTML input element native method */\n  blur,\n  /** @description HTML input element native method */\n  select,\n  /** @description clear input value */\n  clear,\n  /** @description resize textarea. */\n  resizeTextarea,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;mCA2Mc;EACZA,IAAM,EAAAC,cAAA;EACNC,YAAc;AAChB;;;;;;;;;;IAIA,MAAMC,QAAA,GAAWC,QAAY;IAC7B,MAAMC,KAAA,GAAQC,UAAS;IACvB,MAAMC,KAAA,GAAQC,QAAS;IAEjB,MAAAC,YAAA,GAAeC,QAAA,CAAS,MAAM,CAClCC,KAAA,CAAMC,IAAS,kBAAaC,UAAA,CAAWC,CAAE,KAAIC,OAAA,CAAQD,CAAE,IACvDC,OAAA,CAAQC,CAAE,CAAAC,SAAA,CAAUC,KAAK,GACzBH,OAAQ,CAAAI,EAAA,CAAG,UAAY,EAAAC,aAAA,CAAcF,KAAK,GAC1CH,OAAQ,CAAAI,EAAA,CAAG,QAAU,EAAAE,WAAA,CAAYH,KAAK,GACtC;MACE,CAACH,OAAA,CAAQD,CAAE,QAAO,CAAC,GAAGP,KAAA,CAAMe,OAAA,IAAWf,KAAM,CAAAgB,MAAA;MAC7C,CAACR,OAAA,CAAQC,CAAE,SAAQ,CAAC,GAAGT,KAAA,CAAMiB,MAAA,IAAUb,KAAM,CAAAc,UAAA;MAC7C,CAACV,OAAA,CAAQC,CAAE,SAAQ,CAAC,GAClBT,KAAM,CAAAmB,MAAA,IAAUf,KAAM,CAAAgB,UAAA,IAAchB,KAAM,CAAAiB,SAAA,IAAajB,KAAM,CAAAkB,YAAA;MAC/D,CAACd,OAAA,CAAQe,EAAG,WAAU,gBAAgB,CAAC,GACrCC,SAAU,CAAAb,KAAA,IAASc,cAAe,CAAAd,KAAA;MACpC,CAACH,OAAQ,CAAAD,CAAA,CAAE,QAAQ,CAAC,GAAGH,KAAA,CAAMC,IAAS;IAAA,CACxC,EACAT,QAAS,CAAA8B,KAAA,CACV;IAEK,MAAAC,UAAA,GAAaxB,QAAA,CAAS,MAAM,CAChCK,OAAA,CAAQoB,CAAA,CAAE,SAAS,GACnBpB,OAAQ,CAAAI,EAAA,CAAG,OAAS,EAAAiB,SAAA,CAAUlB,KAAK,EACpC;IAED,MAAM;MAAEmB,IAAM,EAAAC,MAAA;MAAQC,QAAU,EAAAC;IAAA,IAAeC,WAAY;IAC3D,MAAM;MAAEC;IAAA,CAAY,GAAAC,kBAAA,CAAmBhC,KAAO;MAC5CiC,eAAiB,EAAAJ;IAAA,CAClB;IACD,MAAMvB,SAAA,GAAY4B,WAAY;IAC9B,MAAMzB,aAAA,GAAgB0B,eAAgB;IAChC,MAAA/B,OAAA,GAAUgC,YAAA,CAAa,OAAO;IAC9B,MAAAlC,UAAA,GAAakC,YAAA,CAAa,UAAU;IAE1C,MAAMC,KAAA,GAAQC,UAA6B;IAC3C,MAAMC,QAAA,GAAWD,UAAgC;IAE3C,MAAAE,QAAA,GAAWC,GAAA,CAAI,KAAK;IACpB,MAAAC,eAAA,GAAkBD,GAAA,CAAI,KAAK;IACjC,MAAME,UAAA,GAAaF,GAAgB;IAC7B,MAAAG,iBAAA,GAAoBN,UAAW,CAAAtC,KAAA,CAAM6C,UAAU;IAErD,MAAMC,IAAA,GAAO/C,QAAS,OAAMsC,KAAM,CAAA9B,KAAA,IAASgC,QAAA,CAAShC,KAAK;IAGzD,MAAM;MAAEwC,UAAA;MAAYtB,SAAW;MAAAuB,WAAA;MAAaC;IAAA,CAAe,GAAAC,kBAAA,CAAAJ,IAAA;MACzDK,QAAA,EAAA1C,aAAA;MACA2C,UAAA;QACE,IAAUC,EAAA;QACV,IAAYrD,KAAA,CAAAsD,aAAA;UACV,CAAAD,EAAA,GAAAxB,UAAyB,oBAAAA,UAAA,CAAA0B,QAAA,qBAAAF,EAAA,CAAAG,IAAA,CAAA3B,UAAA,UAAA4B,KAAA,CAAAC,GAAA,IAAAC,SAAA,CAAAD,GAAA;QACvB;MAA4D;IAC9D,CACF;IACF,MAAAE,cAAA,GAAA7D,QAAA;MACF,IAAAsD,EAAA;MAEA,OAAuB,CAAAA,EAAA,GAAA1B,MAAA,WAAS,KAAM,IAAAA,MAAA,CAAAkC,UAAA,KAA2B,OAAAR,EAAA;IACjE;IACA,MAAMS,aAAe,GAAA/D,QAAA,QAAA8B,UAAA,oBAAAA,UAAA,CAAAiC,aAAA;IAAA,MACbC,YAAA,GAAAhE,QAAuB,OAAA+D,aAAA,CAAAvD,KAAA,IAAAyD,qBAAyC,CAAAF,aAAA,CAAAvD,KAAA;IACxE,MAAA0D,YAAA,GAAAlE,QAAA,OAAA2C,eAAA,CAAAnC,KAAA,GAAA2D,IAAA,GAAAC,IAAA;IACA,MAAMC,cAAe,GAAArE,QAAA,QAASP,QACZ,CAAA6E,KAAA,CAClB;IACM,MAAAC,aAAA,GAAAvE,QAAA,OAA4C,CAChDC,KAAS,CAAA6C,UAAA,EACVD,iBAAA,CAAArC,KAAA,EACK;MAAAgE,MAAA,EAAAvE,KAAA,CAAAuE;IAAA,EAA2C,CAC/C;IAAM,MACYC,gBAAA,GAAAzE,QAAA,OAAA0E,KAAA,CAAAzE,KAAA,CAAA0E,UAAA,SAAAC,MAAA,CAAA3E,KAAA,CAAA0E,UAAA;IAClB,MAAUtD,SAAA,GAAMrB,QAAO,OAAAC,KAAA,CAAAiB,SAAA,KAAAR,aAAA,CAAAF,KAAA,KAAAP,KAAA,CAAA4E,QAAA,MAAAJ,gBAAA,CAAAjE,KAAA,KAAAkB,SAAA,CAAAlB,KAAA,IAAAiC,QAAA,CAAAjC,KAAA;IACzB,MAACc,cAAA,GAAAtB,QAAA,OAAAC,KAAA,CAAAkB,YAAA,KAAAT,aAAA,CAAAF,KAAA,MAAAiE,gBAAA,CAAAjE,KAAA;IACD,MAAMsE,kBAAmB,GAAA9E,QAAA,OAAAC,KAAA,CAAA8E,aAAA,MAAA9E,KAAA,CAAA+E,SAAA,KAAA/E,KAAA,CAAAC,IAAA,eAAAD,KAAA,CAAAC,IAAA,qBAAAQ,aAAA,CAAAF,KAAA,KAAAP,KAAA,CAAA4E,QAAA,KAAA5E,KAAA,CAAAkB,YAAA;IAAS,MAAA8D,UACpB,GAAAjF,QAAA,OAAcyE,gBAAA,CAAAjE,KAA4B,CAAA0E,MAAA;IACxD,MAAAvE,WAAA,GAAAX,QAAA,SAAA8E,kBAAA,CAAAtE,KAAA,IAAAyE,UAAA,CAAAzE,KAAA,GAAA2E,MAAA,CAAAlF,KAAA,CAAA+E,SAAA;IACA,MAAMI,aAAY,GAAApF,QAAA,SAAAH,KAAA,CAAAmB,MAAA,MAAAf,KAAA,CAAAgB,UAAA,IAAAI,SAAA,CAAAb,KAAA,IAAAP,KAAA,CAAAkB,YAAA,IAAA2D,kBAAA,CAAAtE,KAAA,MAAAuD,aAAA,CAAAvD,KAAA,IAAAqD,cAAA,CAAArD,KAAA;IAAA,MAEd,CAAA6E,YACA,EAAAC,SAAA,IAAAC,SACA,CAAAjD,KAAA;IAGJkD,iBAAA,CAAAhD,QAAA,EAAAiD,OAAA;MACAC,oBAAuB;MACrB,KAAAZ,kBAA4B,CAAAtE,KAAC,IAAAP,KAAA,CAAAuE,MAAuB,WAAmB,EACzE;MACA,MAA2BmB,KAAA,GAAAF,OAAA;MACzB,MACE;QAAAG;MACA,IAAAD,KAAA,CAAAE,WAAQ;MAKZjD,UAAA,CAAApC,KAAA;QACAsF,KAAmB,iBAAAF,KAAe;MAClC;IAAoB,CAClB;IAAA,MAAAG,cAAA,GAAAA,CAAA;MAEE,MAAqB;QAAA7F,IAAA;QAAA8F;MAAA,IAAA/F,KAAS;MAAyC,KAAAgG,QAAA,IAAA/F,IAAA,oBAAAsC,QAAA,CAAAhC,KAAA,EAC3E;MACA,IAAMwF,QAAgB;QAAA,MAEhBE,OACF,GAAAC,QAAE,CAAMH,QAAA,IAAAA,QAAA,CAAAE,OAAA,GAER;QAGJ,MAAAE,OAAA,GAAAD,QAAA,CAAAH,QAAA,IAAAA,QAAA,CAAAI,OAAA;QAEA,MAAOC,cAAuB,GAAAC,kBAAmB,CAAA9D,QAAA,CAAAhC,KAAA,EAAA0F,OAAA,EAAAE,OAAA;QAE/BvD,iBAAA,CAAArC,KAAA,GAAuB;UAClB+F,SAAA;UACjB,GAACF;QACL,CAAM;QACAG,QAAQ;UACdhE,QAAmB,CAAAhC,KAAA,CAAAiG,YAAA;UAAA5D,iBAAA,CAAArC,KAAA,GAAA6F,cAAA;QAAA,CAEV;MAA6B,CACtC;QACDxD,iBAAA,CAAArC,KAAA;UAEKkG,SAAA,EAAAJ,kBAAuB,CAAA9D,QAAA,CAAAhC,KAAA,EAAAkG;QAC3B,CAAM;MAEN;IAEA;IACE,MAAAC,oBAAgB,GAAiBC,eAAA,IAAa;MAC9C,IAAAC,MAAgB;MAChB,aAAsB;QAMtB,IAAAvD,EAAA;QAA0B,IACbuD,MAAA,KAAA5G,KAAA,CAAA+F,QAAA,EACX;QACF,MAAAc,UAAA,KAAAxD,EAAA,GAAAd,QAAA,CAAAhC,KAAA,qBAAA8C,EAAA,CAAAyD,YAAA;QAEA,KAAAD,UAAe;UAEbF,eAAgB;UAChBC,MAAA;QAA0B;MAC3B,CACI;IACL;IAA0B,MACxBnB,oBAAW,GAAAiB,oBAA4B,CAAKZ,cAAE;IAChD,MAAAiB,mBAAA,GAAAA,CAAA;MACF,MAAAC,MAAA,GAAAlE,IAAA,CAAAvC,KAAA;MACF,MAAA0G,cAAA,GAAAjH,KAAA,CAAAkH,SAAA,GAAAlH,KAAA,CAAAkH,SAAA,CAAA1C,gBAAA,CAAAjE,KAAA,IAAAiE,gBAAA,CAAAjE,KAAA;MAEM,KAAAyG,MAAA,IAAAA,MAAA,CAAAzG,KAAuB,KAAgC0G,cAAA,EAC3D;MACAD,MAAA,CAAOzG,KAAM,GAAA0G,cAAA;IACX,CAAI;IACE,MAAAE,WAAA,SAAsBC,KAAA;MAC5BhC,YAAiB;MACf;QAAe7E;MAAA,IAAA6G,KAAA,CAAAC,MAAA;MACN,IAAArH,KAAA,CAAAkH,SAAA,IAAAlH,KAAA,CAAAsH,MAAA;QACX/G,KAAA,GAAAP,KAAA,CAAAsH,MAAA,CAAA/G,KAAA;MAAA;MAEJ,IAAAgH,WAAA,CAAAhH,KAAA,EAEM;MAEN,IAAMA,KAAA,KAAAiE,gBAA4B,CAAAjE,KAAA;QAChCwG,mBAAmB;QACb;MAGN;MACAS,IAAA,CAAAC,kBAAc,EAAAlH,KAAA;MAChBiH,IAAA,CAAAE,WAAA,EAAAnH,KAAA;MAEM,MAAAgG,QAAA;MACSQ,mBAAA;MAET1B,SAAQ;IAEZ,CAAI;IACM,MAAAsC,YAAM,GAAAP,KAAY;MAC5B;QAAA7G;MAAA,IAAA6G,KAAA,CAAAC,MAAA;MAIA,IAAIrH,KAAA,CAAAkH,SAAmB,IAAAlH,KAAA,CAAAsH,MAAA;QAInB/G,KAAA,GAAAP,KAAA,CAAAsH,MAAA,CAAA/G,KAAA;MACF;MACAiH,IAAA,CAAAI,YAAA,EAAArH,KAAA;IAAA,CACF;IAEA;MACAgH,WAAA;MAIAM,sBAAe;MACKC,uBAAA;MACVC;IAAA,CACZ,GAAAC,cAAA;MAAAR,IAAA;MAAAS,gBAAA,EAAAd;IAAA;IAEM,MAAAe,qBAAiC,GAAAA,CAAA;MACjC9C,YAAQ;MAER1C,eAAmB,CAAAnC,KAAA,IAAAmC,eAAc,CAAAnC,KAAA;MAC3B4H,UAAA,CAAA9C,SAAA;IAAkB,CAC5B;IACA,MAAA+C,KAAA,GAAAA,CAAA;MACF,IAAA/E,EAAA;MAEM,QAAAA,EAAA,GAAAP,IAAA,CAAAvC,KAAA,qBAAA8C,EAAA,CAAA+E,KAAA;IAAA,CACJ;IACA,MAAAC,IAAA,GAAAA,CAAA;MACA,IAAAhF,EAAA;MACA,QAAAA,EAAA,GAAAP,IAAA,CAAAvC,KAAA,qBAAA8C,EAAA,CAAAgF,IAAA;IAAA;IAGF,MAAMC,gBAAA,GAAAC,GAAA,IAA8B;MACrB/F,QAAA,CAAAjC,KAAA;MACGiH,IAAA,eAAAe,GAAA;IAEhB;IACF,MAAAC,gBAAA,GAAAD,GAAA;MAEA/F,QAAc,CAAAjC,KAAA,OAAW;MAEzBiH,IAAM,CAAO,YAAM,EAAKe,GAAA;IAExB,CAAM;IACJ,MAAAE,aAAiB,GAAAF,GAAA;MACjBf,IAAA,CAAK,WAAAe,GAAc,CAAG;IAAA,CACxB;IAEM,MAAAG,MAAA,GAAAA,CAAA;MACJ,IAAArF,EAAA;MACA,CAAAA,EAAA,GAAAP,IAAA,CAAAvC,KAAA,KAAsB,gBAAA8C,EAAA,CAAAqF,MAAA;IAAA,CACxB;IAEM,MAAAC,KAAA,GAAAA,CAAA;MACJnB,IAAA,CAAKC,kBAAc;MACrBD,IAAA,CAAAI,YAAA;MAEAJ,IAAM,SAAS;MACbA,IAAA,CAAKE,WAAc;IAAA,CACrB;IAEAkB,KAAA,CAAM,MAAA5I,KAAc,CAAA0E,UAAA;MAClB,IAAArB,EAAA;MACAkD,QAAA,OAAAT,cAAqB;MACrB,IAAA9F,KAAY,CAAAsD,aAAA;QACZ,CAAAD,EAAK,GAAAxB,UAAa,IAAE,gBAAAA,UAAA,CAAA0B,QAAA,qBAAAF,EAAA,CAAAG,IAAA,CAAA3B,UAAA,YAAA4B,KAAA,CAAAC,GAAA,IAAAC,SAAA,CAAAD,GAAA;MAAA;IAGtB;IAAAkF,KAAA,CAAApE,gBACc,QAAAuC,mBAAA;IAAA6B,KACN,OAAA5I,KAAA,CAAAC,IAAA;MACK,MAAAsG,QAAA;MACTQ,mBAAyB;MACXjB,cAAA;IAAkD,CAChE;IACF+C,SAAA;MACF,KAAA7I,KAAA,CAAAkH,SAAA,IAAAlH,KAAA,CAAAsH,MAAA;QAKM3D,SAAA,CAAArE,cAAwB,8DAAqB;MAKnD;MACEyH,mBAAY;MACZR,QAAY,CAAAT,cAAA;IACV;IACoBgD,MAAA;MACLzG,KAAA;MACjBE,QAAA;MACFE,GAAA,EAAAK,IAAA;MAEAwB,aAAgB;MACdyB,QAAK,EAAAgD,KAAmB,CAAA/I,KAAA,YAAc;MACpCuH,WAAA;MACEa,KAAA;MACAC,IAAA;MACFK,MAAA;MACFC,KAAA;MACoB7C;IACpB;IACF,OAAC,CAAAkD,IAAA,EAAAC,MAAA;MAEY,OAAAC,SAAA,IAAAC,kBAAA;QAAA7H,KAAA,EAAA8H,cAAA,EAEXC,KAAA,CAAAvJ,YAAA;UAEA,CAAAuJ,KAAA,CAAAjJ,OAAA,EAAAe,EAAA,sBAAA6H,IAAA,CAAAM,MAAA,CAAA1I,MAAA;UAAA,CAAAyI,KAAA,CAAAjJ,OAAA,EAAAe,EAAA,uBAAA6H,IAAA,CAAAM,MAAA,CAAA3I;QAAA,CAEK;QAEL0D,KAAA,EAAAkF,cAAA,CAAAF,KAAA,CAAAjF,cAAA;QAAAoF,YAAA,EAAAhB,gBAAA;QAGAiB,YAAgB,EAAAnB;MAAiB,IAGjCoB,kBAAA,aAAAV,IAAA,CAAA/I,IAAA,mBAAAiJ,SAAA,IAAAC,kBAAA,CAAAQ,QAAA;QAAAC,GAAA;MAAA,IAGAF,kBAAA,oBAAAV,IAAA,CAAAM,MAAA,CAAA3I,OAAA,IAAAuI,SAAA,IAAAC,kBAAA;QAEAS,GAAA;QAAAtI,KAAA,EAAA8H,cAAA,CAAAC,KAAA,CAAAjJ,OAAA,EAAAyJ,EAAA;MAAA,CAEA,GAAAC,UAAA,CAAAd,IAAA,CAAAM,MAAA,aAEA,QAAAI,kBAAA,gBAAAK,kBAAA;QAEAC,OAAA;QACDvH,GAAA,EAAAM,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "js/824.a340c6aa.js", "mappings": "+aAwHMA,EAAU,0B,oCAGhB,MAAMC,EAAOC,EAAAA,EAAMC,OAAO,CACxBC,QAASJ,IAIXC,EAAKI,aAAaC,QAAQC,IAAIC,IAE5B,MAAMC,EAAQC,aAAaC,QAAQ,SAInC,OAHIF,IACFD,EAAOI,QAAQC,cAAgB,UAAUJ,KAEpCD,GACNM,GACMC,QAAQC,OAAOF,IAIxB,MAAMG,EAAcC,IAClB,IAAKA,EAAY,MAAO,IACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,GAAIG,MAAMF,EAAKG,WAAY,OAAOJ,EAElC,MAAMK,EAAOJ,EAAKK,cACZC,EAAQC,OAAOP,EAAKQ,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOP,EAAKW,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOP,EAAKa,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOP,EAAKe,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOP,EAAKiB,cAAcR,SAAS,EAAG,KAEtD,MAAO,GAAGL,KAAQE,KAASI,KAAOE,KAASE,KAAWE,KAGlDE,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAcD,EAAAA,EAAAA,IAAI,GAClBE,GAAWF,EAAAA,EAAAA,IAAI,IACfG,GAAQH,EAAAA,EAAAA,IAAI,GACZI,GAAgBJ,EAAAA,EAAAA,KAAI,GACpBK,GAAcL,EAAAA,EAAAA,IAAI,QAClBM,GAAgBN,EAAAA,EAAAA,IAAI,MACpBO,GAAWP,EAAAA,EAAAA,IAAI,IACfQ,GAAeR,EAAAA,EAAAA,IAAI,GAAGtC,iBACtB+C,GAAaT,EAAAA,EAAAA,KAAI,GACjBU,GAAWV,EAAAA,EAAAA,IAAI,MAGfW,GAAgBC,EAAAA,EAAAA,IAAS,KACtB,CACLrC,cAAe,UAAUH,aAAaC,QAAQ,UAAY,QAKxDwC,GAAab,EAAAA,EAAAA,IAAI,IAGjBc,GAAaC,EAAAA,EAAAA,IAAS,CAC1BC,MAAO,KAIHC,GAAaF,EAAAA,EAAAA,IAAS,CAC1BG,GAAI,KACJF,MAAO,GACPG,YAAa,GACbC,cAAe,KAIXC,EAAc,CAClBL,MAAO,CACL,CAAEM,UAAU,EAAMC,QAAS,UAAWC,QAAS,UAK7CC,EAAeC,UACnB3B,EAAQ4B,OAAQ,EAChB,IAEE,MAAMC,QAAiBjE,EAAKkE,IAAI,gBAC5BD,EAASE,KAAKC,SAChBlB,EAAWc,MAAQC,EAASE,KAAKA,KACjC3B,EAAMwB,MAAQC,EAASE,KAAKE,OAE5BC,EAAAA,GAAUzD,MAAMoD,EAASE,KAAKP,SAAW,WAE7C,CAAE,MAAO/C,GACP0D,QAAQ1D,MAAM,YAAaA,GACvBA,EAAMoD,UAAsC,MAA1BpD,EAAMoD,SAASO,OACnCF,EAAAA,GAAUzD,MAAM,eAIhByD,EAAAA,GAAUzD,MAAM,mBAEpB,CAAE,QACAuB,EAAQ4B,OAAQ,CAClB,IAGFS,EAAAA,EAAAA,IAAU,KACRX,MAIF,MAAMY,EAAcA,KAClBvB,EAAWE,MAAQ,GACnBS,KAIIa,EAAeZ,UACnB3B,EAAQ4B,OAAQ,EAChB,IACE,MAAMC,QAAiBjE,EAAKkE,IAAI,sBAAuB,CACrDU,OAAQ,CAAEC,QAAS1B,EAAWE,SAE5BY,EAASE,KAAKC,SAChBlB,EAAWc,MAAQC,EAASE,KAAKA,KACjC3B,EAAMwB,MAAQC,EAASE,KAAKE,MAC5BC,EAAAA,GAAUF,QAAQ,SAElBE,EAAAA,GAAUzD,MAAMoD,EAASE,KAAKP,SAAW,OAE7C,CAAE,MAAO/C,GACP0D,QAAQ1D,MAAM,QAASA,GACvByD,EAAAA,GAAUzD,MAAM,eAClB,CAAE,QACAuB,EAAQ4B,OAAQ,CAClB,GAIIc,EAAkBA,KACtBpC,EAAYsB,MAAQ,OACpBvB,EAAcuB,OAAQ,EAEtBV,EAAWC,GAAK,KAChBD,EAAWD,MAAQ,GACnBC,EAAWE,YAAc,GACzBF,EAAWG,cAAgB,GAC3BV,EAASiB,MAAQ,KACjBpB,EAASoB,MAAQ,IAIbe,EAAcC,IAClBtC,EAAYsB,MAAQ,OACpBvB,EAAcuB,OAAQ,EAGtBV,EAAWC,GAAKyB,EAAIzB,GACpBD,EAAWD,MAAQ2B,EAAI3B,MACvBC,EAAWE,YAAcwB,EAAIxB,YAC7BF,EAAWG,cAAgBuB,EAAIvB,cAC/BV,EAASiB,MAAQ,KAGjBpB,EAASoB,MAAQ,GACbgB,EAAIvB,eACNb,EAASoB,MAAMiB,KAAK,CAClBC,KAAMF,EAAIvB,cAAc0B,MAAM,KAAKC,MACnCC,IAAK,GAAGtF,KAAWiF,EAAIvB,mBAMvB6B,EAAgBN,IACpBO,EAAAA,EAAaC,QAAQ,WAAWR,EAAI3B,WAAY,KAAM,CACpDoC,kBAAmB,KACnBC,iBAAkB,KAClBC,KAAM,YACLC,KAAK7B,UACN,IACE,MAAME,QAAiBjE,EAAK6F,OAAO,gBAAgBb,EAAIzB,MACnDU,EAASE,KAAKC,SAChBE,EAAAA,GAAUF,QAAQ,MAAMY,EAAI3B,aAC5BS,KAEAQ,EAAAA,GAAUzD,MAAMoD,EAASE,KAAKP,SAAW,SAE7C,CAAE,MAAO/C,GACP0D,QAAQ1D,MAAM,UAAWA,GACzByD,EAAAA,GAAUzD,MAAM,iBAClB,IACCiF,MAAM,SAILC,EAAiBhC,UACrB,GAAKiB,EAAIvB,cAKT,IACEa,EAAAA,GAAUF,QAAQ,QAAQY,EAAI3B,aAG9B,MAAMY,QAAiBjE,EAAKkE,IAAI,gBAAgBc,EAAIzB,cAAe,CACjEyC,aAAc,SAIhB,IAAIC,EAAW,GACf,MAAMC,EAAqBjC,EAAStD,QAAQ,uBAC5C,GAAIuF,EAAoB,CACtB,MAAMC,EAAgBD,EAAmBE,MAAM,0CAC/C,GAAID,GAAiBA,EAAc,GAAI,CACrCF,EAAWE,EAAc,GAAGE,QAAQ,QAAS,IAC7C,IAEEJ,EAAWK,mBAAmBL,EAChC,CAAE,MAAOM,GACPhC,QAAQ1D,MAAM,UAAW0F,EAE3B,CACF,CACF,CAGKN,IACHA,EAAWjB,EAAIvB,cAAc0B,MAAM,KAAKC,OAI1C,MAAMoB,EAAO,IAAIC,KAAK,CAACxC,EAASE,OAC1BkB,EAAMqB,OAAOC,IAAIC,gBAAgBJ,GAGjCK,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAO3B,EACZwB,EAAKI,aAAa,WAAYhB,GAC9Ba,SAASI,KAAKC,YAAYN,GAC1BA,EAAKO,QAGLN,SAASI,KAAKG,YAAYR,GAC1BH,OAAOC,IAAIW,gBAAgBjC,EAE7B,CAAE,MAAOxE,GACP0D,QAAQ1D,MAAM,QAASA,GACvByD,EAAAA,GAAUzD,MAAM,oBAClB,MApDEyD,EAAAA,GAAUiD,QAAQ,cAwDhBC,EAAuBC,IAE3B1E,EAASiB,MAAQyD,EAAQC,KAGzB,MAAMC,EAAmBF,EAAQC,KAAKxC,KAEtCZ,EAAAA,GAAUF,QAAQ,MAAMuD,SACxBF,EAAQG,aAIJC,EAAa9D,UACZpB,EAAcqB,aAEbrB,EAAcqB,MAAM8D,SAAS/D,UACjC,IAAIgE,EAyCF,OAAO,EAxCPjF,EAAWkB,OAAQ,EACnB,IACE,MAAMgE,EAAW,IAAIC,SAUrB,IAAIhE,EATJ+D,EAASE,OAAO,QAAS5E,EAAWD,OACpC2E,EAASE,OAAO,cAAe5E,EAAWE,aAEtCT,EAASiB,QAEXgE,EAASE,OAAO,mBAAoBnF,EAASiB,MAAMkB,MACnD8C,EAASE,OAAO,WAAYnF,EAASiB,QAInCV,EAAWC,IAEbU,QAAiBjE,EAAKmI,IAAI,gBAAgB7E,EAAWC,KAAMyE,GACvD/D,EAASE,KAAKC,QAChBE,EAAAA,GAAUF,QAAQ,MAAMd,EAAWD,eAEnCiB,EAAAA,GAAUzD,MAAMoD,EAASE,KAAKP,SAAW,YAI3CK,QAAiBjE,EAAKoI,KAAK,eAAgBJ,GACvC/D,EAASE,KAAKC,QAChBE,EAAAA,GAAUF,QAAQ,MAAMd,EAAWD,cAEnCiB,EAAAA,GAAUzD,MAAMoD,EAASE,KAAKP,SAAW,WAI7CnB,EAAcuB,OAAQ,EACtBF,GACF,CAAE,MAAOjD,GACP0D,QAAQ1D,MAAM,UAAWA,GACzByD,EAAAA,GAAUzD,MAAM,eAClB,CAAE,QACAiC,EAAWkB,OAAQ,CACrB,KAQAqE,EAAiBX,IACjBA,EAAKrC,KACPqB,OAAO4B,KAAKZ,EAAKrC,IAAK,WAIpBkD,EAAeA,KACnB3F,EAASoB,MAAQ,GACjBjB,EAASiB,MAAQ,MAGbwE,EAAsBA,OAItBC,EAAgBf,IAEpB,MAAMgB,EAAU,SAChB,QAAIhB,EAAKiB,KAAOD,KACdpE,EAAAA,GAAUzD,MAAM,iBACT,IAML+H,EAAoBD,IACxBpG,EAASyB,MAAQ2E,EACjBrG,EAAY0B,MAAQ,EACpBF,KAGI+E,EAAuBC,IAC3BxG,EAAY0B,MAAQ8E,EACpBhF,K,yUAndAiF,EAAAA,EAAAA,IA6GM,MA7GNC,EA6GM,EA5GJC,EAAAA,EAAAA,IAYUC,EAAA,CAZDC,MAAM,eAAa,C,iBAC1B,IAUM,EAVNC,EAAAA,EAAAA,IAUM,MAVNC,EAUM,EATJJ,EAAAA,EAAAA,IAQUK,EAAA,CARAC,MAAOpG,EAAYqG,OAAA,I,kBAC3B,IAEe,EAFfP,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,QAAM,C,iBACxB,IAAgF,EAAhFT,EAAAA,EAAAA,IAAgFU,EAAA,C,WAA7DxG,EAAWE,M,qCAAXF,EAAWE,MAAKuG,GAAEC,YAAY,UAAUC,UAAA,I,gCAE7Db,EAAAA,EAAAA,IAGeQ,EAAA,M,iBAFb,IAA8D,EAA9DR,EAAAA,EAAAA,IAA8Dc,EAAA,CAAnDpE,KAAK,UAAWqE,QAAOrF,G,kBAAc,IAAEsF,EAAA,KAAAA,EAAA,K,QAAF,S,cAChDhB,EAAAA,EAAAA,IAA8Cc,EAAA,CAAlCC,QAAOtF,GAAW,C,iBAAE,IAAEuF,EAAA,KAAAA,EAAA,K,QAAF,S,oDAMxChB,EAAAA,EAAAA,IAgDUC,EAAA,CAhDDC,MAAM,cAAY,CACde,QAAMC,EAAAA,EAAAA,IACf,IAKM,EALNf,EAAAA,EAAAA,IAKM,MALNgB,EAKM,C,aAJJhB,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAEM,aADJH,EAAAA,EAAAA,IAAmEc,EAAA,CAAxDpE,KAAK,UAAWqE,QAAOlF,G,kBAAiB,IAAImF,EAAA,KAAAA,EAAA,K,QAAJ,W,oCAKzD,IAwBW,E,qBAxBXI,EAAAA,EAAAA,IAwBWC,EAAA,CAxBAnG,KAAMjB,EAAAc,MAAYuG,OAAA,GAAOC,OAAA,GAAOC,MAAA,gB,kBACzC,IAA2C,EAA3CxB,EAAAA,EAAAA,IAA2CyB,EAAA,CAA1B/E,KAAK,QAAQgF,MAAM,QACpC1B,EAAAA,EAAAA,IAA6DyB,EAAA,CAA5CE,KAAK,QAAQlB,MAAM,OAAO,YAAU,SACrDT,EAAAA,EAAAA,IAAyFyB,EAAA,CAAxEE,KAAK,cAAclB,MAAM,OAAO,YAAU,MAAM,8BACjET,EAAAA,EAAAA,IAQkByB,EAAA,CARDhB,MAAM,KAAKiB,MAAM,MAAME,MAAM,U,CACjCC,SAAOX,EAAAA,EAAAA,IAAEY,GAAK,CACNA,EAAM/F,IAAIvB,gB,WAA3B4G,EAAAA,EAAAA,IAGYN,EAAA,C,MAH8BpB,KAAK,QAAQhD,KAAK,UAAUkB,KAAA,GAAMmD,QAAKJ,GAAE7D,EAAegF,EAAM/F,M,kBACtG,IAA+B,EAA/BiE,EAAAA,EAAAA,IAA+B+B,EAAA,M,iBAAtB,IAAY,EAAZ/B,EAAAA,EAAAA,KAAYgC,EAAAA,EAAAA,IAAAC,EAAAA,a,2BAAU,a,4CAGjCnC,EAAAA,EAAAA,IAAyC,OAAzCoC,EAAiC,Q,OAGrClC,EAAAA,EAAAA,IAIkByB,EAAA,CAJDE,KAAK,aAAalB,MAAM,OAAOiB,MAAM,O,CACzCG,SAAOX,EAAAA,EAAAA,IAAEY,GAAK,E,iBACpB/J,EAAW+J,EAAM/F,IAAIoG,aAAU,K,OAGtCnC,EAAAA,EAAAA,IAKkByB,EAAA,CALDhB,MAAM,KAAKiB,MAAM,MAAMU,MAAM,S,CACjCP,SAAOX,EAAAA,EAAAA,IAAEY,GAAK,EACvB9B,EAAAA,EAAAA,IAAoFc,EAAA,CAAzEpE,KAAK,UAAUgD,KAAK,QAASqB,QAAKJ,GAAE7E,EAAWgG,EAAM/F,M,kBAAM,IAAEiF,EAAA,MAAAA,EAAA,M,QAAF,S,gCACtEhB,EAAAA,EAAAA,IAAqFc,EAAA,CAA1EpE,KAAK,SAASgD,KAAK,QAASqB,QAAKJ,GAAEtE,EAAayF,EAAM/F,M,kBAAM,IAAEiF,EAAA,MAAAA,EAAA,M,QAAF,S,+DArBH7H,EAAA4B,UA0B1EoF,EAAAA,EAAAA,IAWM,MAXNkC,EAWM,EAVJrC,EAAAA,EAAAA,IASEsC,EAAA,CARAC,WAAA,GACAC,OAAO,0CACN,eAAcnJ,EAAA0B,MACd,aAAY,CAAC,GAAI,GAAI,GAAI,KACzB,YAAWzB,EAAAyB,MACXxB,MAAOA,EAAAwB,MACP0H,aAAa9C,EACb+C,gBAAgB9C,G,wDAMvBI,EAAAA,EAAAA,IA0CY2C,EAAA,CAzCTvI,MAAOX,EAAAsB,M,WACCvB,EAAAuB,M,qCAAAvB,EAAauB,MAAA4F,GACtBe,MAAM,S,CAiCKkB,QAAM1B,EAAAA,EAAAA,IACf,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHN0C,EAGM,EAFJ7C,EAAAA,EAAAA,IAAwDc,EAAA,CAA5CC,QAAKC,EAAA,KAAAA,EAAA,GAAAL,GAAEnH,EAAAuB,OAAgB,I,kBAAO,IAAEiG,EAAA,MAAAA,EAAA,M,QAAF,S,eAC1ChB,EAAAA,EAAAA,IAAkFc,EAAA,CAAvEpE,KAAK,UAAWqE,QAAOnC,EAAazF,QAASU,EAAAkB,O,kBAAY,IAAEiG,EAAA,MAAAA,EAAA,M,QAAF,S,iDAlCxE,IA8BU,EA9BVhB,EAAAA,EAAAA,IA8BUK,EAAA,CA9BAC,MAAOjG,EAAayI,MAAOrI,E,QAAiB,gBAAJrB,IAAIM,EAAgB,cAAY,Q,kBAChF,IAEe,EAFfsG,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,OAAOkB,KAAK,S,kBAC9B,IAAsE,EAAtE3B,EAAAA,EAAAA,IAAsEU,EAAA,C,WAAnDrG,EAAWD,M,qCAAXC,EAAWD,MAAKuG,GAAEC,YAAY,W,gCAGnDZ,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,OAAOkB,KAAK,e,kBAC9B,IAAqG,EAArG3B,EAAAA,EAAAA,IAAqGU,EAAA,CAA3FhE,KAAK,W,WAAoBrC,EAAWE,Y,qCAAXF,EAAWE,YAAWoG,GAAEC,YAAY,UAAUmC,KAAK,K,gCAGxF/C,EAAAA,EAAAA,IAoBeQ,EAAA,CApBDC,MAAM,QAAM,C,iBACxB,IAkBY,EAlBZT,EAAAA,EAAAA,IAkBYgD,EAAA,CAjBV9C,MAAM,kBACL+C,OAAQrJ,EAAAmB,MACRrD,QAASqC,EAAAgB,MACT,eAAcwD,EACd,aAAYa,EACZ,YAAWE,EACX,aAAYC,EACZ,gBAAeC,EACf0D,MAAO,EACP,YAAWvJ,EAAAoB,O,CAGDoI,KAAGjC,EAAAA,EAAAA,IACZ,IAEMF,EAAA,MAAAA,EAAA,MAFNb,EAAAA,EAAAA,IAEM,OAFDD,MAAM,kBAAiB,qCAE5B,M,iBAJF,IAA0C,EAA1CF,EAAAA,EAAAA,IAA0Cc,EAAA,CAA/BpE,KAAK,WAAS,C,iBAAC,IAAIsE,EAAA,MAAAA,EAAA,M,QAAJ,W,qICxFtC,MAAMoC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://ms/./src/views/courses/CourseList.vue", "webpack://ms/./src/views/courses/CourseList.vue?35b6"], "sourcesContent": ["<template>\r\n  <div class=\"course-list-container\">\r\n    <el-card class=\"filter-card\">\r\n      <div class=\"filter-container\">\r\n        <el-form :model=\"filterForm\" inline>\r\n          <el-form-item label=\"课程标题\">\r\n            <el-input v-model=\"filterForm.title\" placeholder=\"请输入课程标题\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button @click=\"resetFilter\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n    \r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>岗前培训课程</span>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddCourse\">新增课程</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table :data=\"courseList\" stripe border style=\"width: 100%\" v-loading=\"loading\">\r\n        <el-table-column type=\"index\" width=\"50\" />\r\n        <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"200\" />\r\n        <el-table-column prop=\"description\" label=\"课程描述\" min-width=\"300\" show-overflow-tooltip />\r\n        <el-table-column label=\"课件\" width=\"120\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button v-if=\"scope.row.material_path\" size=\"small\" type=\"success\" link @click=\"handleDownload(scope.row)\">\r\n              <el-icon><Download /></el-icon>\r\n              下载课件\r\n            </el-button>\r\n            <span v-else class=\"no-material\">无</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/编辑课程对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form :model=\"courseForm\" :rules=\"courseRules\" ref=\"courseFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"课程标题\" prop=\"title\">\r\n          <el-input v-model=\"courseForm.title\" placeholder=\"请输入课程标题\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程描述\" prop=\"description\">\r\n          <el-input type=\"textarea\" v-model=\"courseForm.description\" placeholder=\"请输入课程描述\" rows=\"4\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课件文件\">\r\n          <el-upload\r\n            class=\"material-upload\"\r\n            :action=\"uploadAction\"\r\n            :headers=\"uploadHeaders\"\r\n            :http-request=\"customUploadRequest\"\r\n            :on-preview=\"handlePreview\"\r\n            :on-remove=\"handleRemove\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :before-upload=\"beforeUpload\"\r\n            :limit=\"1\"\r\n            :file-list=\"fileList\"\r\n          >\r\n            <el-button type=\"primary\">点击上传</el-button>\r\n            <template #tip>\r\n              <div class=\"el-upload__tip\">\r\n                支持上传PDF、Word、PPT等格式文件，大小不超过10MB\r\n              </div>\r\n            </template>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Download } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\n// 直接定义API基础URL，而不是使用环境变量\r\nconst API_URL = 'http://localhost:3000'\r\n\r\n// 创建axios实例，配置默认headers\r\nconst http = axios.create({\r\n  baseURL: API_URL\r\n})\r\n\r\n// 添加请求拦截器，为每个请求添加token\r\nhttp.interceptors.request.use(config => {\r\n  // 从localStorage获取token\r\n  const token = localStorage.getItem('token')\r\n  if (token) {\r\n    config.headers.Authorization = `Bearer ${token}`\r\n  }\r\n  return config\r\n}, error => {\r\n  return Promise.reject(error)\r\n})\r\n\r\n// 日期格式化函数\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return '-'\r\n  const date = new Date(dateString)\r\n  if (isNaN(date.getTime())) return dateString\r\n  \r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  const seconds = String(date.getSeconds()).padStart(2, '0')\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n}\r\n\r\nconst loading = ref(true)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst dialogVisible = ref(false)\r\nconst dialogTitle = ref('新增课程')\r\nconst courseFormRef = ref(null)\r\nconst fileList = ref([])\r\nconst uploadAction = ref(`${API_URL}/api/courses`) // 实际的上传API地址\r\nconst submitting = ref(false)\r\nconst tempFile = ref(null) // 存储临时文件\r\n\r\n// 上传请求头，包含token\r\nconst uploadHeaders = computed(() => {\r\n  return {\r\n    Authorization: `Bearer ${localStorage.getItem('token') || ''}`\r\n  }\r\n})\r\n\r\n// 课程列表\r\nconst courseList = ref([])\r\n\r\n// 过滤条件\r\nconst filterForm = reactive({\r\n  title: ''\r\n})\r\n\r\n// 课程表单\r\nconst courseForm = reactive({\r\n  id: null,\r\n  title: '',\r\n  description: '',\r\n  material_path: ''\r\n})\r\n\r\n// 验证规则\r\nconst courseRules = {\r\n  title: [\r\n    { required: true, message: '请输入课程标题', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 获取课程列表数据\r\nconst fetchCourses = async () => {\r\n  loading.value = true\r\n  try {\r\n    // 处理分页\r\n    const response = await http.get('/api/courses')\r\n    if (response.data.success) {\r\n      courseList.value = response.data.data\r\n      total.value = response.data.count\r\n    } else {\r\n      ElMessage.error(response.data.message || '获取课程列表失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取课程列表失败:', error)\r\n    if (error.response && error.response.status === 401) {\r\n      ElMessage.error('登录已过期，请重新登录')\r\n      // 可以选择重定向到登录页面\r\n      // router.push('/login')\r\n    } else {\r\n      ElMessage.error('获取课程列表失败，请检查网络连接')\r\n    }\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  fetchCourses()\r\n})\r\n\r\n// 重置过滤条件\r\nconst resetFilter = () => {\r\n  filterForm.title = ''\r\n  fetchCourses()\r\n}\r\n\r\n// 搜索\r\nconst handleSearch = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await http.get('/api/courses/search', {\r\n      params: { keyword: filterForm.title }\r\n    })\r\n    if (response.data.success) {\r\n      courseList.value = response.data.data\r\n      total.value = response.data.count\r\n      ElMessage.success('搜索完成')\r\n    } else {\r\n      ElMessage.error(response.data.message || '搜索失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('搜索失败:', error)\r\n    ElMessage.error('搜索失败，请检查网络连接')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 新增课程\r\nconst handleAddCourse = () => {\r\n  dialogTitle.value = '新增课程'\r\n  dialogVisible.value = true\r\n  // 重置表单\r\n  courseForm.id = null\r\n  courseForm.title = ''\r\n  courseForm.description = ''\r\n  courseForm.material_path = ''\r\n  tempFile.value = null\r\n  fileList.value = []\r\n}\r\n\r\n// 编辑课程\r\nconst handleEdit = (row) => {\r\n  dialogTitle.value = '编辑课程'\r\n  dialogVisible.value = true\r\n  \r\n  // 填充表单数据\r\n  courseForm.id = row.id\r\n  courseForm.title = row.title\r\n  courseForm.description = row.description\r\n  courseForm.material_path = row.material_path\r\n  tempFile.value = null\r\n  \r\n  // 设置文件列表\r\n  fileList.value = []\r\n  if (row.material_path) {\r\n    fileList.value.push({\r\n      name: row.material_path.split('/').pop(),\r\n      url: `${API_URL}/${row.material_path}`\r\n    })\r\n  }\r\n}\r\n\r\n// 删除课程\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      const response = await http.delete(`/api/courses/${row.id}`)\r\n      if (response.data.success) {\r\n        ElMessage.success(`课程 ${row.title} 已删除`)\r\n        fetchCourses()\r\n      } else {\r\n        ElMessage.error(response.data.message || '删除课程失败')\r\n      }\r\n    } catch (error) {\r\n      console.error('删除课程失败:', error)\r\n      ElMessage.error('删除课程失败，请检查网络连接')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 下载课件\r\nconst handleDownload = async (row) => {\r\n  if (!row.material_path) {\r\n    ElMessage.warning('该课程没有上传课件')\r\n    return\r\n  }\r\n  \r\n  try {\r\n    ElMessage.success(`正在下载 ${row.title} 的课件`)\r\n    \r\n    // 使用axios发送请求并获取blob数据\r\n    const response = await http.get(`/api/courses/${row.id}/download`, {\r\n      responseType: 'blob' // 指定响应类型为blob\r\n    })\r\n    \r\n    // 获取文件名，从响应头获取\r\n    let filename = ''\r\n    const contentDisposition = response.headers['content-disposition']\r\n    if (contentDisposition) {\r\n      const filenameMatch = contentDisposition.match(/filename[^;=\\n]*=((['\"]).*?\\2|[^;\\n]*)/)\r\n      if (filenameMatch && filenameMatch[1]) {\r\n        filename = filenameMatch[1].replace(/['\"]/g, '')\r\n        try {\r\n          // 尝试解码文件名\r\n          filename = decodeURIComponent(filename)\r\n        } catch (e) {\r\n          console.error('解码文件名失败', e)\r\n          // 如果解码失败，使用原始文件名\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 如果没有从响应头获取到文件名，则使用路径中的文件名\r\n    if (!filename) {\r\n      filename = row.material_path.split('/').pop()\r\n    }\r\n    \r\n    // 创建blob链接\r\n    const blob = new Blob([response.data])\r\n    const url = window.URL.createObjectURL(blob)\r\n    \r\n    // 创建临时链接并模拟点击下载\r\n    const link = document.createElement('a')\r\n    link.href = url\r\n    link.setAttribute('download', filename)\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    \r\n    // 清理\r\n    document.body.removeChild(link)\r\n    window.URL.revokeObjectURL(url)\r\n    \r\n  } catch (error) {\r\n    console.error('下载失败:', error)\r\n    ElMessage.error('下载课件失败，请检查权限或网络连接')\r\n  }\r\n}\r\n\r\n// 自定义上传请求处理\r\nconst customUploadRequest = (options) => {\r\n  // 存储文件对象\r\n  tempFile.value = options.file\r\n  \r\n  // 修改文件名以解决中文编码问题\r\n  const originalFileName = options.file.name\r\n  // 显示成功消息，包含原始文件名\r\n  ElMessage.success(`文件 ${originalFileName} 已选择`)\r\n  options.onSuccess()\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!courseFormRef.value) return\r\n  \r\n  await courseFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      submitting.value = true\r\n      try {\r\n        const formData = new FormData()\r\n        formData.append('title', courseForm.title)\r\n        formData.append('description', courseForm.description)\r\n        \r\n        if (tempFile.value) {\r\n          // 添加原始文件名到formData以供后端使用\r\n          formData.append('originalFileName', tempFile.value.name)\r\n          formData.append('material', tempFile.value)\r\n        }\r\n        \r\n        let response\r\n        if (courseForm.id) {\r\n          // 编辑模式\r\n          response = await http.put(`/api/courses/${courseForm.id}`, formData)\r\n          if (response.data.success) {\r\n            ElMessage.success(`课程 ${courseForm.title} 信息已更新`)\r\n          } else {\r\n            ElMessage.error(response.data.message || '更新课程失败')\r\n          }\r\n        } else {\r\n          // 新增模式\r\n          response = await http.post('/api/courses', formData)\r\n          if (response.data.success) {\r\n            ElMessage.success(`课程 ${courseForm.title} 添加成功`)\r\n          } else {\r\n            ElMessage.error(response.data.message || '添加课程失败')\r\n          }\r\n        }\r\n        \r\n        dialogVisible.value = false\r\n        fetchCourses()\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error)\r\n        ElMessage.error('提交失败，请检查网络连接')\r\n      } finally {\r\n        submitting.value = false\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 文件上传相关方法\r\nconst handlePreview = (file) => {\r\n  if (file.url) {\r\n    window.open(file.url, '_blank')\r\n  }\r\n}\r\n\r\nconst handleRemove = () => {\r\n  fileList.value = []\r\n  tempFile.value = null\r\n}\r\n\r\nconst handleUploadSuccess = () => {\r\n  // 这个方法不再需要显示成功消息，因为在customUploadRequest中已处理\r\n}\r\n\r\nconst beforeUpload = (file) => {\r\n  // 文件大小限制：10MB\r\n  const maxSize = 10 * 1024 * 1024\r\n  if (file.size > maxSize) {\r\n    ElMessage.error('文件大小不能超过10MB')\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  fetchCourses()\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchCourses()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.course-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.no-material {\r\n  color: #909399;\r\n}\r\n\r\n.material-upload {\r\n  width: 100%;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n}\r\n\r\n:deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n}\r\n</style> ", "import script from \"./CourseList.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./CourseList.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./CourseList.vue?vue&type=style&index=0&id=981232ac&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-981232ac\"]])\n\nexport default __exports__"], "names": ["API_URL", "http", "axios", "create", "baseURL", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "formatDate", "dateString", "date", "Date", "isNaN", "getTime", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "loading", "ref", "currentPage", "pageSize", "total", "dialogVisible", "dialogTitle", "courseFormRef", "fileList", "uploadAction", "submitting", "tempFile", "uploadHeaders", "computed", "courseList", "filterForm", "reactive", "title", "courseForm", "id", "description", "material_path", "courseRules", "required", "message", "trigger", "fetchCourses", "async", "value", "response", "get", "data", "success", "count", "ElMessage", "console", "status", "onMounted", "resetFilter", "handleSearch", "params", "keyword", "handleAddCourse", "handleEdit", "row", "push", "name", "split", "pop", "url", "handleDelete", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "delete", "catch", "handleDownload", "responseType", "filename", "contentDisposition", "filenameMatch", "match", "replace", "decodeURIComponent", "e", "blob", "Blob", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "warning", "customUploadRequest", "options", "file", "originalFileName", "onSuccess", "submitForm", "validate", "valid", "formData", "FormData", "append", "put", "post", "handlePreview", "open", "handleRemove", "handleUploadSuccess", "beforeUpload", "maxSize", "size", "handleSizeChange", "handleCurrentChange", "page", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "class", "_createElementVNode", "_hoisted_2", "_component_el_form", "model", "inline", "_component_el_form_item", "label", "_component_el_input", "$event", "placeholder", "clearable", "_component_el_button", "onClick", "_cache", "header", "_withCtx", "_hoisted_3", "_createBlock", "_component_el_table", "stripe", "border", "style", "_component_el_table_column", "width", "prop", "align", "default", "scope", "_component_el_icon", "_unref", "Download", "_hoisted_4", "created_at", "fixed", "_hoisted_5", "_component_el_pagination", "background", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "footer", "_hoisted_6", "rules", "rows", "_component_el_upload", "action", "limit", "tip", "__exports__"], "sourceRoot": ""}
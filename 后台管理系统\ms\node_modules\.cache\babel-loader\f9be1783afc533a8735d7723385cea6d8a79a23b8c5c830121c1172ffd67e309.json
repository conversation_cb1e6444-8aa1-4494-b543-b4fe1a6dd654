{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, useSlots, inject, ref, computed, watch, reactive, toRefs, provide, onMounted, onBeforeUnmount, openBlock, createElementBlock, normalizeClass, unref, createVNode, withCtx, createBlock, resolveDynamicComponent, normalizeStyle, renderSlot, createTextVNode, toDisplayString, createCommentVNode, createElementVNode, TransitionGroup, nextTick } from 'vue';\nimport AsyncValidator from 'async-validator';\nimport { castArray, clone } from 'lodash-unified';\nimport { refDebounced } from '@vueuse/core';\nimport { formItemProps } from './form-item.mjs';\nimport FormLabelWrap from './form-label-wrap.mjs';\nimport { formContextKey, formItemContextKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { getProp } from '../../../utils/objects.mjs';\nimport { useFormSize } from './hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { isBoolean } from '../../../utils/types.mjs';\nimport { isArray, isFunction } from '@vue/shared';\nconst __default__ = defineComponent({\n  name: \"ElFormItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: formItemProps,\n  setup(__props, {\n    expose\n  }) {\n    const props = __props;\n    const slots = useSlots();\n    const formContext = inject(formContextKey, void 0);\n    const parentFormItemContext = inject(formItemContextKey, void 0);\n    const _size = useFormSize(void 0, {\n      formItem: false\n    });\n    const ns = useNamespace(\"form-item\");\n    const labelId = useId().value;\n    const inputIds = ref([]);\n    const validateState = ref(\"\");\n    const validateStateDebounced = refDebounced(validateState, 100);\n    const validateMessage = ref(\"\");\n    const formItemRef = ref();\n    let initialValue = void 0;\n    let isResettingField = false;\n    const labelPosition = computed(() => props.labelPosition || (formContext == null ? void 0 : formContext.labelPosition));\n    const labelStyle = computed(() => {\n      if (labelPosition.value === \"top\") {\n        return {};\n      }\n      const labelWidth = addUnit(props.labelWidth || (formContext == null ? void 0 : formContext.labelWidth) || \"\");\n      if (labelWidth) return {\n        width: labelWidth\n      };\n      return {};\n    });\n    const contentStyle = computed(() => {\n      if (labelPosition.value === \"top\" || (formContext == null ? void 0 : formContext.inline)) {\n        return {};\n      }\n      if (!props.label && !props.labelWidth && isNested) {\n        return {};\n      }\n      const labelWidth = addUnit(props.labelWidth || (formContext == null ? void 0 : formContext.labelWidth) || \"\");\n      if (!props.label && !slots.label) {\n        return {\n          marginLeft: labelWidth\n        };\n      }\n      return {};\n    });\n    const formItemClasses = computed(() => [ns.b(), ns.m(_size.value), ns.is(\"error\", validateState.value === \"error\"), ns.is(\"validating\", validateState.value === \"validating\"), ns.is(\"success\", validateState.value === \"success\"), ns.is(\"required\", isRequired.value || props.required), ns.is(\"no-asterisk\", formContext == null ? void 0 : formContext.hideRequiredAsterisk), (formContext == null ? void 0 : formContext.requireAsteriskPosition) === \"right\" ? \"asterisk-right\" : \"asterisk-left\", {\n      [ns.m(\"feedback\")]: formContext == null ? void 0 : formContext.statusIcon,\n      [ns.m(`label-${labelPosition.value}`)]: labelPosition.value\n    }]);\n    const _inlineMessage = computed(() => isBoolean(props.inlineMessage) ? props.inlineMessage : (formContext == null ? void 0 : formContext.inlineMessage) || false);\n    const validateClasses = computed(() => [ns.e(\"error\"), {\n      [ns.em(\"error\", \"inline\")]: _inlineMessage.value\n    }]);\n    const propString = computed(() => {\n      if (!props.prop) return \"\";\n      return isArray(props.prop) ? props.prop.join(\".\") : props.prop;\n    });\n    const hasLabel = computed(() => {\n      return !!(props.label || slots.label);\n    });\n    const labelFor = computed(() => {\n      var _a;\n      return (_a = props.for) != null ? _a : inputIds.value.length === 1 ? inputIds.value[0] : void 0;\n    });\n    const isGroup = computed(() => {\n      return !labelFor.value && hasLabel.value;\n    });\n    const isNested = !!parentFormItemContext;\n    const fieldValue = computed(() => {\n      const model = formContext == null ? void 0 : formContext.model;\n      if (!model || !props.prop) {\n        return;\n      }\n      return getProp(model, props.prop).value;\n    });\n    const normalizedRules = computed(() => {\n      const {\n        required\n      } = props;\n      const rules = [];\n      if (props.rules) {\n        rules.push(...castArray(props.rules));\n      }\n      const formRules = formContext == null ? void 0 : formContext.rules;\n      if (formRules && props.prop) {\n        const _rules = getProp(formRules, props.prop).value;\n        if (_rules) {\n          rules.push(...castArray(_rules));\n        }\n      }\n      if (required !== void 0) {\n        const requiredRules = rules.map((rule, i) => [rule, i]).filter(([rule]) => Object.keys(rule).includes(\"required\"));\n        if (requiredRules.length > 0) {\n          for (const [rule, i] of requiredRules) {\n            if (rule.required === required) continue;\n            rules[i] = {\n              ...rule,\n              required\n            };\n          }\n        } else {\n          rules.push({\n            required\n          });\n        }\n      }\n      return rules;\n    });\n    const validateEnabled = computed(() => normalizedRules.value.length > 0);\n    const getFilteredRule = trigger => {\n      const rules = normalizedRules.value;\n      return rules.filter(rule => {\n        if (!rule.trigger || !trigger) return true;\n        if (isArray(rule.trigger)) {\n          return rule.trigger.includes(trigger);\n        } else {\n          return rule.trigger === trigger;\n        }\n      }).map(({\n        trigger: trigger2,\n        ...rule\n      }) => rule);\n    };\n    const isRequired = computed(() => normalizedRules.value.some(rule => rule.required));\n    const shouldShowError = computed(() => {\n      var _a;\n      return validateStateDebounced.value === \"error\" && props.showMessage && ((_a = formContext == null ? void 0 : formContext.showMessage) != null ? _a : true);\n    });\n    const currentLabel = computed(() => `${props.label || \"\"}${(formContext == null ? void 0 : formContext.labelSuffix) || \"\"}`);\n    const setValidationState = state => {\n      validateState.value = state;\n    };\n    const onValidationFailed = error => {\n      var _a, _b;\n      const {\n        errors,\n        fields\n      } = error;\n      if (!errors || !fields) {\n        console.error(error);\n      }\n      setValidationState(\"error\");\n      validateMessage.value = errors ? (_b = (_a = errors == null ? void 0 : errors[0]) == null ? void 0 : _a.message) != null ? _b : `${props.prop} is required` : \"\";\n      formContext == null ? void 0 : formContext.emit(\"validate\", props.prop, false, validateMessage.value);\n    };\n    const onValidationSucceeded = () => {\n      setValidationState(\"success\");\n      formContext == null ? void 0 : formContext.emit(\"validate\", props.prop, true, \"\");\n    };\n    const doValidate = async rules => {\n      const modelName = propString.value;\n      const validator = new AsyncValidator({\n        [modelName]: rules\n      });\n      return validator.validate({\n        [modelName]: fieldValue.value\n      }, {\n        firstFields: true\n      }).then(() => {\n        onValidationSucceeded();\n        return true;\n      }).catch(err => {\n        onValidationFailed(err);\n        return Promise.reject(err);\n      });\n    };\n    const validate = async (trigger, callback) => {\n      if (isResettingField || !props.prop) {\n        return false;\n      }\n      const hasCallback = isFunction(callback);\n      if (!validateEnabled.value) {\n        callback == null ? void 0 : callback(false);\n        return false;\n      }\n      const rules = getFilteredRule(trigger);\n      if (rules.length === 0) {\n        callback == null ? void 0 : callback(true);\n        return true;\n      }\n      setValidationState(\"validating\");\n      return doValidate(rules).then(() => {\n        callback == null ? void 0 : callback(true);\n        return true;\n      }).catch(err => {\n        const {\n          fields\n        } = err;\n        callback == null ? void 0 : callback(false, fields);\n        return hasCallback ? false : Promise.reject(fields);\n      });\n    };\n    const clearValidate = () => {\n      setValidationState(\"\");\n      validateMessage.value = \"\";\n      isResettingField = false;\n    };\n    const resetField = async () => {\n      const model = formContext == null ? void 0 : formContext.model;\n      if (!model || !props.prop) return;\n      const computedValue = getProp(model, props.prop);\n      isResettingField = true;\n      computedValue.value = clone(initialValue);\n      await nextTick();\n      clearValidate();\n      isResettingField = false;\n    };\n    const addInputId = id => {\n      if (!inputIds.value.includes(id)) {\n        inputIds.value.push(id);\n      }\n    };\n    const removeInputId = id => {\n      inputIds.value = inputIds.value.filter(listId => listId !== id);\n    };\n    watch(() => props.error, val => {\n      validateMessage.value = val || \"\";\n      setValidationState(val ? \"error\" : \"\");\n    }, {\n      immediate: true\n    });\n    watch(() => props.validateStatus, val => setValidationState(val || \"\"));\n    const context = reactive({\n      ...toRefs(props),\n      $el: formItemRef,\n      size: _size,\n      validateMessage,\n      validateState,\n      labelId,\n      inputIds,\n      isGroup,\n      hasLabel,\n      fieldValue,\n      addInputId,\n      removeInputId,\n      resetField,\n      clearValidate,\n      validate,\n      propString\n    });\n    provide(formItemContextKey, context);\n    onMounted(() => {\n      if (props.prop) {\n        formContext == null ? void 0 : formContext.addField(context);\n        initialValue = clone(fieldValue.value);\n      }\n    });\n    onBeforeUnmount(() => {\n      formContext == null ? void 0 : formContext.removeField(context);\n    });\n    expose({\n      size: _size,\n      validateMessage,\n      validateState,\n      validate,\n      clearValidate,\n      resetField\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"formItemRef\",\n        ref: formItemRef,\n        class: normalizeClass(unref(formItemClasses)),\n        role: unref(isGroup) ? \"group\" : void 0,\n        \"aria-labelledby\": unref(isGroup) ? unref(labelId) : void 0\n      }, [createVNode(unref(FormLabelWrap), {\n        \"is-auto-width\": unref(labelStyle).width === \"auto\",\n        \"update-all\": ((_a = unref(formContext)) == null ? void 0 : _a.labelWidth) === \"auto\"\n      }, {\n        default: withCtx(() => [unref(hasLabel) ? (openBlock(), createBlock(resolveDynamicComponent(unref(labelFor) ? \"label\" : \"div\"), {\n          key: 0,\n          id: unref(labelId),\n          for: unref(labelFor),\n          class: normalizeClass(unref(ns).e(\"label\")),\n          style: normalizeStyle(unref(labelStyle))\n        }, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"label\", {\n            label: unref(currentLabel)\n          }, () => [createTextVNode(toDisplayString(unref(currentLabel)), 1)])]),\n          _: 3\n        }, 8, [\"id\", \"for\", \"class\", \"style\"])) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"is-auto-width\", \"update-all\"]), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"content\")),\n        style: normalizeStyle(unref(contentStyle))\n      }, [renderSlot(_ctx.$slots, \"default\"), createVNode(TransitionGroup, {\n        name: `${unref(ns).namespace.value}-zoom-in-top`\n      }, {\n        default: withCtx(() => [unref(shouldShowError) ? renderSlot(_ctx.$slots, \"error\", {\n          key: 0,\n          error: validateMessage.value\n        }, () => [createElementVNode(\"div\", {\n          class: normalizeClass(unref(validateClasses))\n        }, toDisplayString(validateMessage.value), 3)]) : createCommentVNode(\"v-if\", true)]),\n        _: 3\n      }, 8, [\"name\"])], 6)], 10, [\"role\", \"aria-labelledby\"]);\n    };\n  }\n});\nvar FormItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"form-item.vue\"]]);\nexport { FormItem as default };", "map": {"version": 3, "names": ["name", "slots", "useSlots", "formContext", "inject", "formContextKey", "parentFormItemContext", "formItemContextKey", "_size", "useFormSize", "formItem", "ns", "useNamespace", "labelId", "useId", "value", "inputIds", "ref", "validateState", "validateStateDebounced", "refDebounced", "validateMessage", "formItemRef", "initialValue", "isResettingField", "labelPosition", "computed", "props", "labelStyle", "labelWidth", "addUnit", "width", "contentStyle", "inline", "label", "isNested", "marginLeft", "formItemClasses", "b", "m", "is", "isRequired", "required", "hideRequiredAsterisk", "requireAsteriskPosition", "statusIcon", "_inlineMessage", "isBoolean", "inlineMessage", "validateClasses", "e", "em", "propString", "prop", "isArray", "join", "<PERSON><PERSON><PERSON><PERSON>", "labelFor", "_a", "for", "length", "isGroup", "fieldValue", "model", "getProp", "normalizedRules", "rules", "push", "<PERSON><PERSON><PERSON><PERSON>", "formRules", "_rules", "requiredRules", "map", "rule", "i", "filter", "Object", "keys", "includes", "validateEnabled", "getFilteredRule", "trigger", "trigger2", "some", "shouldShowError", "showMessage", "current<PERSON><PERSON><PERSON>", "labelSuffix", "setValidationState", "state", "onValidationFailed", "error", "_b", "errors", "fields", "console", "message", "emit", "onValidationSucceeded", "doValidate", "modelName", "validator", "AsyncValidator", "validate", "firstFields", "then", "catch", "err", "Promise", "reject", "callback", "<PERSON><PERSON><PERSON><PERSON>", "isFunction", "clearValidate", "reset<PERSON>ield", "computedValue", "clone", "nextTick", "addInputId", "id", "removeInputId", "listId", "watch", "val", "immediate", "validateStatus", "context", "reactive", "toRefs", "$el", "size", "provide", "onMounted", "addField", "onBeforeUnmount", "removeField", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "class", "normalizeClass", "unref", "role", "createVNode", "FormLabelWrap", "default", "withCtx", "createBlock", "resolveDynamicComponent", "key", "style", "normalizeStyle", "renderSlot", "$slots", "createTextVNode", "toDisplayString", "_", "createCommentVNode"], "sources": ["../../../../../../packages/components/form/src/form-item.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"formItemRef\"\n    :class=\"formItemClasses\"\n    :role=\"isGroup ? 'group' : undefined\"\n    :aria-labelledby=\"isGroup ? labelId : undefined\"\n  >\n    <form-label-wrap\n      :is-auto-width=\"labelStyle.width === 'auto'\"\n      :update-all=\"formContext?.labelWidth === 'auto'\"\n    >\n      <component\n        :is=\"labelFor ? 'label' : 'div'\"\n        v-if=\"hasLabel\"\n        :id=\"labelId\"\n        :for=\"labelFor\"\n        :class=\"ns.e('label')\"\n        :style=\"labelStyle\"\n      >\n        <slot name=\"label\" :label=\"currentLabel\">\n          {{ currentLabel }}\n        </slot>\n      </component>\n    </form-label-wrap>\n\n    <div :class=\"ns.e('content')\" :style=\"contentStyle\">\n      <slot />\n      <transition-group :name=\"`${ns.namespace.value}-zoom-in-top`\">\n        <slot v-if=\"shouldShowError\" name=\"error\" :error=\"validateMessage\">\n          <div :class=\"validateClasses\">\n            {{ validateMessage }}\n          </div>\n        </slot>\n      </transition-group>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n  useSlots,\n  watch,\n} from 'vue'\nimport AsyncValidator from 'async-validator'\nimport { clone } from 'lodash-unified'\nimport { refDebounced } from '@vueuse/core'\nimport {\n  addUnit,\n  ensureArray,\n  getProp,\n  isArray,\n  isBoolean,\n  isFunction,\n} from '@element-plus/utils'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from './hooks'\nimport { formItemProps } from './form-item'\nimport FormLabelWrap from './form-label-wrap'\nimport { formContextKey, formItemContextKey } from './constants'\n\nimport type { CSSProperties } from 'vue'\nimport type { RuleItem } from 'async-validator'\nimport type { Arrayable } from '@element-plus/utils'\nimport type {\n  FormItemContext,\n  FormItemRule,\n  FormValidateFailure,\n} from './types'\nimport type { FormItemValidateState } from './form-item'\n\ndefineOptions({\n  name: 'ElFormItem',\n})\nconst props = defineProps(formItemProps)\nconst slots = useSlots()\n\nconst formContext = inject(formContextKey, undefined)\nconst parentFormItemContext = inject(formItemContextKey, undefined)\n\nconst _size = useFormSize(undefined, { formItem: false })\nconst ns = useNamespace('form-item')\n\nconst labelId = useId().value\nconst inputIds = ref<string[]>([])\n\nconst validateState = ref<FormItemValidateState>('')\nconst validateStateDebounced = refDebounced(validateState, 100)\nconst validateMessage = ref('')\nconst formItemRef = ref<HTMLDivElement>()\n// special inline value.\nlet initialValue: any = undefined\nlet isResettingField = false\n\nconst labelPosition = computed(\n  () => props.labelPosition || formContext?.labelPosition\n)\n\nconst labelStyle = computed<CSSProperties>(() => {\n  if (labelPosition.value === 'top') {\n    return {}\n  }\n\n  const labelWidth = addUnit(props.labelWidth || formContext?.labelWidth || '')\n  if (labelWidth) return { width: labelWidth }\n  return {}\n})\n\nconst contentStyle = computed<CSSProperties>(() => {\n  if (labelPosition.value === 'top' || formContext?.inline) {\n    return {}\n  }\n  if (!props.label && !props.labelWidth && isNested) {\n    return {}\n  }\n  const labelWidth = addUnit(props.labelWidth || formContext?.labelWidth || '')\n  if (!props.label && !slots.label) {\n    return { marginLeft: labelWidth }\n  }\n  return {}\n})\n\nconst formItemClasses = computed(() => [\n  ns.b(),\n  ns.m(_size.value),\n  ns.is('error', validateState.value === 'error'),\n  ns.is('validating', validateState.value === 'validating'),\n  ns.is('success', validateState.value === 'success'),\n  ns.is('required', isRequired.value || props.required),\n  ns.is('no-asterisk', formContext?.hideRequiredAsterisk),\n  formContext?.requireAsteriskPosition === 'right'\n    ? 'asterisk-right'\n    : 'asterisk-left',\n  {\n    [ns.m('feedback')]: formContext?.statusIcon,\n    [ns.m(`label-${labelPosition.value}`)]: labelPosition.value,\n  },\n])\n\nconst _inlineMessage = computed(() =>\n  isBoolean(props.inlineMessage)\n    ? props.inlineMessage\n    : formContext?.inlineMessage || false\n)\n\nconst validateClasses = computed(() => [\n  ns.e('error'),\n  { [ns.em('error', 'inline')]: _inlineMessage.value },\n])\n\nconst propString = computed(() => {\n  if (!props.prop) return ''\n  return isArray(props.prop) ? props.prop.join('.') : props.prop\n})\n\nconst hasLabel = computed<boolean>(() => {\n  return !!(props.label || slots.label)\n})\n\nconst labelFor = computed<string | undefined>(() => {\n  return (\n    props.for ?? (inputIds.value.length === 1 ? inputIds.value[0] : undefined)\n  )\n})\n\nconst isGroup = computed<boolean>(() => {\n  return !labelFor.value && hasLabel.value\n})\n\nconst isNested = !!parentFormItemContext\n\nconst fieldValue = computed(() => {\n  const model = formContext?.model\n  if (!model || !props.prop) {\n    return\n  }\n  return getProp(model, props.prop).value\n})\n\nconst normalizedRules = computed(() => {\n  const { required } = props\n\n  const rules: FormItemRule[] = []\n\n  if (props.rules) {\n    rules.push(...ensureArray(props.rules))\n  }\n\n  const formRules = formContext?.rules\n  if (formRules && props.prop) {\n    const _rules = getProp<Arrayable<FormItemRule> | undefined>(\n      formRules,\n      props.prop\n    ).value\n    if (_rules) {\n      rules.push(...ensureArray(_rules))\n    }\n  }\n\n  if (required !== undefined) {\n    const requiredRules = rules\n      .map((rule, i) => [rule, i] as const)\n      .filter(([rule]) => Object.keys(rule).includes('required'))\n\n    if (requiredRules.length > 0) {\n      for (const [rule, i] of requiredRules) {\n        if (rule.required === required) continue\n        rules[i] = { ...rule, required }\n      }\n    } else {\n      rules.push({ required })\n    }\n  }\n\n  return rules\n})\n\nconst validateEnabled = computed(() => normalizedRules.value.length > 0)\n\nconst getFilteredRule = (trigger: string) => {\n  const rules = normalizedRules.value\n  return (\n    rules\n      .filter((rule) => {\n        if (!rule.trigger || !trigger) return true\n        if (isArray(rule.trigger)) {\n          return rule.trigger.includes(trigger)\n        } else {\n          return rule.trigger === trigger\n        }\n      })\n      // exclude trigger\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      .map(({ trigger, ...rule }): RuleItem => rule)\n  )\n}\n\nconst isRequired = computed(() =>\n  normalizedRules.value.some((rule) => rule.required)\n)\n\nconst shouldShowError = computed(\n  () =>\n    validateStateDebounced.value === 'error' &&\n    props.showMessage &&\n    (formContext?.showMessage ?? true)\n)\n\nconst currentLabel = computed(\n  () => `${props.label || ''}${formContext?.labelSuffix || ''}`\n)\n\nconst setValidationState = (state: FormItemValidateState) => {\n  validateState.value = state\n}\n\nconst onValidationFailed = (error: FormValidateFailure) => {\n  const { errors, fields } = error\n  if (!errors || !fields) {\n    console.error(error)\n  }\n\n  setValidationState('error')\n  validateMessage.value = errors\n    ? errors?.[0]?.message ?? `${props.prop} is required`\n    : ''\n\n  formContext?.emit('validate', props.prop!, false, validateMessage.value)\n}\n\nconst onValidationSucceeded = () => {\n  setValidationState('success')\n  formContext?.emit('validate', props.prop!, true, '')\n}\n\nconst doValidate = async (rules: RuleItem[]): Promise<true> => {\n  const modelName = propString.value\n  const validator = new AsyncValidator({\n    [modelName]: rules,\n  })\n  return validator\n    .validate({ [modelName]: fieldValue.value }, { firstFields: true })\n    .then(() => {\n      onValidationSucceeded()\n      return true as const\n    })\n    .catch((err: FormValidateFailure) => {\n      onValidationFailed(err)\n      return Promise.reject(err)\n    })\n}\n\nconst validate: FormItemContext['validate'] = async (trigger, callback) => {\n  // skip validation if its resetting\n  if (isResettingField || !props.prop) {\n    return false\n  }\n\n  const hasCallback = isFunction(callback)\n  if (!validateEnabled.value) {\n    callback?.(false)\n    return false\n  }\n\n  const rules = getFilteredRule(trigger)\n  if (rules.length === 0) {\n    callback?.(true)\n    return true\n  }\n\n  setValidationState('validating')\n\n  return doValidate(rules)\n    .then(() => {\n      callback?.(true)\n      return true as const\n    })\n    .catch((err: FormValidateFailure) => {\n      const { fields } = err\n      callback?.(false, fields)\n      return hasCallback ? false : Promise.reject(fields)\n    })\n}\n\nconst clearValidate: FormItemContext['clearValidate'] = () => {\n  setValidationState('')\n  validateMessage.value = ''\n  isResettingField = false\n}\n\nconst resetField: FormItemContext['resetField'] = async () => {\n  const model = formContext?.model\n  if (!model || !props.prop) return\n\n  const computedValue = getProp(model, props.prop)\n\n  // prevent validation from being triggered\n  isResettingField = true\n\n  computedValue.value = clone(initialValue)\n\n  await nextTick()\n  clearValidate()\n\n  isResettingField = false\n}\n\nconst addInputId: FormItemContext['addInputId'] = (id: string) => {\n  if (!inputIds.value.includes(id)) {\n    inputIds.value.push(id)\n  }\n}\n\nconst removeInputId: FormItemContext['removeInputId'] = (id: string) => {\n  inputIds.value = inputIds.value.filter((listId) => listId !== id)\n}\n\nwatch(\n  () => props.error,\n  (val) => {\n    validateMessage.value = val || ''\n    setValidationState(val ? 'error' : '')\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.validateStatus,\n  (val) => setValidationState(val || '')\n)\n\nconst context: FormItemContext = reactive({\n  ...toRefs(props),\n  $el: formItemRef,\n  size: _size,\n  validateMessage,\n  validateState,\n  labelId,\n  inputIds,\n  isGroup,\n  hasLabel,\n  fieldValue,\n  addInputId,\n  removeInputId,\n  resetField,\n  clearValidate,\n  validate,\n  propString,\n})\n\nprovide(formItemContextKey, context)\n\nonMounted(() => {\n  if (props.prop) {\n    formContext?.addField(context)\n    initialValue = clone(fieldValue.value)\n  }\n})\n\nonBeforeUnmount(() => {\n  formContext?.removeField(context)\n})\n\ndefineExpose({\n  /**\n   * @description Form item size.\n   */\n  size: _size,\n  /**\n   * @description Validation message.\n   */\n  validateMessage,\n  /**\n   * @description Validation state.\n   */\n  validateState,\n  /**\n   * @description Validate form item.\n   */\n  validate,\n  /**\n   * @description Remove validation status of the field.\n   */\n  clearValidate,\n  /**\n   * @description Reset current field and remove validation result.\n   */\n  resetField,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;mCA+Ec;EACZA,IAAM;AACR;;;;;;;;IAEA,MAAMC,KAAA,GAAQC,QAAS;IAEjB,MAAAC,WAAA,GAAcC,MAAO,CAAAC,cAAA,EAAgB,KAAS;IAC9C,MAAAC,qBAAA,GAAwBF,MAAO,CAAAG,kBAAA,EAAoB,KAAS;IAElE,MAAMC,KAAA,GAAQC,WAAY,SAAW;MAAEC,QAAA,EAAU;IAAA,CAAO;IAClD,MAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IAE7B,MAAAC,OAAA,GAAUC,KAAA,EAAQ,CAAAC,KAAA;IAClB,MAAAC,QAAA,GAAWC,GAAc,GAAE;IAE3B,MAAAC,aAAA,GAAgBD,GAAA,CAA2B,EAAE;IAC7C,MAAAE,sBAAA,GAAyBC,YAAa,CAAAF,aAAA,EAAe,GAAG;IACxD,MAAAG,eAAA,GAAkBJ,GAAA,CAAI,EAAE;IAC9B,MAAMK,WAAA,GAAcL,GAAoB;IAExC,IAAIM,YAAoB;IACxB,IAAIC,gBAAmB;IAEvB,MAAMC,aAAgB,GAAAC,QAAA,OAAAC,KAAA,CAAAF,aAAA,KAAAtB,WAAA,oBAAAA,WAAA,CAAAsB,aAAA;IACpB,MAAAG,UAAY,GAAAF,QAAA,OAA8B;MAC5C,IAAAD,aAAA,CAAAV,KAAA;QAEM;MACJ;MACE,MAAAc,UAAQ,GAAAC,OAAA,CAAAH,KAAA,CAAAE,UAAA,KAAA1B,WAAA,oBAAAA,WAAA,CAAA0B,UAAA;MACV,IAAAA,UAAA,EAEA;QAAAE,KAAA,EAAAF;MAA2B;MAC3B,OAAgB;IAChB;IACF,MAACG,YAAA,GAAAN,QAAA;MAEK,IAAAD,aAAA,CAAAV,KAAA,KAAuC,KAAM,KAAAZ,WAAA,oBAAAA,WAAA,CAAA8B,MAAA;QACjD,OAAkB;MAChB;MACF,KAAAN,KAAA,CAAAO,KAAA,KAAAP,KAAA,CAAAE,UAAA,IAAAM,QAAA;QACA,OAAW;MACT;MACF,MAAAN,UAAA,GAAAC,OAAA,CAAAH,KAAA,CAAAE,UAAA,KAAA1B,WAAA,oBAAAA,WAAA,CAAA0B,UAAA;MACA,KAAAF,KAAA,CAAAO,KAAA,IAA2B,CAAAjC,KAAA,CAAAiC,KAAA,EAAoB;QAC/C,OAAK;UAAME,UAAU,EAAAP;QAAa;MAChC;MACF;IACA;IACF,MAACQ,eAAA,GAAAX,QAAA,QAEKf,EAAA,CAAA2B,CAAA,IACJ3B,EAAA,CAAG4B,CAAE,CAAA/B,KAAA,CAAAO,KAAA,GACLJ,EAAA,CAAG6B,EAAE,QAAW,EAAAtB,aAAA,CAAAH,KAAA,eAChBJ,EAAG,CAAA6B,EAAA,CAAG,YAAS,EAAAtB,aAAA,CAAAH,KAAwB,KAAO,eAC9CJ,EAAG,CAAA6B,EAAA,CAAG,SAAc,EAAAtB,aAAA,CAAAH,KAAA,cAAoC,GACxDJ,EAAG,CAAA6B,EAAA,CAAG,UAAW,EAAAC,UAAA,CAAA1B,KAAA,IAAAY,KAAiC,CAAAe,QAAA,GAClD/B,EAAA,CAAG6B,EAAG,cAAuB,EAAArC,WAAA,gBAAuB,IAAAA,WAAA,CAAAwC,oBAAA,GACpD,CAAGxC,WAAkB,oBAAAA,WAAiC,CAAAyC,uBAAA,oDACtD;MAGA,CAAAjC,EAAA,CAAA4B,CAAA,eAAApC,WAAA,oBAAAA,WAAA,CAAA0C,UAAA;MACE,CAAClC,EAAG,CAAA4B,CAAA,CAAE,SAAUd,aAAiB,CAAAV,KAAA,MAAAU,aAAA,CAAAV;IAAA,CACjC,CACF;IACF,MAAC+B,cAAA,GAAApB,QAAA,OAAAqB,SAAA,CAAApB,KAAA,CAAAqB,aAAA,IAAArB,KAAA,CAAAqB,aAAA,IAAA7C,WAAA,oBAAAA,WAAA,CAAA6C,aAAA;IAED,MAAMC,eAAiB,GAAAvB,QAAA,QAASf,EAAA,CAAAuC,CAAA,WAIhC;MAAA,CAAAvC,EAAA,CAAAwC,EAAA,sBAAAL,cAAA,CAAA/B;IAAA,EAEM;IACJ,MAAAqC,UAAY,GAAA1B,QAAA;MACZ,IAAM,CAAAC,KAAA,CAAA0B,IAAA,EACP;MAEK,OAAAC,OAAA,CAAA3B,KAAA,CAAA0B,IAAA,CAAsB,GAAM1B,KAAA,CAAA0B,IAAA,CAAAE,IAAA,QAAA5B,KAAA,CAAA0B,IAAA;IAChC,CAAI;IACG,MAAAG,QAAA,GAAQ9B,QAAU;MAC1B,UAAAC,KAAA,CAAAO,KAAA,IAAAjC,KAAA,CAAAiC,KAAA;IAED,CAAM;IACJ,MAAAuB,QAAgB,GAAA/B,QAAA,OAAe;MAChC,IAAAgC,EAAA;MAEK,QAAAA,EAAA,GAAA/B,KAAA,CAAAgC,GAAA,KAA8C,OAAAD,EAAA,GAAA1C,QAAA,CAAAD,KAAA,CAAA6C,MAAA,SAAA5C,QAAA,CAAAD,KAAA;IAClD,CACE;IAEJ,MAAC8C,OAAA,GAAAnC,QAAA;MAEK,QAAA+B,QAAA,CAAA1C,KAAA,IAAkCyC,QAAA,CAAAzC,KAAA;IACtC,CAAO;IACT,MAACoB,QAAA,KAAA7B,qBAAA;IAEK,MAAAwD,UAAA,GAAapC,QAAA;MAEb,MAAAqC,KAAA,GAAA5D,WAAA,IAA4B,gBAAAA,WAAA,CAAA4D,KAAA;MAChC,KAAAA,KAAA,IAAc,CAAapC,KAAA,CAAA0B,IAAA;QAC3B;MACE;MACF,OAAAW,OAAA,CAAAD,KAAA,EAAApC,KAAA,CAAA0B,IAAA,EAAAtC,KAAA;IACA;IACF,MAACkD,eAAA,GAAAvC,QAAA;MAEK;QAAAgB;MAAA,IAAAf,KAAA;MACE,MAAAuC,KAAA;MAEN,IAAAvC,KAAA,CAAAuC,KAA+B;QAE/BA,KAAA,CAAAC,IAAiB,IAAAC,SAAA,CAAAzC,KAAA,CAAAuC,KAAA;MACf;MACF,MAAAG,SAAA,GAAAlE,WAAA,oBAAAA,WAAA,CAAA+D,KAAA;MAEA,IAAAG,SAAA,IAAA1C,KAA+B,CAAA0B,IAAA;QAC3B,MAAAiB,MAAA,GAAaN,OAAY,CAAAK,SAAA,EAAA1C,KAAA,CAAA0B,IAAA,EAAAtC,KAAA;QAC3B,IAAAuD,MAAe;UACbJ,KAAA,CAAAC,IAAA,IAAAC,SAAA,CAAAE,MAAA;QAAA;MACM;MAER,IAAA5B,QAAY;QACV,MAAA6B,aAA0B,GAAAL,KAAA,CAAAM,GAAA,EAAAC,IAAO,EAAAC,CAAA,MAAAD,IAAA,EAAAC,CAAA,GAAAC,MAAA,GAAAF,IAAA,MAAAG,MAAA,CAAAC,IAAA,CAAAJ,IAAA,EAAAK,QAAA;QACnC,IAAAP,aAAA,CAAAX,MAAA;UACF,YAAAa,IAAA,EAAAC,CAAA,KAAAH,aAAA;YAEA,IAAAE,IAAA,CAAA/B,QAA4B,KAAAA,QAAA,EACpB;YAIFwB,KAAA,CAAAQ,CAAA;cAAA,GAAAD,IAAA;cAAA/B;YAA0B;UAC5B;QACE,CAAI;UACJwB,KAAA,CAAAC,IAAO;YAAIzB;UAAA,EAAW;QAAS;MACjC;MAEM,OAAAwB,KAAA;IAAiB,CACzB;IACF,MAAAa,eAAA,GAAArD,QAAA,OAAAuC,eAAA,CAAAlD,KAAA,CAAA6C,MAAA;IAEO,MAAAoB,eAAA,GAAAC,OAAA;MACR,MAAAf,KAAA,GAAAD,eAAA,CAAAlD,KAAA;MAED,OAAAmD,KAAA,CAAAS,MAAA,CAAAF,IAAiC;QAE3B,KAAAA,IAAA,CAAAQ,OAAA,IAAmB,CAAoBA,OAAA,EAC3C,WAA8B;QAE5B,IAAA3B,OACG,CAAOmB,IAAA,CAAAQ,OAAU;UAChB,OAAKR,IAAgB,CAAAQ,OAAA,CAACH,QAAS,CAAOG,OAAA;QACtC,CAAI;UACK,OAAAR,IAAA,CAAKQ,OAAQ,KAAAA,OAAgB;QAAA;MAEpC,GAAAT,GAAA;QAAAS,OAAwB,EAAAC,QAAA;QAAA,GAAAT;MAAA,MAAAA,IAAA;IAAA,CAC1B;IACF,MAGChC,UAAA,GAAAf,QAAgB,OAAGuC,eAAyB,CAAAlD,KAAA,CAAAoE,IAAA,CAAAV,IAAA,IAAAA,IAAA,CAAA/B,QAAA;IAEnD,MAAA0C,eAAA,GAAA1D,QAAA;MAEA,IAAMgC,EAAa;MAAS,OACVvC,sBAAA,CAAAJ,KAAY,gBAAsBY,KAAA,CAAA0D,WAAA,MAAA3B,EAAA,GAAAvD,WAAA,oBAAAA,WAAA,CAAAkF,WAAA,YAAA3B,EAAA;IAAA,CACpD;IAEA,MAAM4B,YAAkB,GAAA5D,QAAA,UAAAC,KAAA,CAAAO,KAAA,UAAA/B,WAAA,oBAAAA,WAAA,CAAAoF,WAAA;IAAA,MAAAC,kBAEG,GAAAC,KAAA;MAG3BvE,aAAA,CAAAH,KAAA,GAAA0E,KAAA;IAEA;IACE,MAAAC,kBAAe,GAAWC,KAAG;MAC/B,IAAAjC,EAAA,EAAAkC,EAAA;MAEM;QAAAC,MAAA;QAAAC;MAAA,IAAuDH,KAAA;MAC3D,KAAAE,MAAA,IAAsB,CAAAC,MAAA;QACxBC,OAAA,CAAAJ,KAAA,CAAAA,KAAA;MAEA;MACQH,kBAAU,QAAW;MACvBnE,eAAW,CAACN,KAAQ,GAAA8E,MAAA,IAAAD,EAAA,IAAAlC,EAAA,GAAAmC,MAAA,oBAAAA,MAAA,wBAAAnC,EAAA,CAAAsC,OAAA,YAAAJ,EAAA,MAAAjE,KAAA,CAAA0B,IAAA;MACtBlD,WAAA,QAAmB,YAAAA,WAAA,CAAA8F,IAAA,aAAAtE,KAAA,CAAA0B,IAAA,SAAAhC,eAAA,CAAAN,KAAA;IAAA,CACrB;IAEA,MAAAmF,qBAA0B,GAAAA,CAAA;MACVV,kBAAA;MAIhBrF,WAAA,QAA8B,YAAAA,WAAa,CAAA8F,IAAA,aAAAtE,KAAuB,CAAK0B,IAAA;IAAA,CACzE;IAEA,MAAM8C,UAAA,SAAAjC,KAA8B;MAClC,MAAAkC,SAAA,GAAAhD,UAA4B,CAAArC,KAAA;MAC5B,MAAAsF,SAAkB,OAAAC,cAAkB;QACtC,CAAAF,SAAA,GAAAlC;MAEA,CAAM;MACJ,OAAAmC,SAAA,CAAAE,QAA6B;QAAA,CAAAH,SAAA,GAAAtC,UAAA,CAAA/C;MAAA;QAAAyF,WAAA;MAAA,GAAAC,IAAA;QACvBP,qBAA+B;QACnC,WAAa;MAAA,CACd,EAAAQ,KAAA,CAAAC,GAAA;QACDjB,kBACG,CAAAiB,GAAA;QAEuB,OAAAC,OAAA,CAAAC,MAAA,CAAAF,GAAA;MACtB,CAAO;IAAA;IAGP,MAAAJ,QAAA,SAAAA,CAAsBtB,OAAA,EAAA6B,QAAA;MACf,IAAAtF,gBAAA,IAAe,CAAGG,KAAA,CAAA0B,IAAA;QAC1B;MAAA;MAGC,MAAA0D,WAA+C,GAAAC,UAAA,CAASF,QAAa;MAErE,KAAA/B,eAAA,CAAAhE,KAAqB;QAChB+F,QAAA,oBAAAA,QAAA;QACT;MAEA;MACI,MAAA5C,KAAA,GAAAc,eAAwB,CAAAC,OAAA;MAC1B,IAAAf,KAAA,CAAAN,MAAgB;QACTkD,QAAA,oBAAAA,QAAA;QACT;MAEA;MACItB,kBAAA,aAAoB;MACtB,OAAAW,UAAe,CAAAjC,KAAA,EAAAuC,IAAA;QACRK,QAAA,oBAAAA,QAAA;QACT;MAEA,GAAAJ,KAAA,CAAAC,GAAA;QAEA,MAAkB;UAAAb;QAAA,IAAAa,GACf;QACCG,QAAA,IAAe,gBAAAA,QAAA,QAAAhB,MAAA;QACR,OAAAiB,WAAA,WAAAH,OAAA,CAAAC,MAAA,CAAAf,MAAA;MAAA,CACR;IAEC,CAAM;IACN,MAAAmB,aAAA,GAAkBA,CAAA,KAAM;MACxBzB,kBAAqB;MACvBnE,eAAC,CAAAN,KAAA;MACLS,gBAAA;IAEA;IACE,MAAA0F,UAAA,SAAAA,CAAA,KAAqB;MACrB,MAAAnD,KAAA,GAAA5D,WAAwB,oBAAAA,WAAA,CAAA4D,KAAA;MACL,KAAAA,KAAA,KAAApC,KAAA,CAAA0B,IAAA,EACrB;MAEA,MAAA8D,aAA8D,GAAAnD,OAAA,CAAAD,KAAA,EAAApC,KAAA,CAAA0B,IAAA;MAC5D7B,gBAA2B;MAC3B2F,aAAK,CAASpG,KAAC,GAAYqG,KAAA,CAAA7F,YAAA;MAE3B,MAAM8F,QAAgB;MAGHJ,aAAA;MAELzF,gBAAA;IAEd;IACc,MAAA8F,UAAA,GAAAC,EAAA;MAEK,KAAAvG,QAAA,CAAAD,KAAA,CAAA+D,QAAA,CAAAyC,EAAA;QACrBvG,QAAA,CAAAD,KAAA,CAAAoD,IAAA,CAAAoD,EAAA;MAEA;IACE;IACW,MAAAC,aAAM,GAAAD,EAAO;MACxBvG,QAAA,CAAAD,KAAA,GAAAC,QAAA,CAAAD,KAAA,CAAA4D,MAAA,CAAA8C,MAAA,IAAAA,MAAA,KAAAF,EAAA;IAAA,CACF;IAEMG,KAAA,OAAA/F,KAAA,CAAAgE,KAAmD,EAAegC,GAAA;MACtEtG,eAAA,CAAAN,KAA0B,GAAA4G,GAAA;MAC5BnC,kBAAA,CAAAmC,GAAA;IAEA;MAAAC,SAAA;IAAA;IAAAF,KAAA,OACc/F,KAAA,CAAAkG,cAAA,EAAAF,GAAA,IAAAnC,kBAAA,CAAAmC,GAAA;IAAA,MACHG,OAAA,GAAAC,QAAA;MACP,GAAAC,MAAA,CAAArG,KAAA;MACmBsG,GAAA,EAAA3G,WAAA;MACrB4G,IAAA,EAAA1H,KAAA;MACAa,eAAkB;MACpBH,aAAA;MAEAL,OAAA;MACEG,QAAY;MACZ6C,OAAC;MACHL,QAAA;MAEAM,UAAA;MACEwD,UAAU;MACVE,aAAK;MACLN,UAAM;MACND,aAAA;MACAV,QAAA;MACAnD;IAAA,CACA;IACA+E,OAAA,CAAA5H,kBAAA,EAAAuH,OAAA;IACAM,SAAA;MACA,IAAAzG,KAAA,CAAA0B,IAAA;QACAlD,WAAA,oBAAAA,WAAA,CAAAkI,QAAA,CAAAP,OAAA;QACAvG,YAAA,GAAA6F,KAAA,CAAAtD,UAAA,CAAA/C,KAAA;MAAA;IACA,CACA;IACAuH,eAAA;MACAnI,WAAA,oBAAAA,WAAA,CAAAoI,WAAA,CAAAT,OAAA;IAAA,CACD;IAEDU,MAAA;MAEAN,IAAA,EAAA1H,KAAgB;MACda,eAAgB;MACdH,aAAA;MACeqF,QAAA;MACjBU,aAAA;MACDC;IAED;IACE,QAAAuB,IAAA,EAAAC,MAAA;MACD,IAAAhF,EAAA;MAEY,OAAAiF,SAAA,IAAAC,kBAAA;QAAAC,OAAA;QAAA5H,GAAA,EAAAK,WAAA;QAAAwH,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA3G,eAAA;QAIL4G,IAAA,EAAAD,KAAA,CAAAnF,OAAA;QAAA,mBAAAmF,KAAA,CAAAnF,OAAA,IAAAmF,KAAA,CAAAnI,OAAA;MAAA,IAAAqI,WAAA,CAAAF,KAAA,CAAAG,aAAA;QAIN,iBAAAH,KAAA,CAAApH,UAAA,EAAAG,KAAA;QAAA,gBAAA2B,EAAA,GAAAsF,KAAA,CAAA7I,WAAA,sBAAAuD,EAAA,CAAA7B,UAAA;MAAA;QAAAuH,OAAA,EAAAC,OAAA,QAIAL,KAAA,CAAAxF,QAAA,KAAAmF,SAAA,IAAAW,WAAA,CAAAC,uBAAA,CAAAP,KAAA,CAAAvF,QAAA;UAAA+F,GAAA;UAAAjC,EAAA,EAAAyB,KAAA,CAAAnI,OAAA;UAAA8C,GAAA,EAAAqF,KAAA,CAAAvF,QAAA;UAIAqF,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAArI,EAAA,EAAAuC,CAAA;UAAAuG,KAAA,EAAAC,cAAA,CAAAV,KAAA,CAAApH,UAAA;QAAA;UAAAwH,OAAA,EAAAC,OAAA,QAIAM,UAAA,CAAAlB,IAAA,CAAAmB,MAAA;YAAA1H,KAAA,EAAA8G,KAAA,CAAA1D,YAAA;UAAA,UAAAuE,eAAA,CAAAC,eAAA,CAAAd,KAAA,CAAA1D,YAAA;UAIAyE,CAAA;QAAA,CACD,yCAAAC,kBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
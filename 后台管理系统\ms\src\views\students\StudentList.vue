<template>
  <div class="student-list-container">
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="姓名">
            <el-input v-model="searchForm.name" placeholder="搜索实习生姓名" clearable>
            </el-input>
          </el-form-item>
          <el-form-item label="学校">
            <el-input v-model="searchForm.school" placeholder="搜索实习生学校" clearable>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>实习生列表</span>
          <div>
            <el-button type="success" @click="handleImport">
              <el-icon><Upload /></el-icon> 批量导入
            </el-button>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 添加实习生
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="tableData"
        stripe
        border
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" min-width="60" />
        <el-table-column prop="name" label="姓名" min-width="100" />
        <el-table-column prop="gender" label="性别" min-width="60" />
        <el-table-column prop="phone" label="手机号" min-width="120" />
        <el-table-column prop="school" label="学校" min-width="140" />
        <el-table-column prop="major" label="专业" min-width="140" />
        <el-table-column label="实习期间" min-width="160">
          <template #default="scope">
            {{ formatDateDisplay(scope.row.start_date) }} 至 {{ formatDateDisplay(scope.row.end_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="is_leader" label="组长" min-width="70" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.is_leader ? 'success' : 'info'">
              {{ scope.row.is_leader ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_living_outside" label="外宿" min-width="70" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.is_living_outside ? 'warning' : 'info'">
              {{ scope.row.is_living_outside ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑实习生对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加实习生' : '编辑实习生'"
      width="500px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入实习生姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="form.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="学校" prop="school">
          <el-input v-model="form.school" placeholder="请输入学校" />
        </el-form-item>
        <el-form-item label="专业" prop="major">
          <el-input v-model="form.major" placeholder="请输入专业" />
        </el-form-item>
        <el-form-item label="开始日期" prop="start_date">
          <el-date-picker v-model="form.start_date" type="date" placeholder="选择开始日期" />
        </el-form-item>
        <el-form-item label="结束日期" prop="end_date">
          <el-date-picker v-model="form.end_date" type="date" placeholder="选择结束日期" />
        </el-form-item>
        <el-form-item label="家庭住址" prop="home_address">
          <el-input v-model="form.home_address" placeholder="请输入家庭住址" />
        </el-form-item>
        <el-form-item label="当前住址" prop="current_address">
          <el-input v-model="form.current_address" placeholder="请输入当前住址" />
        </el-form-item>
        <el-form-item label="是否外宿" prop="is_living_outside">
          <el-switch v-model="form.is_living_outside" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="同宿联系人" prop="roommate_contact" v-if="form.is_living_outside">
          <el-input v-model="form.roommate_contact" placeholder="请输入同宿联系人" />
        </el-form-item>
        <el-form-item label="是否组长" prop="is_leader">
          <el-switch v-model="form.is_leader" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="form.notes" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
        <el-form-item label="校方联系人" prop="school_contact_name">
          <el-input v-model="form.school_contact_name" placeholder="请输入校方联系人" />
        </el-form-item>
        <el-form-item label="校方电话" prop="school_contact_phone">
          <el-input v-model="form.school_contact_phone" placeholder="请输入校方联系电话" />
        </el-form-item>
        <el-form-item label="家庭联系人" prop="family_contact_name">
          <el-input v-model="form.family_contact_name" placeholder="请输入家庭联系人" />
        </el-form-item>
        <el-form-item label="家庭电话" prop="family_contact_phone">
          <el-input v-model="form.family_contact_phone" placeholder="请输入家庭联系电话" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入实习生"
      width="600px"
    >
      <div class="import-container">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <p>1. 请下载Excel模板，按照模板格式填写数据</p>
            <p>2. 支持的文件格式：.xls, .xlsx</p>
            <p>3. 文件大小不超过10MB</p>
            <p>4. 必填字段：姓名、性别、学校、专业、实习开始时间、实习结束时间、联系方式、学校紧急联系人、学校紧急联系人电话、家庭紧急联系人、家庭紧急联系人电话、家庭住址、现住址</p>
          </template>
        </el-alert>

        <div class="template-download" style="margin-bottom: 20px">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon> 下载Excel模板
          </el-button>
        </div>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
          accept=".xls,.xlsx"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 .xls/.xlsx 文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :loading="importLoading">
            确认导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { Search, Plus, Upload, Download, UploadFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import studentService from '@/services/studentService'
import * as XLSX from 'xlsx'

export default {
  name: 'StudentList',
  setup() {
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const tableData = ref([])
    const dialogVisible = ref(false)
    const dialogType = ref('add')
    const formRef = ref(null)

    // 导入相关变量
    const importDialogVisible = ref(false)
    const importLoading = ref(false)
    const uploadRef = ref(null)
    const fileList = ref([])
    
    // 更新搜索表单，包含name和school字段
    const searchForm = reactive({
      name: '',
      school: ''
    })
    
    const form = reactive({
      id: '',
      name: '',
      gender: '男',
      phone: '',
      school: '',
      major: '',
      start_date: '',
      end_date: '',
      home_address: '',
      current_address: '',
      school_contact_name: '',
      school_contact_phone: '',
      family_contact_name: '',
      family_contact_phone: '',
      notes: '',
      is_leader: 0,
      is_living_outside: 0,
      roommate_contact: ''
    })
    
    const rules = {
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
      ],
      school: [{ required: true, message: '请输入学校', trigger: 'blur' }],
      major: [{ required: true, message: '请输入专业', trigger: 'blur' }],
      start_date: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
      end_date: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
      home_address: [{ required: true, message: '请输入家庭住址', trigger: 'blur' }],
      current_address: [{ required: true, message: '请输入当前住址', trigger: 'blur' }],
      school_contact_name: [{ required: true, message: '请输入校方联系人', trigger: 'blur' }],
      school_contact_phone: [{ required: true, message: '请输入校方联系电话', trigger: 'blur' }],
      family_contact_name: [{ required: true, message: '请输入家庭联系人', trigger: 'blur' }],
      family_contact_phone: [{ required: true, message: '请输入家庭联系电话', trigger: 'blur' }]
    }

    // API基础URL
    const apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000/api';
    
    // 获取token
    const getToken = () => {
      return localStorage.getItem('token') || '';
    }
    
    // 创建通用请求头
    const getAuthHeaders = () => {
      return {
        headers: {
          'Authorization': `Bearer ${getToken()}`
        }
      };
    }

    // 获取学生列表
    const fetchStudents = async () => {
      loading.value = true
      try {
        const response = await axios.get(`${apiBaseUrl}/students`, getAuthHeaders());
        if (response.data.success) {
          total.value = response.data.data.length;
          const start = (currentPage.value - 1) * pageSize.value;
          tableData.value = response.data.data.slice(start, start + pageSize.value);
        } else {
          ElMessage.error(response.data.message || '获取学生列表失败');
        }
      } catch (error) {
        console.error('获取学生列表失败:', error);
        ElMessage.error('获取学生列表失败');
      } finally {
        loading.value = false;
      }
    }

    onMounted(() => {
      fetchStudents()
    })

    // 重置搜索条件
    const resetSearch = () => {
      searchForm.name = '';
      searchForm.school = '';
      fetchStudents();
    }

    // 处理搜索 - 修改为使用searchForm并符合后端API
    const handleSearch = async () => {
      currentPage.value = 1
      loading.value = true
      try {
        // 检查是否有搜索条件
        if (!searchForm.name && !searchForm.school) {
          // 如果搜索条件为空，获取全部学生
          await fetchStudents();
          return;
        }
        
        // 构建请求参数，只包含非空值
        const params = {};
        if (searchForm.name) params.name = searchForm.name;
        if (searchForm.school) params.school = searchForm.school;
        
        const response = await axios.get(`${apiBaseUrl}/students/search`, {
          params,
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        });
        
        if (response.data.success) {
          total.value = response.data.data.length;
          tableData.value = response.data.data.slice(0, pageSize.value);
          
          // 显示搜索结果数量
          ElMessage.success(`找到 ${response.data.count || response.data.data.length} 条匹配记录`);
        } else {
          ElMessage.error(response.data.message || '搜索学生失败');
        }
      } catch (error) {
        console.error('搜索学生失败:', error);
        ElMessage.error('搜索学生失败');
      } finally {
        loading.value = false;
      }
    }

    // 处理分页变化
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchStudents()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchStudents()
    }

    // 处理添加学生
    const handleAdd = () => {
      dialogType.value = 'add'
      dialogVisible.value = true
      if (formRef.value) {
        formRef.value.resetFields()
      }
      Object.assign(form, {
        id: '',
        name: '',
        gender: '男',
        phone: '',
        school: '',
        major: '',
        start_date: '',
        end_date: '',
        home_address: '',
        current_address: '',
        school_contact_name: '',
        school_contact_phone: '',
        family_contact_name: '',
        family_contact_phone: '',
        notes: '',
        is_leader: 0,
        is_living_outside: 0,
        roommate_contact: ''
      })
    }

    // 处理编辑学生
    const handleEdit = async (row) => {
      dialogType.value = 'edit'
      dialogVisible.value = true
      
      loading.value = true
      try {
        // 获取学生的完整信息
        const response = await axios.get(`${apiBaseUrl}/students/${row.id}`, getAuthHeaders());
        if (response.data.success) {
          // 将数据填充到表单
          Object.assign(form, response.data.data);
        } else {
          ElMessage.error(response.data.message || '获取学生详情失败');
        }
      } catch (error) {
        console.error('获取学生详情失败:', error);
        ElMessage.error('获取学生详情失败');
      } finally {
        loading.value = false;
      }
    }

    // 处理删除学生
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除实习生 ${row.name} 吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await axios.delete(`${apiBaseUrl}/students/${row.id}`, getAuthHeaders());
          if (response.data.success) {
            ElMessage.success('删除成功');
            fetchStudents();
          } else {
            ElMessage.error(response.data.message || '删除失败');
          }
        } catch (error) {
          console.error('删除学生失败:', error);
          ElMessage.error('删除学生失败');
        }
      }).catch(() => {})
    }

    // 格式化日期显示
    const formatDateDisplay = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }

    // 提交表单
    const submitForm = () => {
      formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true;
          try {
            let response;
            const headers = getAuthHeaders().headers;
            
            // 创建表单数据的副本以进行处理
            const formData = { ...form };
            
            // 格式化日期为 YYYY-MM-DD
            if (formData.start_date) {
              formData.start_date = formatDate(formData.start_date);
            }
            if (formData.end_date) {
              formData.end_date = formatDate(formData.end_date);
            }
            
            if (dialogType.value === 'add') {
              // 创建新学生
              response = await axios.post(`${apiBaseUrl}/students`, formData, { headers });
            } else {
              // 更新学生信息
              response = await axios.put(`${apiBaseUrl}/students/${formData.id}`, formData, { headers });
            }
            
            if (response.data.success) {
              ElMessage.success(dialogType.value === 'add' ? '添加成功' : '修改成功');
              dialogVisible.value = false;
              fetchStudents();
            } else {
              ElMessage.error(response.data.message || (dialogType.value === 'add' ? '添加失败' : '修改失败'));
            }
          } catch (error) {
            console.error(dialogType.value === 'add' ? '添加学生失败:' : '更新学生失败:', error);
            if (error.response && error.response.data) {
              ElMessage.error(error.response.data.message || (dialogType.value === 'add' ? '添加学生失败' : '更新学生失败'));
            } else {
              ElMessage.error(dialogType.value === 'add' ? '添加学生失败' : '更新学生失败');
            }
          } finally {
            loading.value = false;
          }
        } else {
          return false;
        }
      })
    }
    
    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }

    // 导入相关方法
    const handleImport = () => {
      importDialogVisible.value = true
      fileList.value = []
    }

    const downloadTemplate = () => {
      // 创建Excel模板数据
      const templateData = [
        {
          '姓名': '张三',
          '性别': '男',
          '学校': '某某大学',
          '专业': '护理学',
          '实习开始时间': '2024-01-01',
          '实习结束时间': '2024-06-30',
          '联系方式': '13800138000',
          '学校紧急联系人': '李老师',
          '学校紧急联系人电话': '13900139000',
          '家庭紧急联系人': '张父',
          '家庭紧急联系人电话': '13700137000',
          '家庭住址': '某省某市某区某街道',
          '现住址': '某省某市某区某街道',
          '备注': '组长',
          '是否组长': '是',
          '是否外宿': '否',
          '同宿紧急联系人': '李四'
        }
      ]

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(templateData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '实习生信息')

      // 下载文件
      XLSX.writeFile(wb, '实习生导入模板.xlsx')
    }

    const beforeUpload = (file) => {
      const isExcel = file.type === 'application/vnd.ms-excel' ||
                     file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

      if (!isExcel) {
        ElMessage.error('请上传Excel格式的文件!')
        return false
      }

      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isLt10M) {
        ElMessage.error('文件大小不能超过10MB!')
        return false
      }

      return true
    }

    const handleFileChange = (file, uploadFileList) => {
      fileList.value = uploadFileList
    }

    const submitImport = async () => {
      if (fileList.value.length === 0) {
        ElMessage.error('请选择要上传的文件')
        return
      }

      importLoading.value = true

      try {
        const formData = new FormData()
        const fileObject = fileList.value[0]
        const rawFile = fileObject.raw || fileObject

        formData.append('file', rawFile)

        const response = await studentService.importStudents(formData)

        ElMessage.success(`导入成功！共导入 ${response.data.data.imported} 条记录`)
        importDialogVisible.value = false

        // 重新加载学生列表
        fetchStudents()

      } catch (error) {
        console.error('导入失败:', error)
        let errorMsg = '导入失败'

        if (error.response && error.response.data) {
          if (error.response.data.errors && error.response.data.errors.length > 0) {
            errorMsg += '：\n' + error.response.data.errors.join('\n')
          } else if (error.response.data.message) {
            errorMsg += '：' + error.response.data.message
          }
        }

        ElMessage.error(errorMsg)
      } finally {
        importLoading.value = false
      }
    }

    return {
      loading,
      searchForm,
      currentPage,
      pageSize,
      total,
      tableData,
      dialogVisible,
      dialogType,
      form,
      rules,
      formRef,
      importDialogVisible,
      importLoading,
      uploadRef,
      fileList,
      Search,
      Plus,
      Upload,
      Download,
      UploadFilled,
      formatDateDisplay,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleAdd,
      handleEdit,
      handleDelete,
      submitForm,
      handleImport,
      downloadTemplate,
      beforeUpload,
      handleFileChange,
      submitImport
    }
  }
}
</script>

<style scoped>
.student-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.filter-container {
  padding: 10px 0;
}

.table-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.search-input {
  width: 250px;
  margin-right: 10px;
}

.actions {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* Style the Element Plus components to match other pages */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
}
</style> 
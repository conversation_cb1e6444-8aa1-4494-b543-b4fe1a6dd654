{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, onMounted, watch } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Plus, Delete, Refresh, Upload, Download, UploadFilled } from '@element-plus/icons-vue';\nimport userService from '@/services/userService';\nimport studentService from '@/services/studentService';\nexport default {\n  __name: 'UserList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const studentListLoading = ref(false);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const multipleSelection = ref([]);\n    const dialogVisible = ref(false);\n    const dialogType = ref('add'); // 'add' or 'edit'\n    const userFormRef = ref(null);\n    const studentList = ref([]); // 学生列表\n\n    // 导入相关变量\n    const importDialogVisible = ref(false);\n    const importLoading = ref(false);\n    const uploadRef = ref(null);\n    const fileList = ref([]);\n\n    // 搜索表单\n    const searchForm = reactive({\n      username: '',\n      phone: '',\n      status: ''\n    });\n\n    // 用户表单\n    const userForm = reactive({\n      id: '',\n      username: '',\n      name: '',\n      password: '',\n      phone: '',\n      email: '',\n      role: 'student',\n      student_id: '',\n      status: 1\n    });\n\n    // 获取学生列表\n    const fetchStudentList = async () => {\n      studentListLoading.value = true;\n      try {\n        const response = await studentService.getStudents();\n        studentList.value = response.data.data;\n      } catch (error) {\n        console.error('获取学生列表失败:', error);\n        ElMessage.error('获取学生列表失败');\n      } finally {\n        studentListLoading.value = false;\n      }\n    };\n\n    // 当角色选择为学生时，加载学生列表\n    watch(() => userForm.role, newRole => {\n      if (newRole === 'student' && studentList.value.length === 0) {\n        fetchStudentList();\n      }\n    });\n\n    // 当对话框打开时，如果角色是学生且学生列表为空，则获取学生列表\n    watch(() => dialogVisible.value, newVal => {\n      if (newVal && userForm.role === 'student' && studentList.value.length === 0) {\n        fetchStudentList();\n      }\n    });\n\n    // 表单校验规则\n    const userFormRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      name: [{\n        required: true,\n        message: '请输入姓名',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }],\n      phone: [{\n        required: true,\n        message: '请输入手机号',\n        trigger: 'blur'\n      }, {\n        pattern: /^1[3-9]\\d{9}$/,\n        message: '请输入正确的手机号',\n        trigger: 'blur'\n      }],\n      email: [{\n        required: true,\n        message: '请输入邮箱',\n        trigger: 'blur'\n      }, {\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      role: [{\n        required: true,\n        message: '请选择角色',\n        trigger: 'change'\n      }]\n    };\n\n    // 用户数据\n    const userList = ref([]);\n    onMounted(() => {\n      fetchData();\n    });\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    };\n\n    // 获取数据\n    const fetchData = async () => {\n      loading.value = true;\n      try {\n        const response = await userService.getUsers({\n          page: currentPage.value,\n          limit: pageSize.value,\n          username: searchForm.username || undefined,\n          phone: searchForm.phone || undefined,\n          status: searchForm.status || undefined\n        });\n        userList.value = response.data.data;\n        total.value = response.data.count || 0;\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        ElMessage.error('获取用户列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 查询\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchData();\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      Object.keys(searchForm).forEach(key => {\n        searchForm[key] = '';\n      });\n      handleSearch();\n    };\n\n    // 刷新表格\n    const refreshTable = () => {\n      fetchData();\n    };\n\n    // 多选变化\n    const handleSelectionChange = selection => {\n      multipleSelection.value = selection;\n    };\n\n    // 新增用户\n    const handleAdd = () => {\n      dialogType.value = 'add';\n      resetUserForm();\n      dialogVisible.value = true;\n    };\n\n    // 编辑用户\n    const handleEdit = row => {\n      dialogType.value = 'edit';\n      resetUserForm();\n      Object.keys(userForm).forEach(key => {\n        if (key !== 'password') {\n          userForm[key] = row[key];\n        }\n      });\n      dialogVisible.value = true;\n    };\n\n    // 重置用户表单\n    const resetUserForm = () => {\n      if (userFormRef.value) {\n        userFormRef.value.resetFields();\n      }\n      Object.assign(userForm, {\n        id: '',\n        username: '',\n        name: '',\n        password: '',\n        phone: '',\n        email: '',\n        role: 'student',\n        student_id: '',\n        status: 1\n      });\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!userFormRef.value) return;\n      await userFormRef.value.validate(async valid => {\n        if (valid) {\n          try {\n            if (dialogType.value === 'add') {\n              // 新增用户\n              await userService.createUser(userForm);\n              ElMessage.success('新增用户成功');\n            } else {\n              // 编辑用户\n              await userService.updateUser(userForm.id, userForm);\n              ElMessage.success('编辑用户成功');\n            }\n            dialogVisible.value = false;\n            fetchData();\n          } catch (error) {\n            console.error('保存用户失败:', error);\n            ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message));\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 删除用户\n    const handleDelete = row => {\n      ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await userService.deleteUser(row.id);\n          ElMessage.success(`用户 ${row.username} 已删除`);\n          fetchData();\n        } catch (error) {\n          console.error('删除用户失败:', error);\n          ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message));\n        }\n      }).catch(() => {});\n    };\n\n    // 批量删除\n    const handleBatchDelete = () => {\n      if (multipleSelection.value.length === 0) {\n        ElMessage.warning('请至少选择一条记录');\n        return;\n      }\n      const names = multipleSelection.value.map(item => item.username).join('、');\n      const ids = multipleSelection.value.map(item => item.id);\n      ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await userService.batchDeleteUsers(ids);\n          ElMessage.success('批量删除成功');\n          fetchData();\n        } catch (error) {\n          console.error('批量删除失败:', error);\n          ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message));\n        }\n      }).catch(() => {});\n    };\n\n    // 修改状态\n    const handleStatusChange = async (val, row) => {\n      try {\n        await userService.updateUserStatus(row.id, val);\n        const status = val === 1 ? '启用' : '禁用';\n        ElMessage.success(`已${status}用户 ${row.username}`);\n      } catch (error) {\n        console.error('修改状态失败:', error);\n        ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));\n        // 回滚状态\n        row.status = val === 1 ? 0 : 1;\n      }\n    };\n\n    // 分页大小变化\n    const handleSizeChange = size => {\n      pageSize.value = size;\n      fetchData();\n    };\n\n    // 页码变化\n    const handleCurrentChange = page => {\n      currentPage.value = page;\n      fetchData();\n    };\n\n    // 导入相关方法\n    const handleImport = () => {\n      importDialogVisible.value = true;\n      fileList.value = [];\n    };\n    const downloadTemplate = () => {\n      // 动态导入XLSX库\n      import('xlsx').then(XLSX => {\n        // 创建Excel模板数据\n        const templateData = [{\n          '用户名': 'zhangsan',\n          '密码': '123456',\n          '姓名': '张三',\n          '角色': '学生',\n          '邮箱': '<EMAIL>',\n          '电话': '13800138000'\n        }];\n\n        // 创建工作簿\n        const ws = XLSX.utils.json_to_sheet(templateData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '用户信息');\n\n        // 下载文件\n        XLSX.writeFile(wb, '用户导入模板.xlsx');\n      }).catch(error => {\n        console.error('XLSX库加载失败:', error);\n        ElMessage.error('下载模板失败，请刷新页面重试');\n      });\n    };\n    const beforeUpload = file => {\n      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\n      if (!isExcel) {\n        ElMessage.error('请上传Excel格式的文件!');\n        return false;\n      }\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!');\n        return false;\n      }\n      return true;\n    };\n    const handleFileChange = (file, uploadFileList) => {\n      fileList.value = uploadFileList;\n    };\n    const submitImport = async () => {\n      if (fileList.value.length === 0) {\n        ElMessage.error('请选择要上传的文件');\n        return;\n      }\n      importLoading.value = true;\n      try {\n        const formData = new FormData();\n        const fileObject = fileList.value[0];\n        const rawFile = fileObject.raw || fileObject;\n        formData.append('file', rawFile);\n        const response = await userService.importUsers(formData);\n        ElMessage.success(`导入成功！共导入 ${response.data.data.imported} 条记录`);\n        importDialogVisible.value = false;\n\n        // 清理文件列表\n        fileList.value = [];\n        if (uploadRef.value) {\n          uploadRef.value.clearFiles();\n        }\n\n        // 重新加载用户列表\n        fetchData();\n      } catch (error) {\n        console.error('导入失败:', error);\n        let errorMsg = '导入失败';\n        if (error.response && error.response.data) {\n          if (error.response.data.errors && error.response.data.errors.length > 0) {\n            errorMsg += '：\\n' + error.response.data.errors.join('\\n');\n          } else if (error.response.data.message) {\n            errorMsg += '：' + error.response.data.message;\n          }\n        }\n        ElMessage.error(errorMsg);\n      } finally {\n        importLoading.value = false;\n      }\n    };\n    const __returned__ = {\n      loading,\n      studentListLoading,\n      currentPage,\n      pageSize,\n      total,\n      multipleSelection,\n      dialogVisible,\n      dialogType,\n      userFormRef,\n      studentList,\n      importDialogVisible,\n      importLoading,\n      uploadRef,\n      fileList,\n      searchForm,\n      userForm,\n      fetchStudentList,\n      userFormRules,\n      userList,\n      formatDate,\n      fetchData,\n      handleSearch,\n      resetForm,\n      refreshTable,\n      handleSelectionChange,\n      handleAdd,\n      handleEdit,\n      resetUserForm,\n      submitForm,\n      handleDelete,\n      handleBatchDelete,\n      handleStatusChange,\n      handleSizeChange,\n      handleCurrentChange,\n      handleImport,\n      downloadTemplate,\n      beforeUpload,\n      handleFileChange,\n      submitImport,\n      ref,\n      reactive,\n      onMounted,\n      watch,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get Plus() {\n        return Plus;\n      },\n      get Delete() {\n        return Delete;\n      },\n      get Refresh() {\n        return Refresh;\n      },\n      get Upload() {\n        return Upload;\n      },\n      get Download() {\n        return Download;\n      },\n      get UploadFilled() {\n        return UploadFilled;\n      },\n      get userService() {\n        return userService;\n      },\n      get studentService() {\n        return studentService;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "watch", "ElMessage", "ElMessageBox", "Plus", "Delete", "Refresh", "Upload", "Download", "UploadFilled", "userService", "studentService", "loading", "studentListLoading", "currentPage", "pageSize", "total", "multipleSelection", "dialogVisible", "dialogType", "userFormRef", "studentList", "importDialogVisible", "importLoading", "uploadRef", "fileList", "searchForm", "username", "phone", "status", "userForm", "id", "name", "password", "email", "role", "student_id", "fetchStudentList", "value", "response", "getStudents", "data", "error", "console", "newRole", "length", "newVal", "userFormRules", "required", "message", "trigger", "min", "max", "pattern", "type", "userList", "fetchData", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "getUsers", "page", "limit", "undefined", "count", "handleSearch", "resetForm", "Object", "keys", "for<PERSON>ach", "key", "refreshTable", "handleSelectionChange", "selection", "handleAdd", "resetUserForm", "handleEdit", "row", "resetFields", "assign", "submitForm", "validate", "valid", "createUser", "success", "updateUser", "handleDelete", "confirm", "confirmButtonText", "cancelButtonText", "then", "deleteUser", "catch", "handleBatchDelete", "warning", "names", "map", "item", "join", "ids", "batchDeleteUsers", "handleStatusChange", "val", "updateUserStatus", "handleSizeChange", "size", "handleCurrentChange", "handleImport", "downloadTemplate", "XLSX", "templateData", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "writeFile", "beforeUpload", "file", "isExcel", "isLt10M", "handleFileChange", "uploadFileList", "submitImport", "formData", "FormData", "fileObject", "rawFile", "raw", "append", "importUsers", "imported", "clearFiles", "errorMsg", "errors"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/views/users/UserList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-list-container\">\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card class=\"search-card\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"用户名\">\r\n          <el-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\">\r\n          <el-input v-model=\"searchForm.phone\" placeholder=\"请输入手机号\" clearable></el-input>\r\n        </el-form-item>\r\n    \r\n      \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 表格区域 -->\r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>用户列表</span>\r\n          <div>\r\n            <el-button type=\"success\" @click=\"handleImport\">\r\n              <el-icon><Upload /></el-icon> 批量导入\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"handleAdd\">\r\n              <el-icon><Plus /></el-icon> 新增用户\r\n            </el-button>\r\n            <el-button type=\"danger\" :disabled=\"multipleSelection.length === 0\" @click=\"handleBatchDelete\">\r\n              <el-icon><Delete /></el-icon> 批量删除\r\n            </el-button>\r\n            <el-button @click=\"refreshTable\">\r\n              <el-icon><Refresh /></el-icon> 刷新\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n        border\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"username\" label=\"用户名\" />\r\n        <el-table-column prop=\"name\" label=\"姓名\" />\r\n        <el-table-column prop=\"phone\" label=\"手机号\" />\r\n        <el-table-column prop=\"email\" label=\"邮箱\" />\r\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag v-if=\"scope.row.role === 'admin'\" type=\"danger\">管理员</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'teacher'\" type=\"warning\">带教老师</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'student'\" type=\"success\">学生</el-tag>\r\n            <el-tag v-else type=\"info\">{{ scope.row.role }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"student_id\" label=\"学生ID\" width=\"120\" />\r\n       \r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"danger\" \r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :total=\"total\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 用户表单对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"dialogType === 'add' ? '新增用户' : '编辑用户'\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        :model=\"userForm\"\r\n        :rules=\"userFormRules\"\r\n        ref=\"userFormRef\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"userForm.username\" placeholder=\"请输入用户名\" :disabled=\"dialogType === 'edit'\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"userForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"dialogType === 'add'\" label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色\" prop=\"role\">\r\n          <el-select v-model=\"userForm.role\" placeholder=\"请选择角色\">\r\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n            <el-option label=\"带教老师\" value=\"teacher\"></el-option>\r\n            <el-option label=\"学生\" value=\"student\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色是学生时，显示实习生选择器 -->\r\n        <el-form-item v-if=\"userForm.role === 'student'\" label=\"实习生\" prop=\"student_id\">\r\n          <el-select \r\n            v-model=\"userForm.student_id\" \r\n            placeholder=\"请选择关联的实习生\" \r\n            filterable\r\n            :loading=\"studentListLoading\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in studentList\"\r\n              :key=\"item.id\"\r\n              :label=\"`${item.name} - ${item.school}`\"\r\n              :value=\"item.id\"\r\n            >\r\n              <div class=\"student-option\">\r\n                <span>{{ item.name }}</span>\r\n                <span class=\"student-school\">{{ item.school }}</span>\r\n              </div>\r\n            </el-option>\r\n          </el-select>\r\n          <div class=\"form-tip\">将用户账号关联到现有实习生</div>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色不是学生时，隐藏学号字段 -->\r\n        <el-form-item v-if=\"userForm.role !== 'student'\" label=\"学号\" prop=\"student_id\">\r\n          <el-input v-model=\"userForm.student_id\" placeholder=\"请输入学号（选填）\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"批量导入用户\"\r\n      width=\"600px\"\r\n    >\r\n      <div class=\"import-container\">\r\n        <el-alert\r\n          title=\"导入说明\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px\"\r\n        >\r\n          <template #default>\r\n            <p>1. 请下载Excel模板，按照模板格式填写数据</p>\r\n            <p>2. 支持的文件格式：.xls, .xlsx</p>\r\n            <p>3. 文件大小不超过10MB</p>\r\n            <p>4. 必填字段：用户名、密码、姓名</p>\r\n            <p>5. 角色字段：管理员、教师、学生（默认为学生）</p>\r\n            <p>6. 注意：用户导入时不会自动关联实习生，需要后续手动关联</p>\r\n          </template>\r\n        </el-alert>\r\n\r\n        <div class=\"template-download\" style=\"margin-bottom: 20px\">\r\n          <el-button type=\"primary\" @click=\"downloadTemplate\">\r\n            <el-icon><Download /></el-icon> 下载Excel模板\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-upload\r\n          ref=\"uploadRef\"\r\n          class=\"upload-demo\"\r\n          drag\r\n          :auto-upload=\"false\"\r\n          :limit=\"1\"\r\n          :on-change=\"handleFileChange\"\r\n          :before-upload=\"beforeUpload\"\r\n          accept=\".xls,.xlsx\"\r\n        >\r\n          <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n          <div class=\"el-upload__text\">\r\n            将Excel文件拖到此处，或<em>点击上传</em>\r\n          </div>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              只能上传 .xls/.xlsx 文件，且不超过10MB\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitImport\" :loading=\"importLoading\">\r\n            确认导入\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Plus, Delete, Refresh, Upload, Download, UploadFilled } from '@element-plus/icons-vue'\r\nimport userService from '@/services/userService'\r\nimport studentService from '@/services/studentService'\r\n\r\nconst loading = ref(false)\r\nconst studentListLoading = ref(false)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst multipleSelection = ref([])\r\nconst dialogVisible = ref(false)\r\nconst dialogType = ref('add') // 'add' or 'edit'\r\nconst userFormRef = ref(null)\r\nconst studentList = ref([]) // 学生列表\r\n\r\n// 导入相关变量\r\nconst importDialogVisible = ref(false)\r\nconst importLoading = ref(false)\r\nconst uploadRef = ref(null)\r\nconst fileList = ref([])\r\n\r\n// 搜索表单\r\nconst searchForm = reactive({\r\n  username: '',\r\n  phone: '',\r\n  status: ''\r\n})\r\n\r\n// 用户表单\r\nconst userForm = reactive({\r\n  id: '',\r\n  username: '',\r\n  name: '',\r\n  password: '',\r\n  phone: '',\r\n  email: '',\r\n  role: 'student',\r\n  student_id: '',\r\n  status: 1\r\n})\r\n\r\n// 获取学生列表\r\nconst fetchStudentList = async () => {\r\n  studentListLoading.value = true\r\n  try {\r\n    const response = await studentService.getStudents()\r\n    studentList.value = response.data.data\r\n  } catch (error) {\r\n    console.error('获取学生列表失败:', error)\r\n    ElMessage.error('获取学生列表失败')\r\n  } finally {\r\n    studentListLoading.value = false\r\n  }\r\n}\r\n\r\n// 当角色选择为学生时，加载学生列表\r\nwatch(() => userForm.role, (newRole) => {\r\n  if (newRole === 'student' && studentList.value.length === 0) {\r\n    fetchStudentList()\r\n  }\r\n})\r\n\r\n// 当对话框打开时，如果角色是学生且学生列表为空，则获取学生列表\r\nwatch(() => dialogVisible.value, (newVal) => {\r\n  if (newVal && userForm.role === 'student' && studentList.value.length === 0) {\r\n    fetchStudentList()\r\n  }\r\n})\r\n\r\n// 表单校验规则\r\nconst userFormRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { required: true, message: '请输入手机号', trigger: 'blur' },\r\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  role: [\r\n    { required: true, message: '请选择角色', trigger: 'change' }\r\n  ]\r\n}\r\n\r\n// 用户数据\r\nconst userList = ref([])\r\n\r\nonMounted(() => {\r\n  fetchData()\r\n})\r\n\r\n// 格式化日期\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return ''\r\n  const date = new Date(dateString)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hours}:${minutes}`\r\n}\r\n\r\n// 获取数据\r\nconst fetchData = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await userService.getUsers({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      username: searchForm.username || undefined,\r\n      phone: searchForm.phone || undefined,\r\n      status: searchForm.status || undefined\r\n    })\r\n    userList.value = response.data.data\r\n    total.value = response.data.count || 0\r\n  } catch (error) {\r\n    console.error('获取用户列表失败:', error)\r\n    ElMessage.error('获取用户列表失败')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 查询\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchData()\r\n}\r\n\r\n// 重置表单\r\nconst resetForm = () => {\r\n  Object.keys(searchForm).forEach(key => {\r\n    searchForm[key] = ''\r\n  })\r\n  handleSearch()\r\n}\r\n\r\n// 刷新表格\r\nconst refreshTable = () => {\r\n  fetchData()\r\n}\r\n\r\n// 多选变化\r\nconst handleSelectionChange = (selection) => {\r\n  multipleSelection.value = selection\r\n}\r\n\r\n// 新增用户\r\nconst handleAdd = () => {\r\n  dialogType.value = 'add'\r\n  resetUserForm()\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 编辑用户\r\nconst handleEdit = (row) => {\r\n  dialogType.value = 'edit'\r\n  resetUserForm()\r\n  Object.keys(userForm).forEach(key => {\r\n    if (key !== 'password') {\r\n      userForm[key] = row[key]\r\n    }\r\n  })\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 重置用户表单\r\nconst resetUserForm = () => {\r\n  if (userFormRef.value) {\r\n    userFormRef.value.resetFields()\r\n  }\r\n  Object.assign(userForm, {\r\n    id: '',\r\n    username: '',\r\n    name: '',\r\n    password: '',\r\n    phone: '',\r\n    email: '',\r\n    role: 'student',\r\n    student_id: '',\r\n    status: 1\r\n  })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!userFormRef.value) return\r\n  \r\n  await userFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (dialogType.value === 'add') {\r\n          // 新增用户\r\n          await userService.createUser(userForm)\r\n          ElMessage.success('新增用户成功')\r\n        } else {\r\n          // 编辑用户\r\n          await userService.updateUser(userForm.id, userForm)\r\n          ElMessage.success('编辑用户成功')\r\n        }\r\n        dialogVisible.value = false\r\n        fetchData()\r\n      } catch (error) {\r\n        console.error('保存用户失败:', error)\r\n        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 删除用户\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.deleteUser(row.id)\r\n      ElMessage.success(`用户 ${row.username} 已删除`)\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error)\r\n      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量删除\r\nconst handleBatchDelete = () => {\r\n  if (multipleSelection.value.length === 0) {\r\n    ElMessage.warning('请至少选择一条记录')\r\n    return\r\n  }\r\n  \r\n  const names = multipleSelection.value.map(item => item.username).join('、')\r\n  const ids = multipleSelection.value.map(item => item.id)\r\n  \r\n  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.batchDeleteUsers(ids)\r\n      ElMessage.success('批量删除成功')\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('批量删除失败:', error)\r\n      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 修改状态\r\nconst handleStatusChange = async (val, row) => {\r\n  try {\r\n    await userService.updateUserStatus(row.id, val)\r\n    const status = val === 1 ? '启用' : '禁用'\r\n    ElMessage.success(`已${status}用户 ${row.username}`)\r\n  } catch (error) {\r\n    console.error('修改状态失败:', error)\r\n    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message))\r\n    // 回滚状态\r\n    row.status = val === 1 ? 0 : 1\r\n  }\r\n}\r\n\r\n// 分页大小变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  fetchData()\r\n}\r\n\r\n// 页码变化\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchData()\r\n}\r\n\r\n// 导入相关方法\r\nconst handleImport = () => {\r\n  importDialogVisible.value = true\r\n  fileList.value = []\r\n}\r\n\r\nconst downloadTemplate = () => {\r\n  // 动态导入XLSX库\r\n  import('xlsx').then(XLSX => {\r\n    // 创建Excel模板数据\r\n    const templateData = [\r\n      {\r\n        '用户名': 'zhangsan',\r\n        '密码': '123456',\r\n        '姓名': '张三',\r\n        '角色': '学生',\r\n        '邮箱': '<EMAIL>',\r\n        '电话': '13800138000'\r\n      }\r\n    ]\r\n\r\n    // 创建工作簿\r\n    const ws = XLSX.utils.json_to_sheet(templateData)\r\n    const wb = XLSX.utils.book_new()\r\n    XLSX.utils.book_append_sheet(wb, ws, '用户信息')\r\n\r\n    // 下载文件\r\n    XLSX.writeFile(wb, '用户导入模板.xlsx')\r\n  }).catch(error => {\r\n    console.error('XLSX库加载失败:', error)\r\n    ElMessage.error('下载模板失败，请刷新页面重试')\r\n  })\r\n}\r\n\r\nconst beforeUpload = (file) => {\r\n  const isExcel = file.type === 'application/vnd.ms-excel' ||\r\n                 file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n\r\n  if (!isExcel) {\r\n    ElMessage.error('请上传Excel格式的文件!')\r\n    return false\r\n  }\r\n\r\n  const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n  if (!isLt10M) {\r\n    ElMessage.error('文件大小不能超过10MB!')\r\n    return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\nconst handleFileChange = (file, uploadFileList) => {\r\n  fileList.value = uploadFileList\r\n}\r\n\r\nconst submitImport = async () => {\r\n  if (fileList.value.length === 0) {\r\n    ElMessage.error('请选择要上传的文件')\r\n    return\r\n  }\r\n\r\n  importLoading.value = true\r\n\r\n  try {\r\n    const formData = new FormData()\r\n    const fileObject = fileList.value[0]\r\n    const rawFile = fileObject.raw || fileObject\r\n\r\n    formData.append('file', rawFile)\r\n\r\n    const response = await userService.importUsers(formData)\r\n\r\n    ElMessage.success(`导入成功！共导入 ${response.data.data.imported} 条记录`)\r\n    importDialogVisible.value = false\r\n\r\n    // 清理文件列表\r\n    fileList.value = []\r\n    if (uploadRef.value) {\r\n      uploadRef.value.clearFiles()\r\n    }\r\n\r\n    // 重新加载用户列表\r\n    fetchData()\r\n\r\n  } catch (error) {\r\n    console.error('导入失败:', error)\r\n    let errorMsg = '导入失败'\r\n\r\n    if (error.response && error.response.data) {\r\n      if (error.response.data.errors && error.response.data.errors.length > 0) {\r\n        errorMsg += '：\\n' + error.response.data.errors.join('\\n')\r\n      } else if (error.response.data.message) {\r\n        errorMsg += '：' + error.response.data.message\r\n      }\r\n    }\r\n\r\n    ElMessage.error(errorMsg)\r\n  } finally {\r\n    importLoading.value = false\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.user-list-container {\r\n  padding: 10px;\r\n}\r\n\r\n.search-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.student-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.student-school {\r\n  color: #909399;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n</style> "], "mappings": ";;;AAuOA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,QAAQ,KAAK;AACrD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,yBAAyB;AAC/F,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;;;;;;;IAEtD,MAAMC,OAAO,GAAGd,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMe,kBAAkB,GAAGf,GAAG,CAAC,KAAK,CAAC;IACrC,MAAMgB,WAAW,GAAGhB,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMiB,QAAQ,GAAGjB,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMkB,KAAK,GAAGlB,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMmB,iBAAiB,GAAGnB,GAAG,CAAC,EAAE,CAAC;IACjC,MAAMoB,aAAa,GAAGpB,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMqB,UAAU,GAAGrB,GAAG,CAAC,KAAK,CAAC,EAAC;IAC9B,MAAMsB,WAAW,GAAGtB,GAAG,CAAC,IAAI,CAAC;IAC7B,MAAMuB,WAAW,GAAGvB,GAAG,CAAC,EAAE,CAAC,EAAC;;IAE5B;IACA,MAAMwB,mBAAmB,GAAGxB,GAAG,CAAC,KAAK,CAAC;IACtC,MAAMyB,aAAa,GAAGzB,GAAG,CAAC,KAAK,CAAC;IAChC,MAAM0B,SAAS,GAAG1B,GAAG,CAAC,IAAI,CAAC;IAC3B,MAAM2B,QAAQ,GAAG3B,GAAG,CAAC,EAAE,CAAC;;IAExB;IACA,MAAM4B,UAAU,GAAG3B,QAAQ,CAAC;MAC1B4B,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,MAAMC,QAAQ,GAAG/B,QAAQ,CAAC;MACxBgC,EAAE,EAAE,EAAE;MACNJ,QAAQ,EAAE,EAAE;MACZK,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZL,KAAK,EAAE,EAAE;MACTM,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,SAAS;MACfC,UAAU,EAAE,EAAE;MACdP,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnCxB,kBAAkB,CAACyB,KAAK,GAAG,IAAI;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM5B,cAAc,CAAC6B,WAAW,CAAC,CAAC;QACnDnB,WAAW,CAACiB,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI;MACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCxC,SAAS,CAACwC,KAAK,CAAC,UAAU,CAAC;MAC7B,CAAC,SAAS;QACR7B,kBAAkB,CAACyB,KAAK,GAAG,KAAK;MAClC;IACF,CAAC;;IAED;IACArC,KAAK,CAAC,MAAM6B,QAAQ,CAACK,IAAI,EAAGS,OAAO,IAAK;MACtC,IAAIA,OAAO,KAAK,SAAS,IAAIvB,WAAW,CAACiB,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QAC3DR,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACApC,KAAK,CAAC,MAAMiB,aAAa,CAACoB,KAAK,EAAGQ,MAAM,IAAK;MAC3C,IAAIA,MAAM,IAAIhB,QAAQ,CAACK,IAAI,KAAK,SAAS,IAAId,WAAW,CAACiB,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QAC3ER,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMU,aAAa,GAAG;MACpBpB,QAAQ,EAAE,CACR;QAAEqB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDlB,IAAI,EAAE,CACJ;QAAEgB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CACtD;MACDjB,QAAQ,EAAE,CACR;QAAEe,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDtB,KAAK,EAAE,CACL;QAAEoB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEG,OAAO,EAAE,eAAe;QAAEJ,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CACpE;MACDhB,KAAK,EAAE,CACL;QAAEc,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEI,IAAI,EAAE,OAAO;QAAEL,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC1D;MACDf,IAAI,EAAE,CACJ;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IAE3D,CAAC;;IAED;IACA,MAAMK,QAAQ,GAAGzD,GAAG,CAAC,EAAE,CAAC;IAExBE,SAAS,CAAC,MAAM;MACdwD,SAAS,CAAC,CAAC;IACb,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAU,GAAIC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;MAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,KAAK,GAAGL,MAAM,CAACL,IAAI,CAACW,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMK,OAAO,GAAGP,MAAM,CAACL,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIE,KAAK,IAAIE,OAAO,EAAE;IACtD,CAAC;;IAED;IACA,MAAMf,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B5C,OAAO,CAAC0B,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM7B,WAAW,CAAC+D,QAAQ,CAAC;UAC1CC,IAAI,EAAE5D,WAAW,CAACwB,KAAK;UACvBqC,KAAK,EAAE5D,QAAQ,CAACuB,KAAK;UACrBX,QAAQ,EAAED,UAAU,CAACC,QAAQ,IAAIiD,SAAS;UAC1ChD,KAAK,EAAEF,UAAU,CAACE,KAAK,IAAIgD,SAAS;UACpC/C,MAAM,EAAEH,UAAU,CAACG,MAAM,IAAI+C;QAC/B,CAAC,CAAC;QACFrB,QAAQ,CAACjB,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI;QACnCzB,KAAK,CAACsB,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACoC,KAAK,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCxC,SAAS,CAACwC,KAAK,CAAC,UAAU,CAAC;MAC7B,CAAC,SAAS;QACR9B,OAAO,CAAC0B,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMwC,YAAY,GAAGA,CAAA,KAAM;MACzBhE,WAAW,CAACwB,KAAK,GAAG,CAAC;MACrBkB,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAMuB,SAAS,GAAGA,CAAA,KAAM;MACtBC,MAAM,CAACC,IAAI,CAACvD,UAAU,CAAC,CAACwD,OAAO,CAACC,GAAG,IAAI;QACrCzD,UAAU,CAACyD,GAAG,CAAC,GAAG,EAAE;MACtB,CAAC,CAAC;MACFL,YAAY,CAAC,CAAC;IAChB,CAAC;;IAED;IACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;MACzB5B,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAM6B,qBAAqB,GAAIC,SAAS,IAAK;MAC3CrE,iBAAiB,CAACqB,KAAK,GAAGgD,SAAS;IACrC,CAAC;;IAED;IACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACtBpE,UAAU,CAACmB,KAAK,GAAG,KAAK;MACxBkD,aAAa,CAAC,CAAC;MACftE,aAAa,CAACoB,KAAK,GAAG,IAAI;IAC5B,CAAC;;IAED;IACA,MAAMmD,UAAU,GAAIC,GAAG,IAAK;MAC1BvE,UAAU,CAACmB,KAAK,GAAG,MAAM;MACzBkD,aAAa,CAAC,CAAC;MACfR,MAAM,CAACC,IAAI,CAACnD,QAAQ,CAAC,CAACoD,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,KAAK,UAAU,EAAE;UACtBrD,QAAQ,CAACqD,GAAG,CAAC,GAAGO,GAAG,CAACP,GAAG,CAAC;QAC1B;MACF,CAAC,CAAC;MACFjE,aAAa,CAACoB,KAAK,GAAG,IAAI;IAC5B,CAAC;;IAED;IACA,MAAMkD,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIpE,WAAW,CAACkB,KAAK,EAAE;QACrBlB,WAAW,CAACkB,KAAK,CAACqD,WAAW,CAAC,CAAC;MACjC;MACAX,MAAM,CAACY,MAAM,CAAC9D,QAAQ,EAAE;QACtBC,EAAE,EAAE,EAAE;QACNJ,QAAQ,EAAE,EAAE;QACZK,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZL,KAAK,EAAE,EAAE;QACTM,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,EAAE;QACdP,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgE,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAACzE,WAAW,CAACkB,KAAK,EAAE;MAExB,MAAMlB,WAAW,CAACkB,KAAK,CAACwD,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAChD,IAAIA,KAAK,EAAE;UACT,IAAI;YACF,IAAI5E,UAAU,CAACmB,KAAK,KAAK,KAAK,EAAE;cAC9B;cACA,MAAM5B,WAAW,CAACsF,UAAU,CAAClE,QAAQ,CAAC;cACtC5B,SAAS,CAAC+F,OAAO,CAAC,QAAQ,CAAC;YAC7B,CAAC,MAAM;cACL;cACA,MAAMvF,WAAW,CAACwF,UAAU,CAACpE,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC;cACnD5B,SAAS,CAAC+F,OAAO,CAAC,QAAQ,CAAC;YAC7B;YACA/E,aAAa,CAACoB,KAAK,GAAG,KAAK;YAC3BkB,SAAS,CAAC,CAAC;UACb,CAAC,CAAC,OAAOd,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;YAC/BxC,SAAS,CAACwC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;UAChF;QACF,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMkD,YAAY,GAAIT,GAAG,IAAK;MAC5BvF,YAAY,CAACiG,OAAO,CAAC,WAAWV,GAAG,CAAC/D,QAAQ,KAAK,EAAE,IAAI,EAAE;QACvD0E,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBhD,IAAI,EAAE;MACR,CAAC,CAAC,CAACiD,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAM7F,WAAW,CAAC8F,UAAU,CAACd,GAAG,CAAC3D,EAAE,CAAC;UACpC7B,SAAS,CAAC+F,OAAO,CAAC,MAAMP,GAAG,CAAC/D,QAAQ,MAAM,CAAC;UAC3C6B,SAAS,CAAC,CAAC;QACb,CAAC,CAAC,OAAOd,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BxC,SAAS,CAACwC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;QAChF;MACF,CAAC,CAAC,CAACwD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAIzF,iBAAiB,CAACqB,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QACxC3C,SAAS,CAACyG,OAAO,CAAC,WAAW,CAAC;QAC9B;MACF;MAEA,MAAMC,KAAK,GAAG3F,iBAAiB,CAACqB,KAAK,CAACuE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnF,QAAQ,CAAC,CAACoF,IAAI,CAAC,GAAG,CAAC;MAC1E,MAAMC,GAAG,GAAG/F,iBAAiB,CAACqB,KAAK,CAACuE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC/E,EAAE,CAAC;MAExD5B,YAAY,CAACiG,OAAO,CAAC,YAAYnF,iBAAiB,CAACqB,KAAK,CAACO,MAAM,QAAQ,EAAE,IAAI,EAAE;QAC7EwD,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBhD,IAAI,EAAE;MACR,CAAC,CAAC,CAACiD,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAM7F,WAAW,CAACuG,gBAAgB,CAACD,GAAG,CAAC;UACvC9G,SAAS,CAAC+F,OAAO,CAAC,QAAQ,CAAC;UAC3BzC,SAAS,CAAC,CAAC;QACb,CAAC,CAAC,OAAOd,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BxC,SAAS,CAACwC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;QAChF;MACF,CAAC,CAAC,CAACwD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMS,kBAAkB,GAAG,MAAAA,CAAOC,GAAG,EAAEzB,GAAG,KAAK;MAC7C,IAAI;QACF,MAAMhF,WAAW,CAAC0G,gBAAgB,CAAC1B,GAAG,CAAC3D,EAAE,EAAEoF,GAAG,CAAC;QAC/C,MAAMtF,MAAM,GAAGsF,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;QACtCjH,SAAS,CAAC+F,OAAO,CAAC,IAAIpE,MAAM,MAAM6D,GAAG,CAAC/D,QAAQ,EAAE,CAAC;MACnD,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BxC,SAAS,CAACwC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;QAC9E;QACAyC,GAAG,CAAC7D,MAAM,GAAGsF,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MAChC;IACF,CAAC;;IAED;IACA,MAAME,gBAAgB,GAAIC,IAAI,IAAK;MACjCvG,QAAQ,CAACuB,KAAK,GAAGgF,IAAI;MACrB9D,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAM+D,mBAAmB,GAAI7C,IAAI,IAAK;MACpC5D,WAAW,CAACwB,KAAK,GAAGoC,IAAI;MACxBlB,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAMgE,YAAY,GAAGA,CAAA,KAAM;MACzBlG,mBAAmB,CAACgB,KAAK,GAAG,IAAI;MAChCb,QAAQ,CAACa,KAAK,GAAG,EAAE;IACrB,CAAC;IAED,MAAMmF,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA,MAAM,CAAC,MAAM,CAAC,CAAClB,IAAI,CAACmB,IAAI,IAAI;QAC1B;QACA,MAAMC,YAAY,GAAG,CACnB;UACE,KAAK,EAAE,UAAU;UACjB,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,IAAI;UACV,IAAI,EAAE,IAAI;UACV,IAAI,EAAE,sBAAsB;UAC5B,IAAI,EAAE;QACR,CAAC,CACF;;QAED;QACA,MAAMC,EAAE,GAAGF,IAAI,CAACG,KAAK,CAACC,aAAa,CAACH,YAAY,CAAC;QACjD,MAAMI,EAAE,GAAGL,IAAI,CAACG,KAAK,CAACG,QAAQ,CAAC,CAAC;QAChCN,IAAI,CAACG,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,MAAM,CAAC;;QAE5C;QACAF,IAAI,CAACQ,SAAS,CAACH,EAAE,EAAE,aAAa,CAAC;MACnC,CAAC,CAAC,CAACtB,KAAK,CAAC/D,KAAK,IAAI;QAChBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClCxC,SAAS,CAACwC,KAAK,CAAC,gBAAgB,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC;IAED,MAAMyF,YAAY,GAAIC,IAAI,IAAK;MAC7B,MAAMC,OAAO,GAAGD,IAAI,CAAC9E,IAAI,KAAK,0BAA0B,IACzC8E,IAAI,CAAC9E,IAAI,KAAK,mEAAmE;MAEhG,IAAI,CAAC+E,OAAO,EAAE;QACZnI,SAAS,CAACwC,KAAK,CAAC,gBAAgB,CAAC;QACjC,OAAO,KAAK;MACd;MAEA,MAAM4F,OAAO,GAAGF,IAAI,CAACd,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;MAE5C,IAAI,CAACgB,OAAO,EAAE;QACZpI,SAAS,CAACwC,KAAK,CAAC,eAAe,CAAC;QAChC,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC;IAED,MAAM6F,gBAAgB,GAAGA,CAACH,IAAI,EAAEI,cAAc,KAAK;MACjD/G,QAAQ,CAACa,KAAK,GAAGkG,cAAc;IACjC,CAAC;IAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAIhH,QAAQ,CAACa,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QAC/B3C,SAAS,CAACwC,KAAK,CAAC,WAAW,CAAC;QAC5B;MACF;MAEAnB,aAAa,CAACe,KAAK,GAAG,IAAI;MAE1B,IAAI;QACF,MAAMoG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/B,MAAMC,UAAU,GAAGnH,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC;QACpC,MAAMuG,OAAO,GAAGD,UAAU,CAACE,GAAG,IAAIF,UAAU;QAE5CF,QAAQ,CAACK,MAAM,CAAC,MAAM,EAAEF,OAAO,CAAC;QAEhC,MAAMtG,QAAQ,GAAG,MAAM7B,WAAW,CAACsI,WAAW,CAACN,QAAQ,CAAC;QAExDxI,SAAS,CAAC+F,OAAO,CAAC,YAAY1D,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACwG,QAAQ,MAAM,CAAC;QAChE3H,mBAAmB,CAACgB,KAAK,GAAG,KAAK;;QAEjC;QACAb,QAAQ,CAACa,KAAK,GAAG,EAAE;QACnB,IAAId,SAAS,CAACc,KAAK,EAAE;UACnBd,SAAS,CAACc,KAAK,CAAC4G,UAAU,CAAC,CAAC;QAC9B;;QAEA;QACA1F,SAAS,CAAC,CAAC;MAEb,CAAC,CAAC,OAAOd,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAIyG,QAAQ,GAAG,MAAM;QAErB,IAAIzG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACH,QAAQ,CAACE,IAAI,EAAE;UACzC,IAAIC,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC2G,MAAM,IAAI1G,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC2G,MAAM,CAACvG,MAAM,GAAG,CAAC,EAAE;YACvEsG,QAAQ,IAAI,KAAK,GAAGzG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC2G,MAAM,CAACrC,IAAI,CAAC,IAAI,CAAC;UAC3D,CAAC,MAAM,IAAIrE,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACQ,OAAO,EAAE;YACtCkG,QAAQ,IAAI,GAAG,GAAGzG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACQ,OAAO;UAC/C;QACF;QAEA/C,SAAS,CAACwC,KAAK,CAACyG,QAAQ,CAAC;MAC3B,CAAC,SAAS;QACR5H,aAAa,CAACe,KAAK,GAAG,KAAK;MAC7B;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
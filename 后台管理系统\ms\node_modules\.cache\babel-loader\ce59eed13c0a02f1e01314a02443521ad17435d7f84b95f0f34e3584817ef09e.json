{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { defineComponent, useAttrs, computed, ref, watch, onMounted, openBlock, createElementBlock, mergeProps, unref, renderSlot, createElementVNode, normalizeClass, toDisplayString, Fragment, createCommentVNode, createBlock, createSlots, withCtx, normalizeProps, guardReactiveProps, nextTick } from 'vue';\nimport { isClient, useThrottleFn, useEventListener } from '@vueuse/core';\nimport { fromPairs } from 'lodash-unified';\nimport { ElImageViewer } from '../../image-viewer/index.mjs';\nimport { imageProps, imageEmits } from './image.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { isInContainer } from '../../../utils/dom/position.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useAttrs as useAttrs$1 } from '../../../hooks/use-attrs/index.mjs';\nimport { isArray, isString } from '@vue/shared';\nimport { isElement } from '../../../utils/types.mjs';\nimport { getScrollContainer } from '../../../utils/dom/scroll.mjs';\nconst __default__ = defineComponent({\n  name: \"ElImage\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: imageProps,\n  emits: imageEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"image\");\n    const rawAttrs = useAttrs();\n    const containerAttrs = computed(() => {\n      return fromPairs(Object.entries(rawAttrs).filter(([key]) => /^(data-|on[A-Z])/i.test(key) || [\"id\", \"style\"].includes(key)));\n    });\n    const imgAttrs = useAttrs$1({\n      excludeListeners: true,\n      excludeKeys: computed(() => {\n        return Object.keys(containerAttrs.value);\n      })\n    });\n    const imageSrc = ref();\n    const hasLoadError = ref(false);\n    const isLoading = ref(true);\n    const showViewer = ref(false);\n    const container = ref();\n    const _scrollContainer = ref();\n    const supportLoading = isClient && \"loading\" in HTMLImageElement.prototype;\n    let stopScrollListener;\n    const imageKls = computed(() => [ns.e(\"inner\"), preview.value && ns.e(\"preview\"), isLoading.value && ns.is(\"loading\")]);\n    const imageStyle = computed(() => {\n      const {\n        fit\n      } = props;\n      if (isClient && fit) {\n        return {\n          objectFit: fit\n        };\n      }\n      return {};\n    });\n    const preview = computed(() => {\n      const {\n        previewSrcList\n      } = props;\n      return isArray(previewSrcList) && previewSrcList.length > 0;\n    });\n    const imageIndex = computed(() => {\n      const {\n        previewSrcList,\n        initialIndex\n      } = props;\n      let previewIndex = initialIndex;\n      if (initialIndex > previewSrcList.length - 1) {\n        previewIndex = 0;\n      }\n      return previewIndex;\n    });\n    const isManual = computed(() => {\n      if (props.loading === \"eager\") return false;\n      return !supportLoading && props.loading === \"lazy\" || props.lazy;\n    });\n    const loadImage = () => {\n      if (!isClient) return;\n      isLoading.value = true;\n      hasLoadError.value = false;\n      imageSrc.value = props.src;\n    };\n    function handleLoad(event) {\n      isLoading.value = false;\n      hasLoadError.value = false;\n      emit(\"load\", event);\n    }\n    function handleError(event) {\n      isLoading.value = false;\n      hasLoadError.value = true;\n      emit(\"error\", event);\n    }\n    function handleLazyLoad() {\n      if (isInContainer(container.value, _scrollContainer.value)) {\n        loadImage();\n        removeLazyLoadListener();\n      }\n    }\n    const lazyLoadHandler = useThrottleFn(handleLazyLoad, 200, true);\n    async function addLazyLoadListener() {\n      var _a;\n      if (!isClient) return;\n      await nextTick();\n      const {\n        scrollContainer\n      } = props;\n      if (isElement(scrollContainer)) {\n        _scrollContainer.value = scrollContainer;\n      } else if (isString(scrollContainer) && scrollContainer !== \"\") {\n        _scrollContainer.value = (_a = document.querySelector(scrollContainer)) != null ? _a : void 0;\n      } else if (container.value) {\n        _scrollContainer.value = getScrollContainer(container.value);\n      }\n      if (_scrollContainer.value) {\n        stopScrollListener = useEventListener(_scrollContainer, \"scroll\", lazyLoadHandler);\n        setTimeout(() => handleLazyLoad(), 100);\n      }\n    }\n    function removeLazyLoadListener() {\n      if (!isClient || !_scrollContainer.value || !lazyLoadHandler) return;\n      stopScrollListener == null ? void 0 : stopScrollListener();\n      _scrollContainer.value = void 0;\n    }\n    function clickHandler() {\n      if (!preview.value) return;\n      showViewer.value = true;\n      emit(\"show\");\n    }\n    function closeViewer() {\n      showViewer.value = false;\n      emit(\"close\");\n    }\n    function switchViewer(val) {\n      emit(\"switch\", val);\n    }\n    watch(() => props.src, () => {\n      if (isManual.value) {\n        isLoading.value = true;\n        hasLoadError.value = false;\n        removeLazyLoadListener();\n        addLazyLoadListener();\n      } else {\n        loadImage();\n      }\n    });\n    onMounted(() => {\n      if (isManual.value) {\n        addLazyLoadListener();\n      } else {\n        loadImage();\n      }\n    });\n    expose({\n      showPreview: clickHandler\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", mergeProps({\n        ref_key: \"container\",\n        ref: container\n      }, unref(containerAttrs), {\n        class: [unref(ns).b(), _ctx.$attrs.class]\n      }), [hasLoadError.value ? renderSlot(_ctx.$slots, \"error\", {\n        key: 0\n      }, () => [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"error\"))\n      }, toDisplayString(unref(t)(\"el.image.error\")), 3)]) : (openBlock(), createElementBlock(Fragment, {\n        key: 1\n      }, [imageSrc.value !== void 0 ? (openBlock(), createElementBlock(\"img\", mergeProps({\n        key: 0\n      }, unref(imgAttrs), {\n        src: imageSrc.value,\n        loading: _ctx.loading,\n        style: unref(imageStyle),\n        class: unref(imageKls),\n        crossorigin: _ctx.crossorigin,\n        onClick: clickHandler,\n        onLoad: handleLoad,\n        onError: handleError\n      }), null, 16, [\"src\", \"loading\", \"crossorigin\"])) : createCommentVNode(\"v-if\", true), isLoading.value ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass(unref(ns).e(\"wrapper\"))\n      }, [renderSlot(_ctx.$slots, \"placeholder\", {}, () => [createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"placeholder\"))\n      }, null, 2)])], 2)) : createCommentVNode(\"v-if\", true)], 64)), unref(preview) ? (openBlock(), createElementBlock(Fragment, {\n        key: 2\n      }, [showViewer.value ? (openBlock(), createBlock(unref(ElImageViewer), {\n        key: 0,\n        \"z-index\": _ctx.zIndex,\n        \"initial-index\": unref(imageIndex),\n        infinite: _ctx.infinite,\n        \"zoom-rate\": _ctx.zoomRate,\n        \"min-scale\": _ctx.minScale,\n        \"max-scale\": _ctx.maxScale,\n        \"show-progress\": _ctx.showProgress,\n        \"url-list\": _ctx.previewSrcList,\n        crossorigin: _ctx.crossorigin,\n        \"hide-on-click-modal\": _ctx.hideOnClickModal,\n        teleported: _ctx.previewTeleported,\n        \"close-on-press-escape\": _ctx.closeOnPressEscape,\n        onClose: closeViewer,\n        onSwitch: switchViewer\n      }, createSlots({\n        toolbar: withCtx(toolbar => [renderSlot(_ctx.$slots, \"toolbar\", normalizeProps(guardReactiveProps(toolbar)))]),\n        default: withCtx(() => [_ctx.$slots.viewer ? (openBlock(), createElementBlock(\"div\", {\n          key: 0\n        }, [renderSlot(_ctx.$slots, \"viewer\")])) : createCommentVNode(\"v-if\", true)]),\n        _: 2\n      }, [_ctx.$slots.progress ? {\n        name: \"progress\",\n        fn: withCtx(progress => [renderSlot(_ctx.$slots, \"progress\", normalizeProps(guardReactiveProps(progress)))])\n      } : void 0]), 1032, [\"z-index\", \"initial-index\", \"infinite\", \"zoom-rate\", \"min-scale\", \"max-scale\", \"show-progress\", \"url-list\", \"crossorigin\", \"hide-on-click-modal\", \"teleported\", \"close-on-press-escape\"])) : createCommentVNode(\"v-if\", true)], 64)) : createCommentVNode(\"v-if\", true)], 16);\n    };\n  }\n});\nvar Image = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"image.vue\"]]);\nexport { Image as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "t", "useLocale", "ns", "useNamespace", "rawAttrs", "useAttrs", "containerAttrs", "computed", "fromPairs", "Object", "entries", "filter", "key", "test", "includes", "imgAttrs", "useAttrs$1", "excludeListeners", "excludeKeys", "keys", "value", "imageSrc", "ref", "hasLoadError", "isLoading", "showViewer", "container", "_scrollContainer", "supportLoading", "isClient", "HTMLImageElement", "prototype", "stopScrollListener", "imageKls", "e", "preview", "is", "imageStyle", "fit", "props", "objectFit", "previewSrcList", "isArray", "length", "imageIndex", "initialIndex", "previewIndex", "<PERSON><PERSON><PERSON><PERSON>", "loading", "lazy", "loadImage", "src", "handleLoad", "event", "emit", "handleError", "handleLazyLoad", "isInContainer", "removeLazyLoadListener", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useThrottleFn", "addLazyLoadListener", "_a", "nextTick", "scrollContainer", "isElement", "isString", "document", "querySelector", "getScrollContainer", "useEventListener", "setTimeout", "clickHandler", "<PERSON><PERSON><PERSON><PERSON>", "switchViewer", "val", "watch", "onMounted", "expose", "showPreview", "_ctx", "_cache", "openBlock", "createElementBlock", "mergeProps", "ref_key", "unref", "class", "b", "$attrs"], "sources": ["../../../../../../packages/components/image/src/image.vue"], "sourcesContent": ["<template>\n  <div ref=\"container\" v-bind=\"containerAttrs\" :class=\"[ns.b(), $attrs.class]\">\n    <slot v-if=\"hasLoadError\" name=\"error\">\n      <div :class=\"ns.e('error')\">{{ t('el.image.error') }}</div>\n    </slot>\n    <template v-else>\n      <img\n        v-if=\"imageSrc !== undefined\"\n        v-bind=\"imgAttrs\"\n        :src=\"imageSrc\"\n        :loading=\"loading\"\n        :style=\"imageStyle\"\n        :class=\"imageKls\"\n        :crossorigin=\"crossorigin\"\n        @click=\"clickHandler\"\n        @load=\"handleLoad\"\n        @error=\"handleError\"\n      />\n      <div v-if=\"isLoading\" :class=\"ns.e('wrapper')\">\n        <slot name=\"placeholder\">\n          <div :class=\"ns.e('placeholder')\" />\n        </slot>\n      </div>\n    </template>\n    <template v-if=\"preview\">\n      <image-viewer\n        v-if=\"showViewer\"\n        :z-index=\"zIndex\"\n        :initial-index=\"imageIndex\"\n        :infinite=\"infinite\"\n        :zoom-rate=\"zoomRate\"\n        :min-scale=\"minScale\"\n        :max-scale=\"maxScale\"\n        :show-progress=\"showProgress\"\n        :url-list=\"previewSrcList\"\n        :crossorigin=\"crossorigin\"\n        :hide-on-click-modal=\"hideOnClickModal\"\n        :teleported=\"previewTeleported\"\n        :close-on-press-escape=\"closeOnPressEscape\"\n        @close=\"closeViewer\"\n        @switch=\"switchViewer\"\n      >\n        <div v-if=\"$slots.viewer\">\n          <slot name=\"viewer\" />\n        </div>\n        <template v-if=\"$slots.progress\" #progress=\"progress\">\n          <slot name=\"progress\" v-bind=\"progress\" />\n        </template>\n        <template #toolbar=\"toolbar\">\n          <slot name=\"toolbar\" v-bind=\"toolbar\" />\n        </template>\n      </image-viewer>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  ref,\n  useAttrs as useRawAttrs,\n  watch,\n} from 'vue'\nimport { useEventListener, useThrottleFn } from '@vueuse/core'\nimport { fromPairs } from 'lodash-unified'\nimport { useAttrs, useLocale, useNamespace } from '@element-plus/hooks'\nimport ImageViewer from '@element-plus/components/image-viewer'\nimport {\n  getScrollContainer,\n  isArray,\n  isClient,\n  isElement,\n  isInContainer,\n  isString,\n} from '@element-plus/utils'\nimport { imageEmits, imageProps } from './image'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElImage',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(imageProps)\nconst emit = defineEmits(imageEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('image')\nconst rawAttrs = useRawAttrs()\n\nconst containerAttrs = computed(() => {\n  return fromPairs(\n    Object.entries(rawAttrs).filter(\n      ([key]) => /^(data-|on[A-Z])/i.test(key) || ['id', 'style'].includes(key)\n    )\n  )\n})\n\nconst imgAttrs = useAttrs({\n  excludeListeners: true,\n  excludeKeys: computed<string[]>(() => {\n    return Object.keys(containerAttrs.value)\n  }),\n})\n\nconst imageSrc = ref<string | undefined>()\nconst hasLoadError = ref(false)\nconst isLoading = ref(true)\nconst showViewer = ref(false)\nconst container = ref<HTMLElement>()\nconst _scrollContainer = ref<HTMLElement | Window>()\n\nconst supportLoading = isClient && 'loading' in HTMLImageElement.prototype\nlet stopScrollListener: (() => void) | undefined\n\nconst imageKls = computed(() => [\n  ns.e('inner'),\n  preview.value && ns.e('preview'),\n  isLoading.value && ns.is('loading'),\n])\n\nconst imageStyle = computed<CSSProperties>(() => {\n  const { fit } = props\n  if (isClient && fit) {\n    return { objectFit: fit }\n  }\n  return {}\n})\n\nconst preview = computed(() => {\n  const { previewSrcList } = props\n  return isArray(previewSrcList) && previewSrcList.length > 0\n})\n\nconst imageIndex = computed(() => {\n  const { previewSrcList, initialIndex } = props\n  let previewIndex = initialIndex\n  if (initialIndex > previewSrcList.length - 1) {\n    previewIndex = 0\n  }\n  return previewIndex\n})\n\nconst isManual = computed(() => {\n  if (props.loading === 'eager') return false\n  return (!supportLoading && props.loading === 'lazy') || props.lazy\n})\n\nconst loadImage = () => {\n  if (!isClient) return\n\n  // reset status\n  isLoading.value = true\n  hasLoadError.value = false\n  imageSrc.value = props.src\n}\n\nfunction handleLoad(event: Event) {\n  isLoading.value = false\n  hasLoadError.value = false\n  emit('load', event)\n}\n\nfunction handleError(event: Event) {\n  isLoading.value = false\n  hasLoadError.value = true\n  emit('error', event)\n}\n\nfunction handleLazyLoad() {\n  if (isInContainer(container.value, _scrollContainer.value)) {\n    loadImage()\n    removeLazyLoadListener()\n  }\n}\n\nconst lazyLoadHandler = useThrottleFn(handleLazyLoad, 200, true)\n\nasync function addLazyLoadListener() {\n  if (!isClient) return\n\n  await nextTick()\n\n  const { scrollContainer } = props\n  if (isElement(scrollContainer)) {\n    _scrollContainer.value = scrollContainer\n  } else if (isString(scrollContainer) && scrollContainer !== '') {\n    _scrollContainer.value =\n      document.querySelector<HTMLElement>(scrollContainer) ?? undefined\n  } else if (container.value) {\n    _scrollContainer.value = getScrollContainer(container.value)\n  }\n\n  if (_scrollContainer.value) {\n    stopScrollListener = useEventListener(\n      _scrollContainer,\n      'scroll',\n      lazyLoadHandler\n    )\n    setTimeout(() => handleLazyLoad(), 100)\n  }\n}\n\nfunction removeLazyLoadListener() {\n  if (!isClient || !_scrollContainer.value || !lazyLoadHandler) return\n\n  stopScrollListener?.()\n  _scrollContainer.value = undefined\n}\n\nfunction clickHandler() {\n  // don't show viewer when preview is false\n  if (!preview.value) return\n  showViewer.value = true\n  emit('show')\n}\n\nfunction closeViewer() {\n  showViewer.value = false\n  emit('close')\n}\n\nfunction switchViewer(val: number) {\n  emit('switch', val)\n}\n\nwatch(\n  () => props.src,\n  () => {\n    if (isManual.value) {\n      // reset status\n      isLoading.value = true\n      hasLoadError.value = false\n      removeLazyLoadListener()\n      addLazyLoadListener()\n    } else {\n      loadImage()\n    }\n  }\n)\n\nonMounted(() => {\n  if (isManual.value) {\n    addLazyLoadListener()\n  } else {\n    loadImage()\n  }\n})\n\ndefineExpose({\n  /** @description manually open preview */\n  showPreview: clickHandler,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;mCAiFc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;;;;;IAKM;MAAEC;IAAE,IAAIC,SAAU;IAClB,MAAAC,EAAA,GAAKC,YAAA,CAAa,OAAO;IAC/B,MAAMC,QAAA,GAAWC,QAAY;IAEvB,MAAAC,cAAA,GAAiBC,QAAA,CAAS,MAAM;MAC7B,OAAAC,SAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAN,QAAA,EAAAO,MAAA,GAAAC,GAAA,0BAAAC,IAAA,CAAAD,GAAA,qBAAAE,QAAA,CAAAF,GAAA;IAAA,CACL;IAAyB,MACvBG,QAAK,GAAMC,UAAoB;MACjCC,gBAAA;MACFC,WAAA,EAAAX,QAAA;QACD,OAAAE,MAAA,CAAAU,IAAA,CAAAb,cAAA,CAAAc,KAAA;MAED;IAA0B,EACxB;IACA,MAAAC,QAAA,GAAAC,GAAA;IACS,MAAAC,YAAO,GAAKD,GAAA;IAAoB,MACxCE,SAAA,GAAAF,GAAA;IACH,MAACG,UAAA,GAAAH,GAAA;IAED,MAAMI,SAAA,GAAmCJ,GAAA;IACnC,MAAAK,gBAAA,GAAmBL,GAAK;IACxB,MAAAM,cAAA,GAAoBC,QAAA,iBAAAC,gBAAA,CAAAC,SAAA;IACpB,IAAAC,kBAAA;IACN,MAAMC,QAAA,GAAA1B,QAA6B,QACnCL,EAAA,CAAAgC,CAAM,WAEAC,OAAA,CAAAf,KAAA,IAAAlB,EAAA,CAAAgC,CAAiB,CAAY,YAC/BV,SAAA,CAAAJ,KAAA,IAAAlB,EAAA,CAAAkC,EAAA,YAEE;IACJ,MAAAC,UAAY,GAAA9B,QAAA;MACZ,MAAQ;QAAA+B;MAAA,IAAAC,KAAY;MACpB,IAAUV,QAAA,IAAAS,GAAS,EAAG;QACvB;UAAAE,SAAA,EAAAF;QAAA;MAED;MACQ;IACN;IACS,MAAAH,OAAE,GAAA5B,QAAW,CAAI;MAC1B;QAAAkC;MAAA,IAAAF,KAAA;MACA,OAAOG,OAAC,CAAAD,cAAA,KAAAA,cAAA,CAAAE,MAAA;IAAA,CACT;IAEK,MAAAC,UAAU,GAAArC,QAAe;MACvB;QAAEkC,cAAA;QAAmBI;MAAA,IAAAN,KAAA;MAC3B,IAAAO,YAAe,GAAAD,YAAmB;MACnC,IAAAA,YAAA,GAAAJ,cAAA,CAAAE,MAAA;QAEKG,YAAA,GAAa;MACjB;MACA,OAAmBA,YAAA;IACnB,CAAI;IACa,MAAAC,QAAA,GAAAxC,QAAA;MACjB,IAAAgC,KAAA,CAAAS,OAAA,cACO;MACR,QAAApB,cAAA,IAAAW,KAAA,CAAAS,OAAA,eAAAT,KAAA,CAAAU,IAAA;IAED,CAAM;IACA,MAAAC,SAAkB,GAAAA,CAAA;MACtB,KAAArB,QAAS,EACV;MAEDL,SAAA,CAAAJ,KAAA,GAAwB;MACtBG,YAAe,CAAAH,KAAA;MAGfC,QAAA,CAAAD,KAAkB,GAAAmB,KAAA,CAAAY,GAAA;IAClB;IACA,SAAAC,UAAiBA,CAAMC,KAAA;MACzB7B,SAAA,CAAAJ,KAAA;MAEAG,YAAA,CAAAH,KAAoB,GAAc;MAChCkC,IAAA,OAAkB,EAAAD,KAAA;IAClB;IACA,SAAAE,WAAkBA,CAAAF,KAAA;MACpB7B,SAAA,CAAAJ,KAAA;MAEAG,YAAA,CAAAH,KAAA,GAAmC;MACjCkC,IAAA,QAAkB,EAAAD,KAAA;IAClB;IACA,SAAAG,cAAmBA,CAAA;MACrB,IAAAC,aAAA,CAAA/B,SAAA,CAAAN,KAAA,EAAAO,gBAAA,CAAAP,KAAA;QAEA8B,SAA0B;QACxBQ,sBAAkB;MAChB;IACA;IACF,MAAAC,eAAA,GAAAC,aAAA,CAAAJ,cAAA;IACF,eAAAK,oBAAA;MAEA,IAAMC,EAAkB;MAExB,KAAAjC,QAAe,EACb;MAEA,MAAMkC,QAAS;MAET;QAAEC;MAAA,CAAoB,GAAAzB,KAAA;MACxB,IAAA0B,SAAA,CAAUD,eAAe,CAAG;QAC9BrC,gBAAA,CAAiBP,KAAQ,GAAA4C,eAAA;MAAA,CAChB,UAAAE,QAAA,CAASF,eAAe,KAAKA,eAAA,KAAoB,EAAI;QAC9DrC,gBAAA,CAAiBP,KACf,IAAA0C,EAAA,GAAAK,QAAoC,CAAAC,aAAA,CAAAJ,eAAoB,aAAAF,EAAA;MAAA,CAC5D,UAAWpC,SAAA,CAAUN,KAAO;QACTO,gBAAA,CAAAP,KAAA,GAAQiD,kBAAmB,CAAA3C,SAAA,CAAUN,KAAK;MAAA;MAG7D,IAAIO,gBAAA,CAAiBP,KAAO;QACLY,kBAAA,GAAAsC,gBAAA,CAAA3C,gBAAA,YAAAgC,eAAA;QACnBY,UAAA,OAAAf,cAAA;MAAA;IACA;IAEF,SAAAE,uBAAA;MACW,KAAA7B,QAAA,KAAAF,gBAAqB,CAAAP,KAAM,KAAAuC,eAAA,EACxC;MACF3B,kBAAA,oBAAAA,kBAAA;MAEAL,gBAAkC,CAAAP,KAAA;IAChC;IAEqB,SAAAoD,aAAA;MACrB,KAAArC,OAAA,CAAAf,KAAyB,EAC3B;MAEAK,UAAwB,CAAAL,KAAA;MAElBkC,IAAA,CAAC;IACL;IACA,SAAWmB,YAAA;MACbhD,UAAA,CAAAL,KAAA;MAEAkC,IAAA,QAAuB;IACrB;IACA,SAAYoB,aAAAC,GAAA;MACdrB,IAAA,WAAAqB,GAAA;IAEA;IACEC,KAAA,OAAArC,KAAe,CAAGY,GAAA;MACpB,IAAAJ,QAAA,CAAA3B,KAAA;QAEAI,SAAA,CAAAJ,KAAA;QAAAG,YACc,CAAAH,KAAA;QACNsC,sBAAA;QACJG,mBAAoB;MAElB;QACAX,SAAA;MACA;IACA,CAAoB;IAAA2B,SACf;MACK,IAAA9B,QAAA,CAAA3B,KAAA;QACZyC,mBAAA;MAAA,CACF;QACFX,SAAA;MAEA;IACE;IACsB4B,MAAA;MACtBC,WAAO,EAAAP;IACL,CAAU;IACZ,QAAAQ,IAAA,EAAAC,MAAA;MACD,OAAAC,SAAA,IAAAC,kBAAA,QAAAC,UAAA;QAEYC,OAAA;QAAA/D,GAAA,EAAAI;MAAA,CAEE,EAAA4D,KAAA,CAAAhF,cAAA;QACdiF,KAAA,GAAAD,KAAA,CAAApF,EAAA,EAAAsF,CAAA,IAAAR,IAAA,CAAAS,MAAA,CAAAF,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { reactive, ref, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { User, Lock, Check } from '@element-plus/icons-vue';\nimport axios from 'axios';\n\n// 配置全局API请求拦截器，自动添加token\n\nexport default {\n  __name: 'LoginView',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    axios.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器，处理401错误\n    axios.interceptors.response.use(response => response, error => {\n      if (error.response && error.response.status === 401) {\n        // 清除本地存储的token\n        localStorage.removeItem('token');\n        localStorage.removeItem('userInfo');\n        // 如果用户不在登录页，重定向到登录页\n        if (window.location.pathname !== '/login') {\n          ElMessage.error('登录已过期，请重新登录');\n          window.location.href = '/#/login';\n        }\n      }\n      return Promise.reject(error);\n    });\n    const router = useRouter();\n    const loginFormRef = ref(null);\n    const loading = ref(false);\n    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n\n    // 登录相关\n    const loginForm = reactive({\n      username: '',\n      password: '',\n      remember: false\n    });\n    const loginRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }]\n    };\n    const handleLogin = async () => {\n      if (!loginFormRef.value) return;\n      try {\n        await loginFormRef.value.validate(async valid => {\n          if (valid) {\n            loading.value = true;\n            try {\n              const response = await axios.post(`${API_URL}/auth/login`, {\n                username: loginForm.username,\n                password: loginForm.password\n              });\n\n              // 登录成功，保存token和用户信息\n              const {\n                token,\n                data\n              } = response.data;\n              localStorage.setItem('token', token);\n\n              // 如果选择记住我，保存用户名\n              if (loginForm.remember) {\n                localStorage.setItem('rememberedUsername', loginForm.username);\n              } else {\n                localStorage.removeItem('rememberedUsername');\n              }\n\n              // 存储用户信息\n              localStorage.setItem('userInfo', JSON.stringify(data));\n\n              // 单独保存关键信息，方便使用\n              localStorage.setItem('userId', data.id);\n              localStorage.setItem('userRole', data.role);\n              if (data.student_id) {\n                localStorage.setItem('studentId', data.student_id);\n                console.log('保存学生ID:', data.student_id);\n              } else if (data.role === 'student') {\n                // 如果是学生但没有student_id，尝试从其他地方获取\n                try {\n                  const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`);\n                  if (studentResponse.data.success && studentResponse.data.data) {\n                    localStorage.setItem('studentId', studentResponse.data.data.id);\n                    console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id);\n                  } else {\n                    console.error('无法获取学生ID，但用户角色为学生');\n                    ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用');\n                  }\n                } catch (err) {\n                  console.error('获取学生信息失败:', err);\n                }\n              }\n              console.log('登录成功，用户信息:', data);\n              ElMessage.success('登录成功');\n\n              // 根据用户角色跳转到不同页面\n              if (data.role === 'student') {\n                router.push('/courses/list'); // 学生跳转到课程管理\n              } else if (data.role === 'admin' || data.role === 'teacher') {\n                router.push('/students/list'); // 管理员和教师跳转到实习生管理\n              } else {\n                router.push('/dashboard'); // 其他角色跳转到首页\n              }\n            } catch (error) {\n              console.error('登录失败:', error);\n              ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码');\n            } finally {\n              loading.value = false;\n            }\n          }\n        });\n      } catch (error) {\n        loading.value = false;\n        ElMessage.error('表单验证失败');\n      }\n    };\n\n    // 注册相关\n    const registerDialogVisible = ref(false);\n    const registerFormRef = ref(null);\n    const registerLoading = ref(false);\n    const registerForm = reactive({\n      username: '',\n      password: '',\n      confirmPassword: '',\n      name: '',\n      email: '',\n      phone: '',\n      student_id: '',\n      role: 'user' // 默认注册为普通用户\n    });\n    const validatePass = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入密码'));\n      } else if (value !== registerForm.password) {\n        callback(new Error('两次输入密码不一致!'));\n      } else {\n        callback();\n      }\n    };\n    const registerRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }],\n      confirmPassword: [{\n        required: true,\n        message: '请再次输入密码',\n        trigger: 'blur'\n      }, {\n        validator: validatePass,\n        trigger: 'blur'\n      }],\n      name: [{\n        required: true,\n        message: '请输入姓名',\n        trigger: 'blur'\n      }],\n      email: [{\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      phone: [{\n        pattern: /^1[3456789]\\d{9}$/,\n        message: '请输入正确的手机号码',\n        trigger: 'blur'\n      }]\n    };\n    const showRegister = () => {\n      registerDialogVisible.value = true;\n    };\n    const handleRegister = async () => {\n      if (!registerFormRef.value) return;\n      try {\n        await registerFormRef.value.validate(async valid => {\n          if (valid) {\n            registerLoading.value = true;\n            try {\n              const {\n                confirmPassword,\n                ...registerData\n              } = registerForm;\n              const response = await axios.post(`${API_URL}/auth/register`, registerData);\n              ElMessage.success('注册成功，请登录');\n              registerDialogVisible.value = false;\n\n              // 可选：自动填充登录表单\n              loginForm.username = registerForm.username;\n              loginForm.password = '';\n            } catch (error) {\n              console.error('注册失败:', error);\n              ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试');\n            } finally {\n              registerLoading.value = false;\n            }\n          }\n        });\n      } catch (error) {\n        registerLoading.value = false;\n        ElMessage.error('表单验证失败');\n      }\n    };\n\n    // 忘记密码相关\n    const forgotPasswordDialogVisible = ref(false);\n    const forgotPasswordFormRef = ref(null);\n    const forgotPasswordLoading = ref(false);\n    const forgotPasswordForm = reactive({\n      username: '',\n      email: ''\n    });\n    const forgotPasswordRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }],\n      email: [{\n        required: true,\n        message: '请输入邮箱',\n        trigger: 'blur'\n      }, {\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }]\n    };\n    const showForgotPassword = () => {\n      forgotPasswordDialogVisible.value = true;\n    };\n    const handleForgotPassword = async () => {\n      if (!forgotPasswordFormRef.value) return;\n      try {\n        await forgotPasswordFormRef.value.validate(async valid => {\n          if (valid) {\n            forgotPasswordLoading.value = true;\n            try {\n              // 注意：需要在后端实现忘记密码API\n              const response = await axios.post(`${API_URL}/auth/forgot-password`, forgotPasswordForm);\n              ElMessage.success('重置密码链接已发送到您的邮箱');\n              forgotPasswordDialogVisible.value = false;\n            } catch (error) {\n              console.error('忘记密码请求失败:', error);\n              ElMessage.error(error.response?.data?.message || '操作失败，请稍后重试');\n            } finally {\n              forgotPasswordLoading.value = false;\n            }\n          }\n        });\n      } catch (error) {\n        forgotPasswordLoading.value = false;\n        ElMessage.error('表单验证失败');\n      }\n    };\n\n    // 检查是否有记住的用户名\n    const checkRememberedUsername = () => {\n      const rememberedUsername = localStorage.getItem('rememberedUsername');\n      if (rememberedUsername) {\n        loginForm.username = rememberedUsername;\n        loginForm.remember = true;\n      }\n    };\n\n    // 组件挂载时检查记住的用户名\n    checkRememberedUsername();\n    const __returned__ = {\n      router,\n      loginFormRef,\n      loading,\n      API_URL,\n      loginForm,\n      loginRules,\n      handleLogin,\n      registerDialogVisible,\n      registerFormRef,\n      registerLoading,\n      registerForm,\n      validatePass,\n      registerRules,\n      showRegister,\n      handleRegister,\n      forgotPasswordDialogVisible,\n      forgotPasswordFormRef,\n      forgotPasswordLoading,\n      forgotPasswordForm,\n      forgotPasswordRules,\n      showForgotPassword,\n      handleForgotPassword,\n      checkRememberedUsername,\n      reactive,\n      ref,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get User() {\n        return User;\n      },\n      get Lock() {\n        return Lock;\n      },\n      get Check() {\n        return Check;\n      },\n      get axios() {\n        return axios;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["reactive", "ref", "onMounted", "useRouter", "ElMessage", "User", "Lock", "Check", "axios", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "pathname", "href", "router", "loginFormRef", "loading", "API_URL", "process", "env", "VUE_APP_API_URL", "loginForm", "username", "password", "remember", "loginRules", "required", "message", "trigger", "min", "max", "handleLogin", "value", "validate", "valid", "post", "data", "setItem", "JSON", "stringify", "id", "role", "student_id", "console", "log", "studentResponse", "get", "success", "warning", "err", "push", "registerDialogVisible", "registerFormRef", "registerLoading", "registerForm", "confirmPassword", "name", "email", "phone", "validatePass", "rule", "callback", "Error", "registerRules", "validator", "type", "pattern", "showRegister", "handleRegister", "registerData", "forgotPasswordDialogVisible", "forgotPasswordFormRef", "forgotPasswordLoading", "forgotPasswordForm", "forgotPasswordRules", "showForgotPassword", "handleForgotPassword", "checkRememberedUsername", "rememberedUsername"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/views/LoginView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card\">\r\n      <!-- Left side -->\r\n      <div class=\"login-info\">\r\n        <div class=\"logo-wrapper\">\r\n          <div class=\"logo-icon\">\r\n            <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo-img\" />\r\n            <i class=\"el-icon-monitor\"></i>\r\n          </div>\r\n          <div class=\"logo-text\">\r\n            实习生学籍管理系统\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"welcome-text\">\r\n          <h2>欢迎回来</h2>\r\n          <p>登录您的账户以继续访问系统</p>\r\n        </div>\r\n        \r\n        <div class=\"feature-list\">\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">现代化的管理界面</div>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">强大的功能模块</div>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">安全可靠的数据保护</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- Right side -->\r\n      <div class=\"login-form-wrapper\">\r\n        <div class=\"login-form-container\">\r\n          <h2 class=\"form-title\">用户登录</h2>\r\n          <p class=\"form-subtitle\">请输入您的账户信息</p>\r\n          \r\n          <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" class=\"login-form\">\r\n            <el-form-item prop=\"username\">\r\n              <el-input \r\n                v-model=\"loginForm.username\" \r\n                placeholder=\"输入账号\" \r\n                :prefix-icon=\"User\">\r\n              </el-input>\r\n            </el-form-item>\r\n            \r\n            <el-form-item prop=\"password\">\r\n              <el-input \r\n                v-model=\"loginForm.password\" \r\n                type=\"password\" \r\n                placeholder=\"输入密码\" \r\n                :prefix-icon=\"Lock\"\r\n                show-password>\r\n              </el-input>\r\n            </el-form-item>\r\n            \r\n           \r\n            \r\n            <el-form-item>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"handleLogin\" class=\"login-button\">\r\n                登录\r\n              </el-button>\r\n            </el-form-item>\r\n            \r\n           \r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 注册对话框 -->\r\n    <el-dialog\r\n      title=\"用户注册\"\r\n      v-model=\"registerDialogVisible\"\r\n      width=\"400px\"\r\n      center\r\n      destroy-on-close\r\n    >\r\n      <el-form :model=\"registerForm\" :rules=\"registerRules\" ref=\"registerFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"registerForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" :loading=\"registerLoading\" @click=\"handleRegister\">注册</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 忘记密码对话框 -->\r\n    <el-dialog\r\n      title=\"忘记密码\"\r\n      v-model=\"forgotPasswordDialogVisible\"\r\n      width=\"400px\"\r\n      center\r\n      destroy-on-close\r\n    >\r\n      <el-form :model=\"forgotPasswordForm\" :rules=\"forgotPasswordRules\" ref=\"forgotPasswordFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"forgotPasswordForm.username\" placeholder=\"请输入用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"forgotPasswordForm.email\" placeholder=\"请输入注册时的邮箱\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"forgotPasswordDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" :loading=\"forgotPasswordLoading\" @click=\"handleForgotPassword\">提交</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { User, Lock, Check } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\n// 配置全局API请求拦截器，自动添加token\r\naxios.interceptors.request.use(\r\n  config => {\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器，处理401错误\r\naxios.interceptors.response.use(\r\n  response => response,\r\n  error => {\r\n    if (error.response && error.response.status === 401) {\r\n      // 清除本地存储的token\r\n      localStorage.removeItem('token')\r\n      localStorage.removeItem('userInfo')\r\n      // 如果用户不在登录页，重定向到登录页\r\n      if (window.location.pathname !== '/login') {\r\n        ElMessage.error('登录已过期，请重新登录')\r\n        window.location.href = '/#/login'\r\n      }\r\n    }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nconst router = useRouter()\r\nconst loginFormRef = ref(null)\r\nconst loading = ref(false)\r\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\r\n\r\n// 登录相关\r\nconst loginForm = reactive({\r\n  username: '',\r\n  password: '',\r\n  remember: false\r\n})\r\n\r\nconst loginRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst handleLogin = async () => {\r\n  if (!loginFormRef.value) return\r\n  \r\n  try {\r\n    await loginFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        loading.value = true\r\n        \r\n        try {\r\n          const response = await axios.post(`${API_URL}/auth/login`, {\r\n            username: loginForm.username,\r\n            password: loginForm.password\r\n          })\r\n          \r\n          // 登录成功，保存token和用户信息\r\n          const { token, data } = response.data\r\n          localStorage.setItem('token', token)\r\n\r\n          // 如果选择记住我，保存用户名\r\n          if (loginForm.remember) {\r\n            localStorage.setItem('rememberedUsername', loginForm.username)\r\n          } else {\r\n            localStorage.removeItem('rememberedUsername')\r\n          }\r\n\r\n          // 存储用户信息\r\n          localStorage.setItem('userInfo', JSON.stringify(data))\r\n\r\n          // 单独保存关键信息，方便使用\r\n          localStorage.setItem('userId', data.id)\r\n          localStorage.setItem('userRole', data.role)\r\n          if (data.student_id) {\r\n            localStorage.setItem('studentId', data.student_id)\r\n            console.log('保存学生ID:', data.student_id)\r\n          } else if (data.role === 'student') {\r\n            // 如果是学生但没有student_id，尝试从其他地方获取\r\n            try {\r\n              const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`)\r\n              if (studentResponse.data.success && studentResponse.data.data) {\r\n                localStorage.setItem('studentId', studentResponse.data.data.id)\r\n                console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id)\r\n              } else {\r\n                console.error('无法获取学生ID，但用户角色为学生')\r\n                ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用')\r\n              }\r\n            } catch (err) {\r\n              console.error('获取学生信息失败:', err)\r\n            }\r\n          }\r\n\r\n          console.log('登录成功，用户信息:', data)\r\n          \r\n          ElMessage.success('登录成功')\r\n          \r\n          // 根据用户角色跳转到不同页面\r\n          if (data.role === 'student') {\r\n            router.push('/courses/list') // 学生跳转到课程管理\r\n          } else if (data.role === 'admin' || data.role === 'teacher') {\r\n            router.push('/students/list') // 管理员和教师跳转到实习生管理\r\n          } else {\r\n            router.push('/dashboard') // 其他角色跳转到首页\r\n          }\r\n        } catch (error) {\r\n          console.error('登录失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码')\r\n        } finally {\r\n          loading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    loading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 注册相关\r\nconst registerDialogVisible = ref(false)\r\nconst registerFormRef = ref(null)\r\nconst registerLoading = ref(false)\r\n\r\nconst registerForm = reactive({\r\n  username: '',\r\n  password: '',\r\n  confirmPassword: '',\r\n  name: '',\r\n  email: '',\r\n  phone: '',\r\n  student_id: '',\r\n  role: 'user'  // 默认注册为普通用户\r\n})\r\n\r\nconst validatePass = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入密码'))\r\n  } else if (value !== registerForm.password) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\nconst registerRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  confirmPassword: [\r\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\r\n    { validator: validatePass, trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showRegister = () => {\r\n  registerDialogVisible.value = true\r\n}\r\n\r\nconst handleRegister = async () => {\r\n  if (!registerFormRef.value) return\r\n  \r\n  try {\r\n    await registerFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        registerLoading.value = true\r\n        \r\n        try {\r\n          const { confirmPassword, ...registerData } = registerForm\r\n          \r\n          const response = await axios.post(`${API_URL}/auth/register`, registerData)\r\n          \r\n          ElMessage.success('注册成功，请登录')\r\n          registerDialogVisible.value = false\r\n          \r\n          // 可选：自动填充登录表单\r\n          loginForm.username = registerForm.username\r\n          loginForm.password = ''\r\n        } catch (error) {\r\n          console.error('注册失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')\r\n        } finally {\r\n          registerLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    registerLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 忘记密码相关\r\nconst forgotPasswordDialogVisible = ref(false)\r\nconst forgotPasswordFormRef = ref(null)\r\nconst forgotPasswordLoading = ref(false)\r\n\r\nconst forgotPasswordForm = reactive({\r\n  username: '',\r\n  email: ''\r\n})\r\n\r\nconst forgotPasswordRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showForgotPassword = () => {\r\n  forgotPasswordDialogVisible.value = true\r\n}\r\n\r\nconst handleForgotPassword = async () => {\r\n  if (!forgotPasswordFormRef.value) return\r\n  \r\n  try {\r\n    await forgotPasswordFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        forgotPasswordLoading.value = true\r\n        \r\n        try {\r\n          // 注意：需要在后端实现忘记密码API\r\n          const response = await axios.post(`${API_URL}/auth/forgot-password`, forgotPasswordForm)\r\n          \r\n          ElMessage.success('重置密码链接已发送到您的邮箱')\r\n          forgotPasswordDialogVisible.value = false\r\n        } catch (error) {\r\n          console.error('忘记密码请求失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '操作失败，请稍后重试')\r\n        } finally {\r\n          forgotPasswordLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    forgotPasswordLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 检查是否有记住的用户名\r\nconst checkRememberedUsername = () => {\r\n  const rememberedUsername = localStorage.getItem('rememberedUsername')\r\n  if (rememberedUsername) {\r\n    loginForm.username = rememberedUsername\r\n    loginForm.remember = true\r\n  }\r\n}\r\n\r\n// 组件挂载时检查记住的用户名\r\ncheckRememberedUsername()\r\n</script>\r\n\r\n<style scoped>\r\n.login-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color:rgb(124, 181, 239);\r\n}\r\n\r\n.login-card {\r\n  width: 1000px;\r\n  height: 600px;\r\n  display: flex;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Left side */\r\n.login-info {\r\n  width: 50%;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  color: white;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.logo-icon {\r\n  width: 120px;\r\n  height: 120px;\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 24px;\r\n  color: #409EFF;\r\n}\r\n\r\n.logo-img {\r\n  width: 96px;\r\n  height: 96px;\r\n  margin-right: 4px;\r\n  object-fit: contain;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-text {\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.welcome-text h2 {\r\n  font-size: 32px;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-text p {\r\n  font-size: 16px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.feature-list {\r\n  margin-top: auto;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.feature-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.feature-text {\r\n  font-size: 16px;\r\n}\r\n\r\n/* Right side */\r\n.login-form-wrapper {\r\n  width: 50%;\r\n  background-color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n}\r\n\r\n.login-form-container {\r\n  width: 100%;\r\n  max-width: 320px;\r\n}\r\n\r\n.form-title {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  text-align: center;\r\n}\r\n\r\n.form-subtitle {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.login-form :deep(.el-input__wrapper) {\r\n  padding: 0 15px;\r\n  height: 50px;\r\n  box-shadow: 0 0 0 1px #e4e7ed inset;\r\n}\r\n\r\n.login-form :deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n.form-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.forgot-link {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-button {\r\n  width: 100%;\r\n  height: 50px;\r\n  border-radius: 6px;\r\n  font-size: 16px;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.register-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.register-link a {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n\r\n/* Responsive */\r\n@media (max-width: 992px) {\r\n  .login-card {\r\n    width: 90%;\r\n    height: auto;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .login-info,\r\n  .login-form-wrapper {\r\n    width: 100%;\r\n    padding: 30px;\r\n  }\r\n  \r\n  .login-info {\r\n    padding-bottom: 40px;\r\n  }\r\n  \r\n  .welcome-text {\r\n    margin-bottom: 30px;\r\n  }\r\n  \r\n  .feature-list {\r\n    margin-top: 0;\r\n  }\r\n}\r\n</style> "], "mappings": ";AAgJA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,SAAS,QAAQ,KAAK;AAC9C,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,yBAAyB;AAC3D,OAAOC,KAAK,MAAM,OAAO;;AAEzB;;;;;;;;IACAA,KAAK,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAI;MACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACDM,KAAK,IAAI;MACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACAV,KAAK,CAACC,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC7BU,QAAQ,IAAIA,QAAQ,EACpBH,KAAK,IAAI;MACP,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;QACnD;QACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;QAChCT,YAAY,CAACS,UAAU,CAAC,UAAU,CAAC;QACnC;QACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;UACzCtB,SAAS,CAACc,KAAK,CAAC,aAAa,CAAC;UAC9BM,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,UAAU;QACnC;MACF;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,MAAMU,MAAM,GAAGzB,SAAS,CAAC,CAAC;IAC1B,MAAM0B,YAAY,GAAG5B,GAAG,CAAC,IAAI,CAAC;IAC9B,MAAM6B,OAAO,GAAG7B,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAM8B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;;IAE1E;IACA,MAAMC,SAAS,GAAGnC,QAAQ,CAAC;MACzBoC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,MAAMC,UAAU,GAAG;MACjBH,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDL,QAAQ,EAAE,CACR;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC;IAEnE,CAAC;IAED,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAAChB,YAAY,CAACiB,KAAK,EAAE;MAEzB,IAAI;QACF,MAAMjB,YAAY,CAACiB,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;UACjD,IAAIA,KAAK,EAAE;YACTlB,OAAO,CAACgB,KAAK,GAAG,IAAI;YAEpB,IAAI;cACF,MAAMzB,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,aAAa,EAAE;gBACzDK,QAAQ,EAAED,SAAS,CAACC,QAAQ;gBAC5BC,QAAQ,EAAEF,SAAS,CAACE;cACtB,CAAC,CAAC;;cAEF;cACA,MAAM;gBAAExB,KAAK;gBAAEqC;cAAK,CAAC,GAAG7B,QAAQ,CAAC6B,IAAI;cACrCpC,YAAY,CAACqC,OAAO,CAAC,OAAO,EAAEtC,KAAK,CAAC;;cAEpC;cACA,IAAIsB,SAAS,CAACG,QAAQ,EAAE;gBACtBxB,YAAY,CAACqC,OAAO,CAAC,oBAAoB,EAAEhB,SAAS,CAACC,QAAQ,CAAC;cAChE,CAAC,MAAM;gBACLtB,YAAY,CAACS,UAAU,CAAC,oBAAoB,CAAC;cAC/C;;cAEA;cACAT,YAAY,CAACqC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;;cAEtD;cACApC,YAAY,CAACqC,OAAO,CAAC,QAAQ,EAAED,IAAI,CAACI,EAAE,CAAC;cACvCxC,YAAY,CAACqC,OAAO,CAAC,UAAU,EAAED,IAAI,CAACK,IAAI,CAAC;cAC3C,IAAIL,IAAI,CAACM,UAAU,EAAE;gBACnB1C,YAAY,CAACqC,OAAO,CAAC,WAAW,EAAED,IAAI,CAACM,UAAU,CAAC;gBAClDC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAER,IAAI,CAACM,UAAU,CAAC;cACzC,CAAC,MAAM,IAAIN,IAAI,CAACK,IAAI,KAAK,SAAS,EAAE;gBAClC;gBACA,IAAI;kBACF,MAAMI,eAAe,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,GAAG7B,OAAO,qBAAqBmB,IAAI,CAACI,EAAE,EAAE,CAAC;kBACjF,IAAIK,eAAe,CAACT,IAAI,CAACW,OAAO,IAAIF,eAAe,CAACT,IAAI,CAACA,IAAI,EAAE;oBAC7DpC,YAAY,CAACqC,OAAO,CAAC,WAAW,EAAEQ,eAAe,CAACT,IAAI,CAACA,IAAI,CAACI,EAAE,CAAC;oBAC/DG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,eAAe,CAACT,IAAI,CAACA,IAAI,CAACI,EAAE,CAAC;kBAC/D,CAAC,MAAM;oBACLG,OAAO,CAACvC,KAAK,CAAC,mBAAmB,CAAC;oBAClCd,SAAS,CAAC0D,OAAO,CAAC,uBAAuB,CAAC;kBAC5C;gBACF,CAAC,CAAC,OAAOC,GAAG,EAAE;kBACZN,OAAO,CAACvC,KAAK,CAAC,WAAW,EAAE6C,GAAG,CAAC;gBACjC;cACF;cAEAN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAER,IAAI,CAAC;cAE/B9C,SAAS,CAACyD,OAAO,CAAC,MAAM,CAAC;;cAEzB;cACA,IAAIX,IAAI,CAACK,IAAI,KAAK,SAAS,EAAE;gBAC3B3B,MAAM,CAACoC,IAAI,CAAC,eAAe,CAAC,EAAC;cAC/B,CAAC,MAAM,IAAId,IAAI,CAACK,IAAI,KAAK,OAAO,IAAIL,IAAI,CAACK,IAAI,KAAK,SAAS,EAAE;gBAC3D3B,MAAM,CAACoC,IAAI,CAAC,gBAAgB,CAAC,EAAC;cAChC,CAAC,MAAM;gBACLpC,MAAM,CAACoC,IAAI,CAAC,YAAY,CAAC,EAAC;cAC5B;YACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;cACduC,OAAO,CAACvC,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;cAC7Bd,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,gBAAgB,CAAC;YACpE,CAAC,SAAS;cACRX,OAAO,CAACgB,KAAK,GAAG,KAAK;YACvB;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdY,OAAO,CAACgB,KAAK,GAAG,KAAK;QACrB1C,SAAS,CAACc,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC;;IAED;IACA,MAAM+C,qBAAqB,GAAGhE,GAAG,CAAC,KAAK,CAAC;IACxC,MAAMiE,eAAe,GAAGjE,GAAG,CAAC,IAAI,CAAC;IACjC,MAAMkE,eAAe,GAAGlE,GAAG,CAAC,KAAK,CAAC;IAElC,MAAMmE,YAAY,GAAGpE,QAAQ,CAAC;MAC5BoC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZgC,eAAe,EAAE,EAAE;MACnBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACThB,UAAU,EAAE,EAAE;MACdD,IAAI,EAAE,MAAM,CAAE;IAChB,CAAC,CAAC;IAEF,MAAMkB,YAAY,GAAGA,CAACC,IAAI,EAAE5B,KAAK,EAAE6B,QAAQ,KAAK;MAC9C,IAAI7B,KAAK,KAAK,EAAE,EAAE;QAChB6B,QAAQ,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;MAChC,CAAC,MAAM,IAAI9B,KAAK,KAAKsB,YAAY,CAAC/B,QAAQ,EAAE;QAC1CsC,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;MACnC,CAAC,MAAM;QACLD,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,MAAME,aAAa,GAAG;MACpBzC,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDL,QAAQ,EAAE,CACR;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACD2B,eAAe,EAAE,CACf;QAAE7B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEoC,SAAS,EAAEL,YAAY;QAAE/B,OAAO,EAAE;MAAO,CAAC,CAC7C;MACD4B,IAAI,EAAE,CACJ;QAAE9B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CACtD;MACD6B,KAAK,EAAE,CACL;QAAEQ,IAAI,EAAE,OAAO;QAAEtC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC1D;MACD8B,KAAK,EAAE,CACL;QAAEQ,OAAO,EAAE,mBAAmB;QAAEvC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC;IAE5E,CAAC;IAED,MAAMuC,YAAY,GAAGA,CAAA,KAAM;MACzBhB,qBAAqB,CAACnB,KAAK,GAAG,IAAI;IACpC,CAAC;IAED,MAAMoC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAAChB,eAAe,CAACpB,KAAK,EAAE;MAE5B,IAAI;QACF,MAAMoB,eAAe,CAACpB,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;UACpD,IAAIA,KAAK,EAAE;YACTmB,eAAe,CAACrB,KAAK,GAAG,IAAI;YAE5B,IAAI;cACF,MAAM;gBAAEuB,eAAe;gBAAE,GAAGc;cAAa,CAAC,GAAGf,YAAY;cAEzD,MAAM/C,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,gBAAgB,EAAEoD,YAAY,CAAC;cAE3E/E,SAAS,CAACyD,OAAO,CAAC,UAAU,CAAC;cAC7BI,qBAAqB,CAACnB,KAAK,GAAG,KAAK;;cAEnC;cACAX,SAAS,CAACC,QAAQ,GAAGgC,YAAY,CAAChC,QAAQ;cAC1CD,SAAS,CAACE,QAAQ,GAAG,EAAE;YACzB,CAAC,CAAC,OAAOnB,KAAK,EAAE;cACduC,OAAO,CAACvC,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;cAC7Bd,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,YAAY,CAAC;YAChE,CAAC,SAAS;cACR0B,eAAe,CAACrB,KAAK,GAAG,KAAK;YAC/B;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdiD,eAAe,CAACrB,KAAK,GAAG,KAAK;QAC7B1C,SAAS,CAACc,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC;;IAED;IACA,MAAMkE,2BAA2B,GAAGnF,GAAG,CAAC,KAAK,CAAC;IAC9C,MAAMoF,qBAAqB,GAAGpF,GAAG,CAAC,IAAI,CAAC;IACvC,MAAMqF,qBAAqB,GAAGrF,GAAG,CAAC,KAAK,CAAC;IAExC,MAAMsF,kBAAkB,GAAGvF,QAAQ,CAAC;MAClCoC,QAAQ,EAAE,EAAE;MACZmC,KAAK,EAAE;IACT,CAAC,CAAC;IAEF,MAAMiB,mBAAmB,GAAG;MAC1BpD,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,CACvD;MACD6B,KAAK,EAAE,CACL;QAAE/B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEqC,IAAI,EAAE,OAAO;QAAEtC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC;IAE7D,CAAC;IAED,MAAM+C,kBAAkB,GAAGA,CAAA,KAAM;MAC/BL,2BAA2B,CAACtC,KAAK,GAAG,IAAI;IAC1C,CAAC;IAED,MAAM4C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI,CAACL,qBAAqB,CAACvC,KAAK,EAAE;MAElC,IAAI;QACF,MAAMuC,qBAAqB,CAACvC,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;UAC1D,IAAIA,KAAK,EAAE;YACTsC,qBAAqB,CAACxC,KAAK,GAAG,IAAI;YAElC,IAAI;cACF;cACA,MAAMzB,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,uBAAuB,EAAEwD,kBAAkB,CAAC;cAExFnF,SAAS,CAACyD,OAAO,CAAC,gBAAgB,CAAC;cACnCuB,2BAA2B,CAACtC,KAAK,GAAG,KAAK;YAC3C,CAAC,CAAC,OAAO5B,KAAK,EAAE;cACduC,OAAO,CAACvC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;cACjCd,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,YAAY,CAAC;YAChE,CAAC,SAAS;cACR6C,qBAAqB,CAACxC,KAAK,GAAG,KAAK;YACrC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdoE,qBAAqB,CAACxC,KAAK,GAAG,KAAK;QACnC1C,SAAS,CAACc,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC;;IAED;IACA,MAAMyE,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMC,kBAAkB,GAAG9E,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;MACrE,IAAI6E,kBAAkB,EAAE;QACtBzD,SAAS,CAACC,QAAQ,GAAGwD,kBAAkB;QACvCzD,SAAS,CAACG,QAAQ,GAAG,IAAI;MAC3B;IACF,CAAC;;IAED;IACAqD,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
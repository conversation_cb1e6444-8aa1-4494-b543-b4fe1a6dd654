{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Download } from '@element-plus/icons-vue';\nimport axios from 'axios';\n\n// 直接定义API基础URL，而不是使用环境变量\nconst API_URL = 'http://localhost:3000';\n\n// 创建axios实例，配置默认headers\n\nexport default {\n  __name: 'CourseList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const http = axios.create({\n      baseURL: API_URL\n    });\n\n    // 添加请求拦截器，为每个请求添加token\n    http.interceptors.request.use(config => {\n      // 从localStorage获取token\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 日期格式化函数\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      if (isNaN(date.getTime())) return dateString;\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    };\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const dialogVisible = ref(false);\n    const dialogTitle = ref('新增课程');\n    const courseFormRef = ref(null);\n    const fileList = ref([]);\n    const uploadAction = ref(`${API_URL}/api/courses`); // 实际的上传API地址\n    const submitting = ref(false);\n    const tempFile = ref(null); // 存储临时文件\n\n    // 上传请求头，包含token\n    const uploadHeaders = computed(() => {\n      return {\n        Authorization: `Bearer ${localStorage.getItem('token') || ''}`\n      };\n    });\n\n    // 课程列表\n    const courseList = ref([]);\n\n    // 过滤条件\n    const filterForm = reactive({\n      title: ''\n    });\n\n    // 课程表单\n    const courseForm = reactive({\n      id: null,\n      title: '',\n      description: '',\n      material_path: ''\n    });\n\n    // 验证规则\n    const courseRules = {\n      title: [{\n        required: true,\n        message: '请输入课程标题',\n        trigger: 'blur'\n      }]\n    };\n\n    // 获取课程列表数据\n    const fetchCourses = async () => {\n      loading.value = true;\n      try {\n        // 处理分页\n        const response = await http.get('/api/courses');\n        if (response.data.success) {\n          courseList.value = response.data.data;\n          total.value = response.data.count;\n        } else {\n          ElMessage.error(response.data.message || '获取课程列表失败');\n        }\n      } catch (error) {\n        console.error('获取课程列表失败:', error);\n        if (error.response && error.response.status === 401) {\n          ElMessage.error('登录已过期，请重新登录');\n          // 可以选择重定向到登录页面\n          // router.push('/login')\n        } else {\n          ElMessage.error('获取课程列表失败，请检查网络连接');\n        }\n      } finally {\n        loading.value = false;\n      }\n    };\n    onMounted(() => {\n      fetchCourses();\n    });\n\n    // 重置过滤条件\n    const resetFilter = () => {\n      filterForm.title = '';\n      fetchCourses();\n    };\n\n    // 搜索\n    const handleSearch = async () => {\n      loading.value = true;\n      try {\n        const response = await http.get('/api/courses/search', {\n          params: {\n            keyword: filterForm.title\n          }\n        });\n        if (response.data.success) {\n          courseList.value = response.data.data;\n          total.value = response.data.count;\n          ElMessage.success('搜索完成');\n        } else {\n          ElMessage.error(response.data.message || '搜索失败');\n        }\n      } catch (error) {\n        console.error('搜索失败:', error);\n        ElMessage.error('搜索失败，请检查网络连接');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 新增课程\n    const handleAddCourse = () => {\n      dialogTitle.value = '新增课程';\n      dialogVisible.value = true;\n      // 重置表单\n      courseForm.id = null;\n      courseForm.title = '';\n      courseForm.description = '';\n      courseForm.material_path = '';\n      tempFile.value = null;\n      fileList.value = [];\n    };\n\n    // 编辑课程\n    const handleEdit = row => {\n      dialogTitle.value = '编辑课程';\n      dialogVisible.value = true;\n\n      // 填充表单数据\n      courseForm.id = row.id;\n      courseForm.title = row.title;\n      courseForm.description = row.description;\n      courseForm.material_path = row.material_path;\n      tempFile.value = null;\n\n      // 设置文件列表\n      fileList.value = [];\n      if (row.material_path) {\n        fileList.value.push({\n          name: row.material_path.split('/').pop(),\n          url: `${API_URL}/${row.material_path}`\n        });\n      }\n    };\n\n    // 删除课程\n    const handleDelete = row => {\n      ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          const response = await http.delete(`/api/courses/${row.id}`);\n          if (response.data.success) {\n            ElMessage.success(`课程 ${row.title} 已删除`);\n            fetchCourses();\n          } else {\n            ElMessage.error(response.data.message || '删除课程失败');\n          }\n        } catch (error) {\n          console.error('删除课程失败:', error);\n          ElMessage.error('删除课程失败，请检查网络连接');\n        }\n      }).catch(() => {});\n    };\n\n    // 下载课件\n    const handleDownload = async row => {\n      if (!row.material_path) {\n        ElMessage.warning('该课程没有上传课件');\n        return;\n      }\n      try {\n        ElMessage.success(`正在下载 ${row.title} 的课件`);\n\n        // 使用axios发送请求并获取blob数据\n        const response = await http.get(`/api/courses/${row.id}/download`, {\n          responseType: 'blob' // 指定响应类型为blob\n        });\n\n        // 获取文件名，从响应头获取\n        let filename = '';\n        const contentDisposition = response.headers['content-disposition'];\n        if (contentDisposition) {\n          const filenameMatch = contentDisposition.match(/filename[^;=\\n]*=((['\"]).*?\\2|[^;\\n]*)/);\n          if (filenameMatch && filenameMatch[1]) {\n            filename = filenameMatch[1].replace(/['\"]/g, '');\n            try {\n              // 尝试解码文件名\n              filename = decodeURIComponent(filename);\n            } catch (e) {\n              console.error('解码文件名失败', e);\n              // 如果解码失败，使用原始文件名\n            }\n          }\n        }\n\n        // 如果没有从响应头获取到文件名，则使用路径中的文件名\n        if (!filename) {\n          filename = row.material_path.split('/').pop();\n        }\n\n        // 创建blob链接\n        const blob = new Blob([response.data]);\n        const url = window.URL.createObjectURL(blob);\n\n        // 创建临时链接并模拟点击下载\n        const link = document.createElement('a');\n        link.href = url;\n        link.setAttribute('download', filename);\n        document.body.appendChild(link);\n        link.click();\n\n        // 清理\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      } catch (error) {\n        console.error('下载失败:', error);\n        ElMessage.error('下载课件失败，请检查权限或网络连接');\n      }\n    };\n\n    // 自定义上传请求处理\n    const customUploadRequest = options => {\n      // 存储文件对象\n      tempFile.value = options.file;\n\n      // 修改文件名以解决中文编码问题\n      const originalFileName = options.file.name;\n      // 显示成功消息，包含原始文件名\n      ElMessage.success(`文件 ${originalFileName} 已选择`);\n      options.onSuccess();\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!courseFormRef.value) return;\n      await courseFormRef.value.validate(async valid => {\n        if (valid) {\n          submitting.value = true;\n          try {\n            const formData = new FormData();\n            formData.append('title', courseForm.title);\n            formData.append('description', courseForm.description);\n            if (tempFile.value) {\n              // 添加原始文件名到formData以供后端使用\n              formData.append('originalFileName', tempFile.value.name);\n              formData.append('material', tempFile.value);\n            }\n            let response;\n            if (courseForm.id) {\n              // 编辑模式\n              response = await http.put(`/api/courses/${courseForm.id}`, formData);\n              if (response.data.success) {\n                ElMessage.success(`课程 ${courseForm.title} 信息已更新`);\n              } else {\n                ElMessage.error(response.data.message || '更新课程失败');\n              }\n            } else {\n              // 新增模式\n              response = await http.post('/api/courses', formData);\n              if (response.data.success) {\n                ElMessage.success(`课程 ${courseForm.title} 添加成功`);\n              } else {\n                ElMessage.error(response.data.message || '添加课程失败');\n              }\n            }\n            dialogVisible.value = false;\n            fetchCourses();\n          } catch (error) {\n            console.error('提交表单失败:', error);\n            ElMessage.error('提交失败，请检查网络连接');\n          } finally {\n            submitting.value = false;\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 文件上传相关方法\n    const handlePreview = file => {\n      if (file.url) {\n        window.open(file.url, '_blank');\n      }\n    };\n    const handleRemove = () => {\n      fileList.value = [];\n      tempFile.value = null;\n    };\n    const handleUploadSuccess = () => {\n      // 这个方法不再需要显示成功消息，因为在customUploadRequest中已处理\n    };\n    const beforeUpload = file => {\n      // 文件大小限制：10MB\n      const maxSize = 10 * 1024 * 1024;\n      if (file.size > maxSize) {\n        ElMessage.error('文件大小不能超过10MB');\n        return false;\n      }\n      return true;\n    };\n\n    // 分页处理\n    const handleSizeChange = size => {\n      pageSize.value = size;\n      currentPage.value = 1;\n      fetchCourses();\n    };\n    const handleCurrentChange = page => {\n      currentPage.value = page;\n      fetchCourses();\n    };\n    const __returned__ = {\n      API_URL,\n      http,\n      formatDate,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      dialogVisible,\n      dialogTitle,\n      courseFormRef,\n      fileList,\n      uploadAction,\n      submitting,\n      tempFile,\n      uploadHeaders,\n      courseList,\n      filterForm,\n      courseForm,\n      courseRules,\n      fetchCourses,\n      resetFilter,\n      handleSearch,\n      handleAddCourse,\n      handleEdit,\n      handleDelete,\n      handleDownload,\n      customUploadRequest,\n      submitForm,\n      handlePreview,\n      handleRemove,\n      handleUploadSuccess,\n      beforeUpload,\n      handleSizeChange,\n      handleCurrentChange,\n      ref,\n      reactive,\n      onMounted,\n      computed,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get Download() {\n        return Download;\n      },\n      get axios() {\n        return axios;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "ElMessage", "ElMessageBox", "Download", "axios", "API_URL", "http", "create", "baseURL", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "formatDate", "dateString", "date", "Date", "isNaN", "getTime", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "loading", "currentPage", "pageSize", "total", "dialogVisible", "dialogTitle", "courseFormRef", "fileList", "uploadAction", "submitting", "tempFile", "uploadHeaders", "courseList", "filterForm", "title", "courseForm", "id", "description", "material_path", "courseRules", "required", "message", "trigger", "fetchCourses", "value", "response", "get", "data", "success", "count", "console", "status", "resetFilter", "handleSearch", "params", "keyword", "handleAddCourse", "handleEdit", "row", "push", "name", "split", "pop", "url", "handleDelete", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "delete", "catch", "handleDownload", "warning", "responseType", "filename", "contentDisposition", "filenameMatch", "match", "replace", "decodeURIComponent", "e", "blob", "Blob", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "customUploadRequest", "options", "file", "originalFileName", "onSuccess", "submitForm", "validate", "valid", "formData", "FormData", "append", "put", "post", "handlePreview", "open", "handleRemove", "handleUploadSuccess", "beforeUpload", "maxSize", "size", "handleSizeChange", "handleCurrentChange", "page"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/views/courses/CourseList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"course-list-container\">\r\n    <el-card class=\"filter-card\">\r\n      <div class=\"filter-container\">\r\n        <el-form :model=\"filterForm\" inline>\r\n          <el-form-item label=\"课程标题\">\r\n            <el-input v-model=\"filterForm.title\" placeholder=\"请输入课程标题\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button @click=\"resetFilter\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n    \r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>岗前培训课程</span>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddCourse\">新增课程</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table :data=\"courseList\" stripe border style=\"width: 100%\" v-loading=\"loading\">\r\n        <el-table-column type=\"index\" width=\"50\" />\r\n        <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"200\" />\r\n        <el-table-column prop=\"description\" label=\"课程描述\" min-width=\"300\" show-overflow-tooltip />\r\n        <el-table-column label=\"课件\" width=\"120\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button v-if=\"scope.row.material_path\" size=\"small\" type=\"success\" link @click=\"handleDownload(scope.row)\">\r\n              <el-icon><Download /></el-icon>\r\n              下载课件\r\n            </el-button>\r\n            <span v-else class=\"no-material\">无</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/编辑课程对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form :model=\"courseForm\" :rules=\"courseRules\" ref=\"courseFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"课程标题\" prop=\"title\">\r\n          <el-input v-model=\"courseForm.title\" placeholder=\"请输入课程标题\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程描述\" prop=\"description\">\r\n          <el-input type=\"textarea\" v-model=\"courseForm.description\" placeholder=\"请输入课程描述\" rows=\"4\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课件文件\">\r\n          <el-upload\r\n            class=\"material-upload\"\r\n            :action=\"uploadAction\"\r\n            :headers=\"uploadHeaders\"\r\n            :http-request=\"customUploadRequest\"\r\n            :on-preview=\"handlePreview\"\r\n            :on-remove=\"handleRemove\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :before-upload=\"beforeUpload\"\r\n            :limit=\"1\"\r\n            :file-list=\"fileList\"\r\n          >\r\n            <el-button type=\"primary\">点击上传</el-button>\r\n            <template #tip>\r\n              <div class=\"el-upload__tip\">\r\n                支持上传PDF、Word、PPT等格式文件，大小不超过10MB\r\n              </div>\r\n            </template>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Download } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\n// 直接定义API基础URL，而不是使用环境变量\r\nconst API_URL = 'http://localhost:3000'\r\n\r\n// 创建axios实例，配置默认headers\r\nconst http = axios.create({\r\n  baseURL: API_URL\r\n})\r\n\r\n// 添加请求拦截器，为每个请求添加token\r\nhttp.interceptors.request.use(config => {\r\n  // 从localStorage获取token\r\n  const token = localStorage.getItem('token')\r\n  if (token) {\r\n    config.headers.Authorization = `Bearer ${token}`\r\n  }\r\n  return config\r\n}, error => {\r\n  return Promise.reject(error)\r\n})\r\n\r\n// 日期格式化函数\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return '-'\r\n  const date = new Date(dateString)\r\n  if (isNaN(date.getTime())) return dateString\r\n  \r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  const seconds = String(date.getSeconds()).padStart(2, '0')\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n}\r\n\r\nconst loading = ref(true)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst dialogVisible = ref(false)\r\nconst dialogTitle = ref('新增课程')\r\nconst courseFormRef = ref(null)\r\nconst fileList = ref([])\r\nconst uploadAction = ref(`${API_URL}/api/courses`) // 实际的上传API地址\r\nconst submitting = ref(false)\r\nconst tempFile = ref(null) // 存储临时文件\r\n\r\n// 上传请求头，包含token\r\nconst uploadHeaders = computed(() => {\r\n  return {\r\n    Authorization: `Bearer ${localStorage.getItem('token') || ''}`\r\n  }\r\n})\r\n\r\n// 课程列表\r\nconst courseList = ref([])\r\n\r\n// 过滤条件\r\nconst filterForm = reactive({\r\n  title: ''\r\n})\r\n\r\n// 课程表单\r\nconst courseForm = reactive({\r\n  id: null,\r\n  title: '',\r\n  description: '',\r\n  material_path: ''\r\n})\r\n\r\n// 验证规则\r\nconst courseRules = {\r\n  title: [\r\n    { required: true, message: '请输入课程标题', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 获取课程列表数据\r\nconst fetchCourses = async () => {\r\n  loading.value = true\r\n  try {\r\n    // 处理分页\r\n    const response = await http.get('/api/courses')\r\n    if (response.data.success) {\r\n      courseList.value = response.data.data\r\n      total.value = response.data.count\r\n    } else {\r\n      ElMessage.error(response.data.message || '获取课程列表失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取课程列表失败:', error)\r\n    if (error.response && error.response.status === 401) {\r\n      ElMessage.error('登录已过期，请重新登录')\r\n      // 可以选择重定向到登录页面\r\n      // router.push('/login')\r\n    } else {\r\n      ElMessage.error('获取课程列表失败，请检查网络连接')\r\n    }\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  fetchCourses()\r\n})\r\n\r\n// 重置过滤条件\r\nconst resetFilter = () => {\r\n  filterForm.title = ''\r\n  fetchCourses()\r\n}\r\n\r\n// 搜索\r\nconst handleSearch = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await http.get('/api/courses/search', {\r\n      params: { keyword: filterForm.title }\r\n    })\r\n    if (response.data.success) {\r\n      courseList.value = response.data.data\r\n      total.value = response.data.count\r\n      ElMessage.success('搜索完成')\r\n    } else {\r\n      ElMessage.error(response.data.message || '搜索失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('搜索失败:', error)\r\n    ElMessage.error('搜索失败，请检查网络连接')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 新增课程\r\nconst handleAddCourse = () => {\r\n  dialogTitle.value = '新增课程'\r\n  dialogVisible.value = true\r\n  // 重置表单\r\n  courseForm.id = null\r\n  courseForm.title = ''\r\n  courseForm.description = ''\r\n  courseForm.material_path = ''\r\n  tempFile.value = null\r\n  fileList.value = []\r\n}\r\n\r\n// 编辑课程\r\nconst handleEdit = (row) => {\r\n  dialogTitle.value = '编辑课程'\r\n  dialogVisible.value = true\r\n  \r\n  // 填充表单数据\r\n  courseForm.id = row.id\r\n  courseForm.title = row.title\r\n  courseForm.description = row.description\r\n  courseForm.material_path = row.material_path\r\n  tempFile.value = null\r\n  \r\n  // 设置文件列表\r\n  fileList.value = []\r\n  if (row.material_path) {\r\n    fileList.value.push({\r\n      name: row.material_path.split('/').pop(),\r\n      url: `${API_URL}/${row.material_path}`\r\n    })\r\n  }\r\n}\r\n\r\n// 删除课程\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      const response = await http.delete(`/api/courses/${row.id}`)\r\n      if (response.data.success) {\r\n        ElMessage.success(`课程 ${row.title} 已删除`)\r\n        fetchCourses()\r\n      } else {\r\n        ElMessage.error(response.data.message || '删除课程失败')\r\n      }\r\n    } catch (error) {\r\n      console.error('删除课程失败:', error)\r\n      ElMessage.error('删除课程失败，请检查网络连接')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 下载课件\r\nconst handleDownload = async (row) => {\r\n  if (!row.material_path) {\r\n    ElMessage.warning('该课程没有上传课件')\r\n    return\r\n  }\r\n  \r\n  try {\r\n    ElMessage.success(`正在下载 ${row.title} 的课件`)\r\n    \r\n    // 使用axios发送请求并获取blob数据\r\n    const response = await http.get(`/api/courses/${row.id}/download`, {\r\n      responseType: 'blob' // 指定响应类型为blob\r\n    })\r\n    \r\n    // 获取文件名，从响应头获取\r\n    let filename = ''\r\n    const contentDisposition = response.headers['content-disposition']\r\n    if (contentDisposition) {\r\n      const filenameMatch = contentDisposition.match(/filename[^;=\\n]*=((['\"]).*?\\2|[^;\\n]*)/)\r\n      if (filenameMatch && filenameMatch[1]) {\r\n        filename = filenameMatch[1].replace(/['\"]/g, '')\r\n        try {\r\n          // 尝试解码文件名\r\n          filename = decodeURIComponent(filename)\r\n        } catch (e) {\r\n          console.error('解码文件名失败', e)\r\n          // 如果解码失败，使用原始文件名\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 如果没有从响应头获取到文件名，则使用路径中的文件名\r\n    if (!filename) {\r\n      filename = row.material_path.split('/').pop()\r\n    }\r\n    \r\n    // 创建blob链接\r\n    const blob = new Blob([response.data])\r\n    const url = window.URL.createObjectURL(blob)\r\n    \r\n    // 创建临时链接并模拟点击下载\r\n    const link = document.createElement('a')\r\n    link.href = url\r\n    link.setAttribute('download', filename)\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    \r\n    // 清理\r\n    document.body.removeChild(link)\r\n    window.URL.revokeObjectURL(url)\r\n    \r\n  } catch (error) {\r\n    console.error('下载失败:', error)\r\n    ElMessage.error('下载课件失败，请检查权限或网络连接')\r\n  }\r\n}\r\n\r\n// 自定义上传请求处理\r\nconst customUploadRequest = (options) => {\r\n  // 存储文件对象\r\n  tempFile.value = options.file\r\n  \r\n  // 修改文件名以解决中文编码问题\r\n  const originalFileName = options.file.name\r\n  // 显示成功消息，包含原始文件名\r\n  ElMessage.success(`文件 ${originalFileName} 已选择`)\r\n  options.onSuccess()\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!courseFormRef.value) return\r\n  \r\n  await courseFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      submitting.value = true\r\n      try {\r\n        const formData = new FormData()\r\n        formData.append('title', courseForm.title)\r\n        formData.append('description', courseForm.description)\r\n        \r\n        if (tempFile.value) {\r\n          // 添加原始文件名到formData以供后端使用\r\n          formData.append('originalFileName', tempFile.value.name)\r\n          formData.append('material', tempFile.value)\r\n        }\r\n        \r\n        let response\r\n        if (courseForm.id) {\r\n          // 编辑模式\r\n          response = await http.put(`/api/courses/${courseForm.id}`, formData)\r\n          if (response.data.success) {\r\n            ElMessage.success(`课程 ${courseForm.title} 信息已更新`)\r\n          } else {\r\n            ElMessage.error(response.data.message || '更新课程失败')\r\n          }\r\n        } else {\r\n          // 新增模式\r\n          response = await http.post('/api/courses', formData)\r\n          if (response.data.success) {\r\n            ElMessage.success(`课程 ${courseForm.title} 添加成功`)\r\n          } else {\r\n            ElMessage.error(response.data.message || '添加课程失败')\r\n          }\r\n        }\r\n        \r\n        dialogVisible.value = false\r\n        fetchCourses()\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error)\r\n        ElMessage.error('提交失败，请检查网络连接')\r\n      } finally {\r\n        submitting.value = false\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 文件上传相关方法\r\nconst handlePreview = (file) => {\r\n  if (file.url) {\r\n    window.open(file.url, '_blank')\r\n  }\r\n}\r\n\r\nconst handleRemove = () => {\r\n  fileList.value = []\r\n  tempFile.value = null\r\n}\r\n\r\nconst handleUploadSuccess = () => {\r\n  // 这个方法不再需要显示成功消息，因为在customUploadRequest中已处理\r\n}\r\n\r\nconst beforeUpload = (file) => {\r\n  // 文件大小限制：10MB\r\n  const maxSize = 10 * 1024 * 1024\r\n  if (file.size > maxSize) {\r\n    ElMessage.error('文件大小不能超过10MB')\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  fetchCourses()\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchCourses()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.course-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.no-material {\r\n  color: #909399;\r\n}\r\n\r\n.material-upload {\r\n  width: 100%;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n}\r\n\r\n:deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n}\r\n</style> "], "mappings": ";;;;AAkHA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AACxD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,OAAO,GAAG,uBAAuB;;AAEvC;;;;;;;;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAAC;MACxBC,OAAO,EAAEH;IACX,CAAC,CAAC;;IAEF;IACAC,IAAI,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAACC,MAAM,IAAI;MACtC;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EAAEM,KAAK,IAAI;MACV,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CAAC,CAAC;;IAEF;IACA,MAAMG,UAAU,GAAIC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAG;MAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAOJ,UAAU;MAE5C,MAAMK,IAAI,GAAGJ,IAAI,CAACK,WAAW,CAAC,CAAC;MAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACP,IAAI,CAACQ,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACP,IAAI,CAACW,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,KAAK,GAAGL,MAAM,CAACP,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMK,OAAO,GAAGP,MAAM,CAACP,IAAI,CAACe,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMO,OAAO,GAAGT,MAAM,CAACP,IAAI,CAACiB,UAAU,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAE1D,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;IACjE,CAAC;IAED,MAAME,OAAO,GAAG5C,GAAG,CAAC,IAAI,CAAC;IACzB,MAAM6C,WAAW,GAAG7C,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM8C,QAAQ,GAAG9C,GAAG,CAAC,EAAE,CAAC;IACxB,MAAM+C,KAAK,GAAG/C,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMgD,aAAa,GAAGhD,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMiD,WAAW,GAAGjD,GAAG,CAAC,MAAM,CAAC;IAC/B,MAAMkD,aAAa,GAAGlD,GAAG,CAAC,IAAI,CAAC;IAC/B,MAAMmD,QAAQ,GAAGnD,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMoD,YAAY,GAAGpD,GAAG,CAAC,GAAGQ,OAAO,cAAc,CAAC,EAAC;IACnD,MAAM6C,UAAU,GAAGrD,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMsD,QAAQ,GAAGtD,GAAG,CAAC,IAAI,CAAC,EAAC;;IAE3B;IACA,MAAMuD,aAAa,GAAGpD,QAAQ,CAAC,MAAM;MACnC,OAAO;QACLiB,aAAa,EAAE,UAAUH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;MAC9D,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMsC,UAAU,GAAGxD,GAAG,CAAC,EAAE,CAAC;;IAE1B;IACA,MAAMyD,UAAU,GAAGxD,QAAQ,CAAC;MAC1ByD,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAU,GAAG1D,QAAQ,CAAC;MAC1B2D,EAAE,EAAE,IAAI;MACRF,KAAK,EAAE,EAAE;MACTG,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEF;IACA,MAAMC,WAAW,GAAG;MAClBL,KAAK,EAAE,CACL;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IAE3D,CAAC;;IAED;IACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BvB,OAAO,CAACwB,KAAK,GAAG,IAAI;MACpB,IAAI;QACF;QACA,MAAMC,QAAQ,GAAG,MAAM5D,IAAI,CAAC6D,GAAG,CAAC,cAAc,CAAC;QAC/C,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBhB,UAAU,CAACY,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI;UACrCxB,KAAK,CAACqB,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACE,KAAK;QACnC,CAAC,MAAM;UACLrE,SAAS,CAACiB,KAAK,CAACgD,QAAQ,CAACE,IAAI,CAACN,OAAO,IAAI,UAAU,CAAC;QACtD;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAIA,KAAK,CAACgD,QAAQ,IAAIhD,KAAK,CAACgD,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;UACnDvE,SAAS,CAACiB,KAAK,CAAC,aAAa,CAAC;UAC9B;UACA;QACF,CAAC,MAAM;UACLjB,SAAS,CAACiB,KAAK,CAAC,kBAAkB,CAAC;QACrC;MACF,CAAC,SAAS;QACRuB,OAAO,CAACwB,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;IAEDlE,SAAS,CAAC,MAAM;MACdiE,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;;IAEF;IACA,MAAMS,WAAW,GAAGA,CAAA,KAAM;MACxBnB,UAAU,CAACC,KAAK,GAAG,EAAE;MACrBS,YAAY,CAAC,CAAC;IAChB,CAAC;;IAED;IACA,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BjC,OAAO,CAACwB,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM5D,IAAI,CAAC6D,GAAG,CAAC,qBAAqB,EAAE;UACrDQ,MAAM,EAAE;YAAEC,OAAO,EAAEtB,UAAU,CAACC;UAAM;QACtC,CAAC,CAAC;QACF,IAAIW,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBhB,UAAU,CAACY,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI;UACrCxB,KAAK,CAACqB,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACE,KAAK;UACjCrE,SAAS,CAACoE,OAAO,CAAC,MAAM,CAAC;QAC3B,CAAC,MAAM;UACLpE,SAAS,CAACiB,KAAK,CAACgD,QAAQ,CAACE,IAAI,CAACN,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7BjB,SAAS,CAACiB,KAAK,CAAC,cAAc,CAAC;MACjC,CAAC,SAAS;QACRuB,OAAO,CAACwB,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMY,eAAe,GAAGA,CAAA,KAAM;MAC5B/B,WAAW,CAACmB,KAAK,GAAG,MAAM;MAC1BpB,aAAa,CAACoB,KAAK,GAAG,IAAI;MAC1B;MACAT,UAAU,CAACC,EAAE,GAAG,IAAI;MACpBD,UAAU,CAACD,KAAK,GAAG,EAAE;MACrBC,UAAU,CAACE,WAAW,GAAG,EAAE;MAC3BF,UAAU,CAACG,aAAa,GAAG,EAAE;MAC7BR,QAAQ,CAACc,KAAK,GAAG,IAAI;MACrBjB,QAAQ,CAACiB,KAAK,GAAG,EAAE;IACrB,CAAC;;IAED;IACA,MAAMa,UAAU,GAAIC,GAAG,IAAK;MAC1BjC,WAAW,CAACmB,KAAK,GAAG,MAAM;MAC1BpB,aAAa,CAACoB,KAAK,GAAG,IAAI;;MAE1B;MACAT,UAAU,CAACC,EAAE,GAAGsB,GAAG,CAACtB,EAAE;MACtBD,UAAU,CAACD,KAAK,GAAGwB,GAAG,CAACxB,KAAK;MAC5BC,UAAU,CAACE,WAAW,GAAGqB,GAAG,CAACrB,WAAW;MACxCF,UAAU,CAACG,aAAa,GAAGoB,GAAG,CAACpB,aAAa;MAC5CR,QAAQ,CAACc,KAAK,GAAG,IAAI;;MAErB;MACAjB,QAAQ,CAACiB,KAAK,GAAG,EAAE;MACnB,IAAIc,GAAG,CAACpB,aAAa,EAAE;QACrBX,QAAQ,CAACiB,KAAK,CAACe,IAAI,CAAC;UAClBC,IAAI,EAAEF,GAAG,CAACpB,aAAa,CAACuB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UACxCC,GAAG,EAAE,GAAG/E,OAAO,IAAI0E,GAAG,CAACpB,aAAa;QACtC,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA,MAAM0B,YAAY,GAAIN,GAAG,IAAK;MAC5B7E,YAAY,CAACoF,OAAO,CAAC,WAAWP,GAAG,CAACxB,KAAK,KAAK,EAAE,IAAI,EAAE;QACpDgC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAMxB,QAAQ,GAAG,MAAM5D,IAAI,CAACqF,MAAM,CAAC,gBAAgBZ,GAAG,CAACtB,EAAE,EAAE,CAAC;UAC5D,IAAIS,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;YACzBpE,SAAS,CAACoE,OAAO,CAAC,MAAMU,GAAG,CAACxB,KAAK,MAAM,CAAC;YACxCS,YAAY,CAAC,CAAC;UAChB,CAAC,MAAM;YACL/D,SAAS,CAACiB,KAAK,CAACgD,QAAQ,CAACE,IAAI,CAACN,OAAO,IAAI,QAAQ,CAAC;UACpD;QACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;UACdqD,OAAO,CAACrD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BjB,SAAS,CAACiB,KAAK,CAAC,gBAAgB,CAAC;QACnC;MACF,CAAC,CAAC,CAAC0E,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMC,cAAc,GAAG,MAAOd,GAAG,IAAK;MACpC,IAAI,CAACA,GAAG,CAACpB,aAAa,EAAE;QACtB1D,SAAS,CAAC6F,OAAO,CAAC,WAAW,CAAC;QAC9B;MACF;MAEA,IAAI;QACF7F,SAAS,CAACoE,OAAO,CAAC,QAAQU,GAAG,CAACxB,KAAK,MAAM,CAAC;;QAE1C;QACA,MAAMW,QAAQ,GAAG,MAAM5D,IAAI,CAAC6D,GAAG,CAAC,gBAAgBY,GAAG,CAACtB,EAAE,WAAW,EAAE;UACjEsC,YAAY,EAAE,MAAM,CAAC;QACvB,CAAC,CAAC;;QAEF;QACA,IAAIC,QAAQ,GAAG,EAAE;QACjB,MAAMC,kBAAkB,GAAG/B,QAAQ,CAAClD,OAAO,CAAC,qBAAqB,CAAC;QAClE,IAAIiF,kBAAkB,EAAE;UACtB,MAAMC,aAAa,GAAGD,kBAAkB,CAACE,KAAK,CAAC,wCAAwC,CAAC;UACxF,IAAID,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC,EAAE;YACrCF,QAAQ,GAAGE,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,IAAI;cACF;cACAJ,QAAQ,GAAGK,kBAAkB,CAACL,QAAQ,CAAC;YACzC,CAAC,CAAC,OAAOM,CAAC,EAAE;cACV/B,OAAO,CAACrD,KAAK,CAAC,SAAS,EAAEoF,CAAC,CAAC;cAC3B;YACF;UACF;QACF;;QAEA;QACA,IAAI,CAACN,QAAQ,EAAE;UACbA,QAAQ,GAAGjB,GAAG,CAACpB,aAAa,CAACuB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;QAC/C;;QAEA;QACA,MAAMoB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACtC,QAAQ,CAACE,IAAI,CAAC,CAAC;QACtC,MAAMgB,GAAG,GAAGqB,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;QAE5C;QACA,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAG3B,GAAG;QACfwB,IAAI,CAACI,YAAY,CAAC,UAAU,EAAEhB,QAAQ,CAAC;QACvCa,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;;QAEZ;QACAN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACjC,GAAG,CAAC;MAEjC,CAAC,CAAC,OAAOlE,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7BjB,SAAS,CAACiB,KAAK,CAAC,mBAAmB,CAAC;MACtC;IACF,CAAC;;IAED;IACA,MAAMoG,mBAAmB,GAAIC,OAAO,IAAK;MACvC;MACApE,QAAQ,CAACc,KAAK,GAAGsD,OAAO,CAACC,IAAI;;MAE7B;MACA,MAAMC,gBAAgB,GAAGF,OAAO,CAACC,IAAI,CAACvC,IAAI;MAC1C;MACAhF,SAAS,CAACoE,OAAO,CAAC,MAAMoD,gBAAgB,MAAM,CAAC;MAC/CF,OAAO,CAACG,SAAS,CAAC,CAAC;IACrB,CAAC;;IAED;IACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAAC5E,aAAa,CAACkB,KAAK,EAAE;MAE1B,MAAMlB,aAAa,CAACkB,KAAK,CAAC2D,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAClD,IAAIA,KAAK,EAAE;UACT3E,UAAU,CAACe,KAAK,GAAG,IAAI;UACvB,IAAI;YACF,MAAM6D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;YAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAExE,UAAU,CAACD,KAAK,CAAC;YAC1CuE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExE,UAAU,CAACE,WAAW,CAAC;YAEtD,IAAIP,QAAQ,CAACc,KAAK,EAAE;cAClB;cACA6D,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE7E,QAAQ,CAACc,KAAK,CAACgB,IAAI,CAAC;cACxD6C,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE7E,QAAQ,CAACc,KAAK,CAAC;YAC7C;YAEA,IAAIC,QAAQ;YACZ,IAAIV,UAAU,CAACC,EAAE,EAAE;cACjB;cACAS,QAAQ,GAAG,MAAM5D,IAAI,CAAC2H,GAAG,CAAC,gBAAgBzE,UAAU,CAACC,EAAE,EAAE,EAAEqE,QAAQ,CAAC;cACpE,IAAI5D,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;gBACzBpE,SAAS,CAACoE,OAAO,CAAC,MAAMb,UAAU,CAACD,KAAK,QAAQ,CAAC;cACnD,CAAC,MAAM;gBACLtD,SAAS,CAACiB,KAAK,CAACgD,QAAQ,CAACE,IAAI,CAACN,OAAO,IAAI,QAAQ,CAAC;cACpD;YACF,CAAC,MAAM;cACL;cACAI,QAAQ,GAAG,MAAM5D,IAAI,CAAC4H,IAAI,CAAC,cAAc,EAAEJ,QAAQ,CAAC;cACpD,IAAI5D,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;gBACzBpE,SAAS,CAACoE,OAAO,CAAC,MAAMb,UAAU,CAACD,KAAK,OAAO,CAAC;cAClD,CAAC,MAAM;gBACLtD,SAAS,CAACiB,KAAK,CAACgD,QAAQ,CAACE,IAAI,CAACN,OAAO,IAAI,QAAQ,CAAC;cACpD;YACF;YAEAjB,aAAa,CAACoB,KAAK,GAAG,KAAK;YAC3BD,YAAY,CAAC,CAAC;UAChB,CAAC,CAAC,OAAO9C,KAAK,EAAE;YACdqD,OAAO,CAACrD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;YAC/BjB,SAAS,CAACiB,KAAK,CAAC,cAAc,CAAC;UACjC,CAAC,SAAS;YACRgC,UAAU,CAACe,KAAK,GAAG,KAAK;UAC1B;QACF,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMkE,aAAa,GAAIX,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAACpC,GAAG,EAAE;QACZqB,MAAM,CAAC2B,IAAI,CAACZ,IAAI,CAACpC,GAAG,EAAE,QAAQ,CAAC;MACjC;IACF,CAAC;IAED,MAAMiD,YAAY,GAAGA,CAAA,KAAM;MACzBrF,QAAQ,CAACiB,KAAK,GAAG,EAAE;MACnBd,QAAQ,CAACc,KAAK,GAAG,IAAI;IACvB,CAAC;IAED,MAAMqE,mBAAmB,GAAGA,CAAA,KAAM;MAChC;IAAA,CACD;IAED,MAAMC,YAAY,GAAIf,IAAI,IAAK;MAC7B;MACA,MAAMgB,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;MAChC,IAAIhB,IAAI,CAACiB,IAAI,GAAGD,OAAO,EAAE;QACvBvI,SAAS,CAACiB,KAAK,CAAC,cAAc,CAAC;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;;IAED;IACA,MAAMwH,gBAAgB,GAAID,IAAI,IAAK;MACjC9F,QAAQ,CAACsB,KAAK,GAAGwE,IAAI;MACrB/F,WAAW,CAACuB,KAAK,GAAG,CAAC;MACrBD,YAAY,CAAC,CAAC;IAChB,CAAC;IAED,MAAM2E,mBAAmB,GAAIC,IAAI,IAAK;MACpClG,WAAW,CAACuB,KAAK,GAAG2E,IAAI;MACxB5E,YAAY,CAAC,CAAC;IAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
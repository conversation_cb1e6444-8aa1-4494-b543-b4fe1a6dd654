{"version": 3, "file": "js/404.cb454656.js", "mappings": "4kCAgXA,MAAMA,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAcD,EAAAA,EAAAA,IAAI,GAClBE,GAAWF,EAAAA,EAAAA,IAAI,IACfG,GAAQH,EAAAA,EAAAA,IAAI,GACZI,GAAgBJ,EAAAA,EAAAA,KAAI,GACpBK,GAAcL,EAAAA,EAAAA,IAAI,QAClBM,GAAcN,EAAAA,EAAAA,IAAI,MAClBO,GAAqBP,EAAAA,EAAAA,IAAI,MAC/B,IAAIQ,EAAWC,aAAaC,QAAQ,YAAcC,KAAKC,MAAMH,aAAaC,QAAQ,aAAe,CAAC,EAElG,MAAMG,GAAWb,EAAAA,EAAAA,IAAI,IACfc,GAAUd,EAAAA,EAAAA,IAAsB,SAAlBQ,GAAUO,MACxBC,GAAYhB,EAAAA,EAAAA,IAAsB,WAAlBQ,GAAUO,MAC1BE,GAAYjB,EAAAA,EAAAA,IAAsB,WAAlBQ,GAAUO,MAChCG,QAAQC,IAAIF,EAAWD,EAAW,UAClC,MAAMI,GAAiBpB,EAAAA,EAAAA,IAAuB,YAAnBQ,GAAUO,OAGrCM,EAAAA,EAAAA,IAAM,IAAM1B,EAAM2B,MAAMC,KAAOC,IAC7BN,QAAQC,IAAI,UAAWK,GACvBC,KACC,CAAEC,MAAM,IAGX,MAAMD,EAAgBA,KAEpBZ,EAASc,MAAQlB,aAAaC,QAAQ,aAAe,GACrDQ,QAAQC,IAAI,UAAWN,EAASc,OAChCT,QAAQC,IAAI,SAAUL,EAAQa,OAC9BT,QAAQC,IAAI,QAASH,EAAUW,OAC/BT,QAAQC,IAAI,QAASF,EAAUU,OAC/BT,QAAQC,IAAI,SAAUC,EAAeO,QAIjCC,IAAwB5B,EAAAA,EAAAA,KAAI,GAC5B6B,IAA4B7B,EAAAA,EAAAA,KAAI,GAChC8B,IAAsB9B,EAAAA,EAAAA,IAAI,QAC1B+B,IAAkB/B,EAAAA,EAAAA,IAAI,MACtBgC,IAAchC,EAAAA,EAAAA,IAAI,MAClBiC,IAAejC,EAAAA,EAAAA,IAAI,IACnBkC,IAAkBlC,EAAAA,EAAAA,KAAI,GACtBmC,IAAsBnC,EAAAA,EAAAA,IAAI,GAC1BoC,IAAgBpC,EAAAA,EAAAA,IAAI,GAGpBqC,IAAsBrC,EAAAA,EAAAA,KAAI,GAC1BsC,IAAWtC,EAAAA,EAAAA,IAAI,IACfuC,IAAgBvC,EAAAA,EAAAA,KAAI,GAGpBwC,IAAuBxC,EAAAA,EAAAA,KAAI,GAC3ByC,IAAczC,EAAAA,EAAAA,IAAI,IAClB0C,IAAiB1C,EAAAA,EAAAA,KAAI,GACrB2C,IAAiBC,EAAAA,EAAAA,IAAS,CAC9BC,eAAgB,EAChBC,gBAAiB,EACjBC,UAAW,EACXC,cAAe,IAIXC,IAAoBjD,EAAAA,EAAAA,IAAI,CAAC,GACzBkD,IAAiBlD,EAAAA,EAAAA,IAAI,CAAC,GAStBmD,IAAaP,EAAAA,EAAAA,IAAS,CAC1BQ,MAAO,KAIHC,IAAWT,EAAAA,EAAAA,IAAS,CACxBU,GAAI,KACJF,MAAO,GACPG,YAAa,GACbC,SAAU,GACVC,WAAY,GACZC,YAAa,MAITC,GAAY,CAChBP,MAAO,CACL,CAAEQ,UAAU,EAAMC,QAAS,UAAWC,QAAS,SAEjDN,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,UAAWC,QAAS,QAC/C,CAAEC,KAAM,SAAUC,IAAK,EAAGH,QAAS,YAAaC,QAAS,SAE3DL,WAAY,CACV,CAAEG,UAAU,EAAMC,QAAS,UAAWC,QAAS,QAC/C,CAAEC,KAAM,SAAUC,IAAK,EAAGH,QAAS,YAAaC,QAAS,SAE3DJ,YAAa,CACX,CAAEE,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,KAAM,SAAUC,IAAK,EAAGH,QAAS,UAAWC,QAAS,UAKrDG,IAAerB,EAAAA,EAAAA,IAAS,CAC5BU,GAAI,KACJY,SAAU,GACVC,cAAe,MACfC,eAAgB,GAChBC,MAAO,EACPC,QAAS,CACP,CAAEC,KAAM,GAAIC,WAAW,GACvB,CAAED,KAAM,GAAIC,WAAW,GACvB,CAAED,KAAM,GAAIC,WAAW,GACvB,CAAED,KAAM,GAAIC,WAAW,MAKrBC,GAAgB,CACpBP,SAAU,CACR,CAAEN,UAAU,EAAMC,QAAS,UAAWC,QAAS,SAEjDK,cAAe,CACb,CAAEP,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAEjDO,MAAO,CACL,CAAET,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,KAAM,SAAUC,IAAK,EAAGH,QAAS,UAAWC,QAAS,UAKrDY,IAAW1E,EAAAA,EAAAA,IAAI,IAyFrBG,EAAMwB,MAAQ+C,GAASC,OAGvB,MAAMC,GAAaC,UACjB9E,EAAQ4B,OAAQ,EAGhBF,IAGIhB,aAAaC,QAAQ,aAAef,EAAM2B,MAAMP,OAASN,aAAaC,QAAQ,aAChFf,EAAMmF,OAAO,WAAYrE,aAAaC,QAAQ,aAGhD,IACE,MAAMqE,QAAiBC,EAAAA,EAAYC,SAAS,CAC1CC,KAAMjF,EAAY0B,MAClBwD,MAAOjF,EAASyB,MAChByB,MAAOD,GAAWC,QAGpBsB,GAAS/C,MAAQoD,EAASK,KAAKA,KAC/BjF,EAAMwB,MAAQoD,EAASK,KAAKjF,OAASuE,GAAS/C,MAAMgD,OAGhD1D,EAAUU,aACN0D,KAGRtF,EAAQ4B,OAAQ,CAClB,CAAE,MAAO2D,GACPpE,QAAQoE,MAAM,WAAYA,GAC1BC,EAAAA,GAAUD,MAAM,YAChBvF,EAAQ4B,OAAQ,CAClB,GAII0D,GAAyBR,UAI7B,IACE3D,QAAQC,IAAI,wBAAyBqE,WAErC,MAAMC,EAAkBf,GAAS/C,MAAM+D,IAAIb,UACzC,IACE3D,QAAQC,IAAI,UAAUwE,EAAKrC,OAAOqC,EAAKvC,mBACvC,MAAM2B,QAAiBC,EAAAA,EAAYY,eAAeD,EAAKrC,GAAI,CAAEuC,WAAYL,YAIzE,GAHAtE,QAAQC,IAAI,MAAMwE,EAAKrC,WAAYyB,EAASK,MAGxCL,EAASK,MAAQL,EAASK,KAAKA,KAAM,CACvC,MAAMU,EAAUf,EAASK,KAAKA,KAAKU,SAAW,GACxCC,EAAWD,EAAQnB,OACnBqB,EAAYC,KAAKC,IAAI,EAAG,EAAIH,GAElC7E,QAAQC,IAAI,MAAMwE,EAAKrC,OAAOqC,EAAKvC,eAAe2C,UAAiBC,OACnE/C,GAAkBtB,MAAMgE,EAAKrC,IAAM0C,EACnC9C,GAAevB,MAAMgE,EAAKrC,IAAMyC,EAAW,EAGvCD,EAAQnB,OAAS,GACnBmB,EAAQK,QAAQ,CAACC,EAAQC,KACvBnF,QAAQC,IAAI,QAAQkF,EAAQ,SAASD,EAAO/B,YAAY+B,EAAOE,cAGrE,MACEpF,QAAQqF,KAAK,MAAMZ,EAAKrC,yBACxBL,GAAkBtB,MAAMgE,EAAKrC,IAAM,EACnCJ,GAAevB,MAAMgE,EAAKrC,KAAM,CAEpC,CAAE,MAAOkD,GACPtF,QAAQoE,MAAM,QAAQK,EAAKrC,aAAckD,GACzCvD,GAAkBtB,MAAMgE,EAAKrC,IAAM,EACnCJ,GAAevB,MAAMgE,EAAKrC,KAAM,CAClC,UAIImD,QAAQC,IAAIjB,GAClBvE,QAAQC,IAAI,gBAAiB8B,GAAkBtB,OAC/CT,QAAQC,IAAI,WAAY+B,GAAevB,MACzC,CAAE,MAAO2D,GACPpE,QAAQoE,MAAM,cAAeA,GAE7BZ,GAAS/C,MAAMwE,QAAQR,IACrB1C,GAAkBtB,MAAMgE,EAAKrC,IAAM,EACnCJ,GAAevB,MAAMgE,EAAKrC,KAAM,GAEpC,GAIIqD,GAAkBC,IAKtB,GAHArG,EAAmBoB,MAAQiF,EAAItD,IAG1BL,GAAkBtB,MAAMiF,EAAItD,KAA2C,IAApCL,GAAkBtB,MAAMiF,EAAItD,IAAW,CAE7E,MAAMkC,EAAYhF,EAASqF,WAC3B,OAAKL,GAMLD,EAAAA,GAAUsB,KAAK,0BAGf7B,EAAAA,EAAYY,eAAegB,EAAItD,GAAI,CAAEuC,WAAYL,IAC9CsB,KAAK/B,IAGJ,GAFA7D,QAAQC,IAAI,MAAMyF,EAAItD,WAAYyB,EAASK,MAEvCL,EAASK,MAAQL,EAASK,KAAKA,KAAM,CACvC,MAAMU,EAAUf,EAASK,KAAKA,KAAKU,SAAW,GACxCC,EAAWD,EAAQnB,OACnBqB,EAAYC,KAAKC,IAAI,EAAG,EAAIH,GAElC7E,QAAQC,IAAI,MAAMyF,EAAItD,OAAOsD,EAAIxD,eAAe2C,UAAiBC,OACjE/C,GAAkBtB,MAAMiF,EAAItD,IAAM0C,EAClC9C,GAAevB,MAAMiF,EAAItD,IAAMyC,EAAW,EAG1CxF,EAAmBoB,MAAQ,KAC3BoF,GAAmBH,EACrB,MACE1F,QAAQqF,KAAK,MAAMK,EAAItD,yBACvBL,GAAkBtB,MAAMiF,EAAItD,IAAM,EAClCJ,GAAevB,MAAMiF,EAAItD,KAAM,EAC/B/C,EAAmBoB,MAAQ,KAC3BoF,GAAmBH,KAGtBI,MAAM1B,IACLpE,QAAQoE,MAAM,aAAcA,GAC5BC,EAAAA,GAAUD,MAAM,sBAChB/E,EAAmBoB,MAAQ,SAnC7B4D,EAAAA,GAAU0B,QAAQ,yBAClB1G,EAAmBoB,MAAQ,MAqC/B,CAEApB,EAAmBoB,MAAQ,KAC3BoF,GAAmBH,IAIfG,GAAsBH,IAEc,IAApC3D,GAAkBtB,MAAMiF,EAAItD,IAMhC4D,EAAAA,EAAaC,QACX,kBAAkB,EAAIlE,GAAkBtB,MAAMiF,EAAItD,YAAYL,GAAkBtB,MAAMiF,EAAItD,oBAC1F,OACA,CACE8D,kBAAmB,KACnBC,iBAAkB,KAClBtD,KAAM,YAER+C,KAAK,KAELrG,aAAa6G,QAAQ,gBAAiBV,EAAItD,IAC1C7C,aAAa6G,QAAQ,mBAAoBV,EAAIxD,OAC7C3C,aAAa6G,QAAQ,+BAAgCrE,GAAkBtB,MAAMiF,EAAItD,KAGjFzD,EAAO0H,KAAK,eAAeX,EAAItD,QAC9B0D,MAAM,KACP9F,QAAQC,IAAI,cAtBZoE,EAAAA,GAAU0B,QAAQ,wBAwFtBO,EAAAA,EAAAA,IAAU,KAERC,KACA7C,OAIF,MAAM6C,GAAsBA,KAE1B,MAAMC,EAAkBjH,aAAaC,QAAQ,YAG7C,GAAIgH,EAAiB,CACnBxG,QAAQC,IAAI,qBAAsBuG,GAClC/H,EAAMmF,OAAO,WAAY4C,GAGzB,MAAMC,EAASlH,aAAaC,QAAQ,UAChCiH,GAEFhI,EAAMiI,SAAS,oBAAoBZ,MAAMR,IACvCtF,QAAQoE,MAAM,YAAakB,IAGjC,MACEtF,QAAQqF,KAAK,aAIf9E,KAIIoG,GAAcA,KAClB1E,GAAWC,MAAQ,GACnB0E,MAIIA,GAAeA,KACnB7H,EAAY0B,MAAQ,EACpBiD,MAIImD,GAAgBA,KACpB1H,EAAYsB,MAAQ,OACpBvB,EAAcuB,OAAQ,EAEtB0B,GAASC,GAAK,KACdD,GAASD,MAAQ,GACjBC,GAASE,YAAc,GACvBF,GAASG,SAAW,GACpBH,GAASI,WAAa,GACtBJ,GAASK,YAAc,KAInBsE,GAAcpB,IAClBvG,EAAYsB,MAAQ,OACpBvB,EAAcuB,OAAQ,EAGtBqD,EAAAA,EAAYiD,QAAQrB,EAAItD,IACrBwD,KAAK/B,IAEJ,MAAMmD,EAAWnD,EAASK,KAAKA,KAC/B+C,OAAOC,KAAK/E,IAAU8C,QAAQkC,IAExB,CAAC,WAAY,aAAc,eAAeC,SAASD,GACrDhF,GAASgF,GAAOE,OAAOL,EAASG,IAAQ,GAExChF,GAASgF,GAAOH,EAASG,OAI9BrB,MAAM1B,IACLpE,QAAQoE,MAAM,WAAYA,GAC1BC,EAAAA,GAAUD,MAAM,YAGhB6C,OAAOC,KAAK/E,IAAU8C,QAAQkC,IAC5BhF,GAASgF,GAAOzB,EAAIyB,QAMtBG,GAAgB5B,IACpBM,EAAAA,EAAaC,QAAQ,WAAWP,EAAIxD,WAAY,KAAM,CACpDgE,kBAAmB,KACnBC,iBAAkB,KAClBtD,KAAM,YACL+C,KAAKjC,UACN,UACQG,EAAAA,EAAYyD,WAAW7B,EAAItD,IACjCiC,EAAAA,GAAUmD,QAAQ,MAAM9B,EAAIxD,aAC5BwB,IACF,CAAE,MAAOU,GACPpE,QAAQoE,MAAM,SAAUA,GACxBC,EAAAA,GAAUD,MAAM,aAClB,IACC0B,MAAM,SAIL2B,GAAa9D,UACZvE,EAAYqB,aAEXrB,EAAYqB,MAAMiH,SAAS/D,UAC/B,IAAIgE,EAkBF,OAAO,EAjBP,IACMxF,GAASC,UAEL0B,EAAAA,EAAY8D,WAAWzF,GAASC,GAAID,IAC1CkC,EAAAA,GAAUmD,QAAQ,MAAMrF,GAASD,uBAG3B4B,EAAAA,EAAY+D,WAAW1F,IAC7BkC,EAAAA,GAAUmD,QAAQ,MAAMrF,GAASD,eAEnChD,EAAcuB,OAAQ,EACtBiD,IACF,CAAE,MAAOU,GACPpE,QAAQoE,MAAM,SAAUA,GACxBC,EAAAA,GAAUD,MAAM,aAClB,KAQA0D,GAAyBpC,IAC7B5E,GAAYL,MAAQiF,EACpBhF,GAAsBD,OAAQ,EAE9BQ,GAAoBR,MAAQ,EAG5BsH,GAAcrC,EAAItD,IAGlB4F,WAAW,KACT,MAAMC,EAAgBC,SAASC,cAAc,qCACzCF,IACFA,EAAcG,MAAMC,OAAS,UAE9B,MAICN,GAAgBpE,UACpB3C,GAAgBP,OAAQ,EAExB,IAEE,MAAMoD,QAAiBC,EAAAA,EAAYwE,iBAAiBC,GAGpDvI,QAAQC,IAAI,YAAa4D,EAASK,MAG9BL,EAASK,MAAQL,EAASK,KAAKA,MACjCnD,GAAaN,MAAQoD,EAASK,KAAKA,KAEnChD,GAAcT,MAAQoD,EAASK,KAAKsE,OAASzH,GAAaN,MAAMgD,OAChEzD,QAAQC,IAAI,OAAOc,GAAaN,MAAMgD,gBAEtC1C,GAAaN,MAAQ,GACrBS,GAAcT,MAAQ,EACtBT,QAAQqF,KAAK,YAGfrE,GAAgBP,OAAQ,CAC1B,CAAE,MAAO2D,GACPpE,QAAQoE,MAAM,SAAUA,GACxBC,EAAAA,GAAUD,MAAM,UAChBpD,GAAgBP,OAAQ,CAC1B,GAIIgI,GAAoBA,KACxB7H,GAAoBH,MAAQ,OAC5BE,GAA0BF,OAAQ,EAElCsC,GAAaX,GAAK,KAClBW,GAAaC,SAAW,GACxBD,GAAaE,cAAgB,MAC7BF,GAAaG,eAAiB,GAC9BH,GAAaI,MAAQ,EACrBJ,GAAaK,QAAU,CACrB,CAAEC,KAAM,GAAIC,WAAW,GACvB,CAAED,KAAM,GAAIC,WAAW,GACvB,CAAED,KAAM,GAAIC,WAAW,GACvB,CAAED,KAAM,GAAIC,WAAW,KAKrBoF,GAAsBhD,IAC1B9E,GAAoBH,MAAQ,OAC5BE,GAA0BF,OAAQ,EAGlCsC,GAAaX,GAAKsD,EAAItD,GACtBW,GAAaC,SAAW0C,EAAI1C,SAC5BD,GAAaE,cAAgByC,EAAIzC,cACjCF,GAAaG,eAAiBwC,EAAIxC,eAClCH,GAAaI,MAAQuC,EAAIvC,MAGC,QAAtBuC,EAAIzC,eAAiD,QAAtByC,EAAIzC,cACrCF,GAAaK,QAAUsC,EAAItC,QAAU,IAAIsC,EAAItC,SAAW,GACzB,QAAtBsC,EAAIzC,gBACbF,GAAaG,eAAiBwC,EAAIxC,iBAKhCyF,GAAwBjD,IAC5BM,EAAAA,EAAaC,QAAQ,aAAc,KAAM,CACvCC,kBAAmB,KACnBC,iBAAkB,KAClBtD,KAAM,YACL+C,KAAKjC,UACN,UACQG,EAAAA,EAAY8E,eAAelD,EAAItD,IACrCiC,EAAAA,GAAUmD,QAAQ,SAElBO,GAAcjH,GAAYL,MAAM2B,GAClC,CAAE,MAAOgC,GACPpE,QAAQoE,MAAM,SAAUA,GACxBC,EAAAA,GAAUD,MAAM,aAClB,IACC0B,MAAM,SAIL+C,GAAwBA,KAC5B1H,GAAoBV,OAAQ,EAC5BW,GAASX,MAAQ,IAIbqI,GAAgBC,IACpB,MAAMC,EACU,uBAAdD,EAAKlG,MACS,4EAAdkG,EAAKlG,KAEP,IAAKmG,EAEH,OADA3E,EAAAA,GAAUD,MAAM,kBACT,EAGT,MAAM6E,EAAUF,EAAKG,KAAO,KAAO,KAAO,GAE1C,QAAKD,IACH5E,EAAAA,GAAUD,MAAM,kBACT,IAOL+E,GAAmBA,CAACJ,EAAMK,KAE9BpJ,QAAQC,IAAI,UAAW8I,EAAMK,GAC7BhI,GAASX,MAAQ2I,GAIbC,GAAoBjG,IACxB,MAAM,KAAE2F,GAAS3F,EAGjB,OAAO,GAIHkG,GAAe3F,UAEnB,GADA3D,QAAQC,IAAI,UAAWmB,GAASX,OAC3BW,GAASX,OAAmC,IAA1BW,GAASX,MAAMgD,OAAtC,CAKApC,GAAcZ,OAAQ,EAEtB,IAEE,MAAM8I,EAAW,IAAIC,SAEfC,EAAarI,GAASX,MAAM,GAC5BiJ,EAAUD,EAAWE,KAAOF,EAClCzJ,QAAQC,IAAI,QAASyJ,GAGrBH,EAASK,OAAO,WAAYF,GAG5B,MAAM7F,QAAiBC,EAAAA,EAAY+F,wBAAwB/I,GAAYL,MAAM2B,GAAImH,GAGjFlF,EAAAA,GAAUmD,QAAQ,aAAa3D,EAASK,KAAKsE,OAAS,QACtDrH,GAAoBV,OAAQ,EAG5BsH,GAAcjH,GAAYL,MAAM2B,GAClC,CAAE,MAAOgC,GACPpE,QAAQoE,MAAM,SAAUA,GACxB,IAAI0F,EAAW,SAGX1F,EAAMP,UAAYO,EAAMP,SAASK,MAAQE,EAAMP,SAASK,KAAKvB,UAC/DmH,GAAY,IAAI1F,EAAMP,SAASK,KAAKvB,WAGtC0B,EAAAA,GAAUD,MAAM0F,EAClB,CAAE,QACAzI,GAAcZ,OAAQ,CACxB,CApCA,MAFE4D,EAAAA,GAAU0B,QAAQ,cA0ChBgE,GAAmBpG,UACvB,IACEU,EAAAA,GAAUmD,QAAQ,YAGlB,MAAMwC,EAAO9B,SAAS+B,cAAc,KAEpCD,EAAKE,KAAO,SAAGC,GAAAA,cAAgC,wDAG/C,MAAMC,EAAQ7K,aAAaC,QAAQ,SAC/B4K,IACFJ,EAAKE,MAAQ,UAAUE,KAGzBJ,EAAKK,SAAW,cAChBnC,SAASoC,KAAKC,YAAYP,GAC1BA,EAAKQ,QACLtC,SAASoC,KAAKG,YAAYT,EAC5B,CAAE,MAAO5F,GACPpE,QAAQoE,MAAM,SAAUA,GACxBC,EAAAA,GAAUD,MAAM,aAClB,GAIIsG,GAAYA,KAChB3H,GAAaK,QAAQiD,KAAK,CAAEhD,KAAM,GAAIC,WAAW,KAI7CqH,GAAgBxF,IAChBpC,GAAaK,QAAQK,QAAU,EACjCY,EAAAA,GAAU0B,QAAQ,YAGpBhD,GAAaK,QAAQwH,OAAOzF,EAAO,IAI/B0F,GAAqBlH,UACpB9C,GAAgBJ,aAEfI,GAAgBJ,MAAMiH,SAAS/D,UACnC,IAAIgE,EAmEF,OAAO,EAjEP,GAAoC,QAA/B5E,GAAaE,eAA0D,QAA/BF,GAAaE,cAA0B,CAElF,MAAM6H,EAAc/H,GAAaK,QAAQ2H,KAAKC,IAAQA,EAAI3H,KAAK4H,QAC/D,GAAIH,EAEF,YADAzG,EAAAA,GAAUD,MAAM,YAKlB,MAAM8G,EAAanI,GAAaK,QAAQ+H,KAAKH,GAAOA,EAAI1H,WACxD,IAAK4H,EAEH,YADA7G,EAAAA,GAAUD,MAAM,eAKlB,GAAmC,QAA/BrB,GAAaE,cAAyB,CACxC,MAAMmI,EAAerI,GAAaK,QAAQiI,OAAOL,GAAOA,EAAI1H,WAAWG,OACvE,GAAI2H,EAAe,EAEjB,YADA/G,EAAAA,GAAUD,MAAM,eAGpB,CACF,CAEA,IAEE,MAAMkH,EAAa,IAAKvI,IAGxB,GAAmC,QAA/BA,GAAaE,cAAyB,CACxC,MAAMsI,EAAgBxI,GAAaK,QAAQ2H,KAAKC,GAAOA,EAAI1H,WAC3D,GAAIiI,EAAe,CAEjB,MAAMpG,EAAQpC,GAAaK,QAAQoI,UAAUR,GAAOA,EAAI1H,WACxDgI,EAAWpI,eAAiBuI,OAAOC,aAAa,GAAKvG,EACvD,CACF,MAAO,GAAmC,QAA/BpC,GAAaE,cAAyB,CAE/C,MAAM0I,EAAiB5I,GAAaK,QACjCoB,IAAI,CAACwG,EAAK7F,IAAU6F,EAAI1H,UAAYmI,OAAOC,aAAa,GAAKvG,GAAS,MACtEkG,OAAOO,SACPC,KAAK,IACRP,EAAWpI,eAAiByI,CAC9B,CAGI5I,GAAaX,UAET0B,EAAAA,EAAYgI,eAAe/I,GAAaX,GAAIkJ,GAClDjH,EAAAA,GAAUmD,QAAQ,kBAGZ1D,EAAAA,EAAYiI,eAAejL,GAAYL,MAAM2B,GAAIkJ,GACvDjH,EAAAA,GAAUmD,QAAQ,WAGpB7G,GAA0BF,OAAQ,EAElCsH,GAAcjH,GAAYL,MAAM2B,GAClC,CAAE,MAAOgC,GACPpE,QAAQoE,MAAM,SAAUA,GACxBC,EAAAA,GAAUD,MAAM,aAClB,KAQA4H,GAAyBC,GACxBA,EAEEA,EAAQxI,OAAS,IAAMwI,EAAQC,UAAU,EAAG,KAAO,MAAQD,EAF7C,GAQjBE,GAAoBxI,UACxB7C,GAAYL,MAAQiF,EACpBpE,GAAqBb,OAAQ,EAC7Be,GAAef,OAAQ,EAEvB,IACE,MAAMoD,QAAiBC,EAAAA,EAAYY,eAAegB,EAAItD,IAChD8B,EAAOL,EAASK,KAAKA,KAE3B3C,GAAYd,MAAQyD,EAAKU,SAAW,GAGpCnD,GAAeE,eAAiBuC,EAAKkI,QAAQzK,gBAAkB,EAC/DF,GAAeG,gBAAkBsC,EAAKkI,QAAQxK,iBAAmB,EACjEH,GAAeI,UAAYqC,EAAKkI,QAAQvK,WAAa,EACrDJ,GAAeK,cAAgBoC,EAAKkI,QAAQtK,eAAiB,EAE7DN,GAAef,OAAQ,CACzB,CAAE,MAAO2D,GACPpE,QAAQoE,MAAM,WAAYA,GAC1BC,EAAAA,GAAUD,MAAM,YAChB5C,GAAef,OAAQ,CACzB,GAII4L,GAAoB3G,IACxBrB,EAAAA,GAAUmD,QAAQ,MAAM9B,EAAI4G,uBAIxBC,GAAoBrD,IACxBlK,EAASyB,MAAQyI,EACjBnK,EAAY0B,MAAQ,EACpBiD,MAGI8I,GAAuBxI,IAC3BjF,EAAY0B,MAAQuD,EACpBN,M,skBA7yCA+I,EAAAA,EAAAA,IAmWM,MAnWNC,EAmWM,EAlWJC,EAAAA,EAAAA,IAYUC,EAAA,CAZDC,MAAM,eAAa,C,iBAC1B,IAUM,EAVNC,EAAAA,EAAAA,IAUM,MAVNC,EAUM,EATJJ,EAAAA,EAAAA,IAQUK,EAAA,CARAC,MAAOhL,GAAYiL,OAAA,I,kBAC3B,IAEe,EAFfP,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,QAAM,C,iBACxB,IAAgF,EAAhFT,EAAAA,EAAAA,IAAgFU,EAAA,C,WAA7DpL,GAAWC,M,qCAAXD,GAAWC,MAAKoL,GAAEC,YAAY,UAAUC,UAAA,I,gCAE7Db,EAAAA,EAAAA,IAGeQ,EAAA,M,iBAFb,IAA8D,EAA9DR,EAAAA,EAAAA,IAA8Dc,EAAA,CAAnD5K,KAAK,UAAW6K,QAAO9G,I,kBAAc,IAAE+G,EAAA,MAAAA,EAAA,M,QAAF,S,eAChDhB,EAAAA,EAAAA,IAA8Cc,EAAA,CAAlCC,QAAO/G,IAAW,C,iBAAE,IAAEgH,EAAA,MAAAA,EAAA,M,QAAF,S,qDAMxChB,EAAAA,EAAAA,IAqEUC,EAAA,CArEDC,MAAM,cAAY,CACde,QAAMC,EAAAA,EAAAA,IACf,IAKM,EALNf,EAAAA,EAAAA,IAKM,MALNgB,EAKM,C,eAJJhB,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVA,EAAAA,EAAAA,IAEM,YADmD5M,EAAAO,Q,WAAvDsN,EAAAA,EAAAA,IAAuFN,EAAA,C,MAA5E5K,KAAK,UAAW6K,QAAO7G,I,kBAAqC,IAAI8G,EAAA,MAAAA,EAAA,M,QAAJ,W,sDAK7E,IA6CW,E,qBA7CXI,EAAAA,EAAAA,IA6CWC,EAAA,CA7CA9J,KAAMV,GAAA/C,MAAUwN,OAAA,GAAOC,OAAA,GAAO9F,MAAA,gB,kBACvC,IAA2C,EAA3CuE,EAAAA,EAAAA,IAA2CwB,EAAA,CAA1BtL,KAAK,QAAQuL,MAAM,QACpCzB,EAAAA,EAAAA,IAA6DwB,EAAA,CAA5CE,KAAK,QAAQjB,MAAM,OAAO,YAAU,SACrDT,EAAAA,EAAAA,IAAyFwB,EAAA,CAAxEE,KAAK,cAAcjB,MAAM,OAAO,YAAU,MAAM,8BACjET,EAAAA,EAAAA,IAAgEwB,EAAA,CAA/CE,KAAK,WAAWjB,MAAM,WAAWgB,MAAM,SACxDzB,EAAAA,EAAAA,IAA8DwB,EAAA,CAA7CE,KAAK,aAAajB,MAAM,OAAOgB,MAAM,SACtDzB,EAAAA,EAAAA,IAA4DwB,EAAA,CAA3CE,KAAK,cAAcjB,MAAM,KAAKgB,MAAM,OAELrO,EAAAU,Q,WAAhDsN,EAAAA,EAAAA,IAMkBI,EAAA,C,MANDf,MAAM,OAAOgB,MAAM,O,CACvBE,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,EACvB5B,EAAAA,EAAAA,IAES6B,EAAA,CAFA3L,KAAMd,GAAAtB,MAAkB8N,EAAM7I,IAAItD,IAAM,EAAI,UAAY,SAAUqM,OAAO,S,kBAAQ,IACrF,E,QADqF,QACrFC,EAAAA,EAAAA,IAAA,EAAQ3M,GAAAtB,MAAkB8N,EAAM7I,IAAItD,KAAO,QAChD,K,8CAGJuK,EAAAA,EAAAA,IAKkBwB,EAAA,CALDf,MAAM,OAAOgB,MAAM,O,CACvBE,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,E,kBAEpBI,EAAAA,EAAAA,IAAAC,EAAAA,EAAAD,CAAWJ,EAAM7I,IAAImJ,WAAY,qBAAF,K,OAGtClC,EAAAA,EAAAA,IAuBkBwB,EAAA,CAvBDf,MAAM,KAAKgB,MAAM,MAAMU,MAAM,S,CACjCR,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,CAEPrO,EAAAO,Q,WAAhBgM,EAAAA,EAAAA,IAMWsC,EAAAA,GAAA,CAAA5H,IAAA,KAJTwF,EAAAA,EAAAA,IAAiGc,EAAA,CAAtF5K,KAAK,UAAUqG,KAAK,QAASwE,QAAKJ,GAAExF,GAAsByG,EAAM7I,M,kBAAM,IAAIiI,EAAA,MAAAA,EAAA,M,QAAJ,W,gCACjFhB,EAAAA,EAAAA,IAA6Fc,EAAA,CAAlF5K,KAAK,UAAUqG,KAAK,QAASwE,QAAKJ,GAAEnB,GAAkBoC,EAAM7I,M,kBAAM,IAAIiI,EAAA,MAAAA,EAAA,M,QAAJ,W,gCAC7EhB,EAAAA,EAAAA,IAAoFc,EAAA,CAAzE5K,KAAK,UAAUqG,KAAK,QAASwE,QAAKJ,GAAExG,GAAWyH,EAAM7I,M,kBAAM,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,gCACtEhB,EAAAA,EAAAA,IAAqFc,EAAA,CAA1E5K,KAAK,SAASqG,KAAK,QAASwE,QAAKJ,GAAEhG,GAAaiH,EAAM7I,M,kBAAM,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,kDAGvEI,EAAAA,EAAAA,IAQYN,EAAA,C,MAPV5K,KAAK,UACLqG,KAAK,QACJwE,QAAKJ,GAAE7H,GAAe8I,EAAM7I,KAC5BsJ,SAA8C,IAApCjN,GAAAtB,MAAkB8N,EAAM7I,IAAItD,IACtCvD,QAASA,EAAA4B,OAAWpB,EAAAoB,QAAuB8N,EAAM7I,IAAItD,I,kBAEtD,IAA6D,E,iBAAtB,IAApCL,GAAAtB,MAAkB8N,EAAM7I,IAAItD,IAAY,OAAS,QAAnB,K,8EAvC6BvD,EAAA4B,UA+CxEqM,EAAAA,EAAAA,IAWM,MAXNmC,EAWM,EAVJtC,EAAAA,EAAAA,IASEuC,EAAA,CARAC,WAAA,GACAC,OAAO,0CACN,eAAcrQ,EAAA0B,MACd,aAAY,CAAC,GAAI,GAAI,GAAI,KACzB,YAAWzB,EAAAyB,MACXxB,MAAOA,EAAAwB,MACP4O,aAAa9C,GACb+C,gBAAgB9C,I,wDAMvBG,EAAAA,EAAAA,IAuCY4C,GAAA,CAtCTrN,MAAO/C,EAAAsB,M,WACCvB,EAAAuB,M,qCAAAvB,EAAauB,MAAA6M,GACtBc,MAAM,S,CA8BKoB,QAAM3B,EAAAA,EAAAA,IACf,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,EAFJ9C,EAAAA,EAAAA,IAAwDc,EAAA,CAA5CC,QAAKC,EAAA,KAAAA,EAAA,GAAAL,GAAEpO,EAAAuB,OAAgB,I,kBAAO,IAAEkN,EAAA,MAAAA,EAAA,M,QAAF,S,eAC1ChB,EAAAA,EAAAA,IAA4Dc,EAAA,CAAjD5K,KAAK,UAAW6K,QAAOjG,I,kBAAY,IAAEkG,EAAA,MAAAA,EAAA,M,QAAF,S,mCA/BlD,IA2BU,EA3BVhB,EAAAA,EAAAA,IA2BUK,EAAA,CA3BAC,MAAO9K,GAAWuN,MAAOjN,G,QAAe,cAAJ3D,IAAIM,EAAc,cAAY,S,kBAC1E,IAEe,EAFfuN,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,OAAOiB,KAAK,S,kBAC9B,IAAoE,EAApE1B,EAAAA,EAAAA,IAAoEU,EAAA,C,WAAjDlL,GAASD,M,qCAATC,GAASD,MAAKoL,GAAEC,YAAY,W,gCAGjDZ,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,OAAOiB,KAAK,e,kBAC9B,IAAmG,EAAnG1B,EAAAA,EAAAA,IAAmGU,EAAA,CAAzFxK,KAAK,W,WAAoBV,GAASE,Y,qCAATF,GAASE,YAAWiL,GAAEC,YAAY,UAAUoC,KAAK,K,gCAGtFhD,EAAAA,EAAAA,IAiBSiD,GAAA,CAjBAC,OAAQ,IAAE,C,iBACjB,IAKS,EALTlD,EAAAA,EAAAA,IAKSmD,EAAA,CALAC,KAAM,IAAE,C,iBACf,IAGe,EAHfpD,EAAAA,EAAAA,IAGeQ,EAAA,CAHDC,MAAM,OAAOiB,KAAK,Y,kBAE9B,IAAwH,EAAxH1B,EAAAA,EAAAA,IAAwHqD,EAAA,C,WAA9F7N,GAASG,S,qCAATH,GAASG,SAAQgL,GAAGxK,IAAK,EAAIkC,IAAK,IAAKuI,YAAY,KAAKnF,MAAA,gB,wCAGtFuE,EAAAA,EAAAA,IAISmD,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpD,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,OAAOiB,KAAK,c,kBAC9B,IAA0H,EAA1H1B,EAAAA,EAAAA,IAA0HqD,EAAA,C,WAAhG7N,GAASI,W,qCAATJ,GAASI,WAAU+K,GAAGxK,IAAK,EAAIkC,IAAK7C,GAASK,YAAa4F,MAAA,gB,8CAGxFuE,EAAAA,EAAAA,IAISmD,EAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfpD,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,KAAKiB,KAAK,e,kBAC5B,IAA2G,EAA3G1B,EAAAA,EAAAA,IAA2GqD,EAAA,C,WAAjF7N,GAASK,Y,qCAATL,GAASK,YAAW8K,GAAGxK,IAAK,EAAIkC,IAAK,IAAMoD,MAAA,gB,qGAc/EuE,EAAAA,EAAAA,IA6CY4C,GAAA,CA5CVrN,MAAM,O,WACGxB,GAAAD,M,qCAAAC,GAAqBD,MAAA6M,GAC9Bc,MAAM,QACL6B,YAAY,EACZ,wBAAsB,G,kBAEvB,IAqCM,CArCKnP,GAAAL,Q,WAAXgM,EAAAA,EAAAA,IAqCM,MArCNyD,EAqCM,EApCJpD,EAAAA,EAAAA,IAMM,MANNqD,EAMM,EALJrD,EAAAA,EAAAA,IAAgC,WAAA4B,EAAAA,EAAAA,IAAzB5N,GAAAL,MAAYyB,OAAK,IACxB4K,EAAAA,EAAAA,IAGM,MAHNsD,EAGM,EAFJzD,EAAAA,EAAAA,IAAqEc,EAAA,CAA1D5K,KAAK,UAAW6K,QAAOjF,I,kBAAmB,IAAIkF,EAAA,MAAAA,EAAA,M,QAAJ,W,eACrDhB,EAAAA,EAAAA,IAAyEc,EAAA,CAA9D5K,KAAK,UAAW6K,QAAO7E,I,kBAAuB,IAAI8E,EAAA,MAAAA,EAAA,M,QAAJ,W,mBAI7Db,EAAAA,EAAAA,IAyBM,MAzBNuD,EAyBM,E,qBAxBJtC,EAAAA,EAAAA,IAuBWC,EAAA,CAtBR9J,KAAMnD,GAAAN,MACPwN,OAAA,GACAC,OAAA,GACA9F,MAAA,mCAEC,aAAY,IACZ,eAAa,G,kBAEd,IAA2C,EAA3CuE,EAAAA,EAAAA,IAA2CwB,EAAA,CAA1BtL,KAAK,QAAQuL,MAAM,QACpCzB,EAAAA,EAAAA,IAIkBwB,EAAA,CAJDf,MAAM,OAAO,YAAU,O,CAC3BkB,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,EACvBzB,EAAAA,EAAAA,IAA8D,OAAzDwD,UAAQtE,GAAsBuC,EAAM7I,IAAI1C,W,mBAGjD2J,EAAAA,EAAAA,IAA+DwB,EAAA,CAA9CE,KAAK,gBAAgBjB,MAAM,KAAKgB,MAAM,SACvDzB,EAAAA,EAAAA,IAAsDwB,EAAA,CAArCE,KAAK,QAAQjB,MAAM,KAAKgB,MAAM,QAC/CzB,EAAAA,EAAAA,IAKkBwB,EAAA,CALDf,MAAM,KAAKgB,MAAM,MAAMU,MAAM,S,CACjCR,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,EACvB5B,EAAAA,EAAAA,IAA4Fc,EAAA,CAAjF5K,KAAK,UAAUqG,KAAK,QAASwE,QAAKJ,GAAE5E,GAAmB6F,EAAM7I,M,kBAAM,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,gCAC9EhB,EAAAA,EAAAA,IAA6Fc,EAAA,CAAlF5K,KAAK,SAASqG,KAAK,QAASwE,QAAKJ,GAAE3E,GAAqB4F,EAAM7I,M,kBAAM,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,gEAfxE3M,GAAAP,e,yCA0BnBkM,EAAAA,EAAAA,IAiDY4C,GAAA,CAhDVrN,MAAM,S,WACGf,GAAAV,M,uCAAAU,GAAmBV,MAAA6M,GAC5Bc,MAAM,S,CAwCKoB,QAAM3B,EAAAA,EAAAA,IACf,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHNyD,EAGM,EAFJ5D,EAAAA,EAAAA,IAA8Dc,EAAA,CAAlDC,QAAKC,EAAA,KAAAA,EAAA,GAAAL,GAAEnM,GAAAV,OAAsB,I,kBAAO,IAAEkN,EAAA,MAAAA,EAAA,M,QAAF,S,eAChDhB,EAAAA,EAAAA,IAAuFc,EAAA,CAA5E5K,KAAK,UAAW6K,QAAOpE,GAAezK,QAASwC,GAAAZ,O,kBAAe,IAAEkN,EAAA,MAAAA,EAAA,M,QAAF,S,iDAzC7E,IAoCM,EApCNb,EAAAA,EAAAA,IAoCM,MApCN0D,EAoCM,EAnCJ7D,EAAAA,EAAAA,IAME8D,GAAA,CALAvO,MAAM,mBACNW,KAAK,OACJ6N,UAAU,EACX,eACAtI,MAAA,4BAGFuE,EAAAA,EAAAA,IAqBYgE,GAAA,CApBV9D,MAAM,cACN+D,KAAA,GACAC,OAAO,IACN,eAAcxH,GACd,gBAAeP,GACf7E,MAAO,EACP,YAAW7C,GAAAX,MACX,eAAa,EACb,YAAW0I,GACZ2H,OAAO,yG,CAMIC,KAAGlD,EAAAA,EAAAA,IACZ,IAEMF,EAAA,MAAAA,EAAA,MAFNb,EAAAA,EAAAA,IAEM,OAFDD,MAAM,kBAAiB,yBAE5B,M,iBAPF,IAA4D,EAA5DF,EAAAA,EAAAA,IAA4DqE,GAAA,CAAnDnE,MAAM,mBAAiB,C,iBAAC,IAAiB,EAAjBF,EAAAA,EAAAA,KAAiBgC,EAAAA,EAAAA,IAAAsC,EAAAA,iB,qBAClDnE,EAAAA,EAAAA,IAEM,OAFDD,MAAM,mBAAiB,E,QAAC,eAClBC,EAAAA,EAAAA,IAAa,UAAT,U,qCASjBA,EAAAA,EAAAA,IAGM,MAHNoE,EAGM,C,eAFJpE,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXH,EAAAA,EAAAA,IAA2Ec,EAAA,CAAhE5K,KAAK,UAAUmH,KAAA,GAAM0D,QAAO3D,I,kBAAkB,IAAM4D,EAAA,MAAAA,EAAA,M,QAAN,a,4CAa/DhB,EAAAA,EAAAA,IAgEY4C,GAAA,CA/DTrN,MAAOtB,GAAAH,M,WACCE,GAAAF,M,uCAAAE,GAAyBF,MAAA6M,GAClCc,MAAM,S,CAuDKoB,QAAM3B,EAAAA,EAAAA,IACf,IAGM,EAHNf,EAAAA,EAAAA,IAGM,MAHNqE,EAGM,EAFJxE,EAAAA,EAAAA,IAAoEc,EAAA,CAAxDC,QAAKC,EAAA,MAAAA,EAAA,IAAAL,GAAE3M,GAAAF,OAA4B,I,kBAAO,IAAEkN,EAAA,MAAAA,EAAA,M,QAAF,S,eACtDhB,EAAAA,EAAAA,IAAoEc,EAAA,CAAzD5K,KAAK,UAAW6K,QAAO7C,I,kBAAoB,IAAE8C,EAAA,MAAAA,EAAA,M,QAAF,S,mCAxD1D,IAmDU,EAnDVhB,EAAAA,EAAAA,IAmDUK,EAAA,CAnDAC,MAAOlK,GAAe2M,MAAOnM,G,QAAmB,kBAAJzE,IAAI+B,GAAkB,cAAY,S,kBACtF,IAOe,EAPf8L,EAAAA,EAAAA,IAOeQ,EAAA,CAPDC,MAAM,OAAOiB,KAAK,iB,kBAC9B,IAKY,EALZ1B,EAAAA,EAAAA,IAKYyE,GAAA,C,WALQrO,GAAaE,c,uCAAbF,GAAaE,cAAaqK,GAAElF,MAAA,gB,kBAC9C,IAA+C,EAA/CuE,EAAAA,EAAAA,IAA+C0E,GAAA,CAApCjE,MAAM,MAAM3M,MAAM,SAC7BkM,EAAAA,EAAAA,IAA+C0E,GAAA,CAApCjE,MAAM,MAAM3M,MAAM,SAC7BkM,EAAAA,EAAAA,IAA+C0E,GAAA,CAApCjE,MAAM,MAAM3M,MAAM,SAC7BkM,EAAAA,EAAAA,IAA+C0E,GAAA,CAApCjE,MAAM,MAAM3M,MAAM,U,gCAIjCkM,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,KAAKiB,KAAK,S,kBAC5B,IAAwG,EAAxG1B,EAAAA,EAAAA,IAAwGqD,EAAA,C,WAA9EjN,GAAaI,M,uCAAbJ,GAAaI,MAAKmK,GAAGxK,IAAK,EAAIkC,IAAK,IAAKoD,MAAA,gB,gCAGpEuE,EAAAA,EAAAA,IAEeQ,EAAA,CAFDC,MAAM,OAAOiB,KAAK,Y,kBAC9B,IAAoG,EAApG1B,EAAAA,EAAAA,IAAoGU,EAAA,CAA1FxK,KAAK,W,WAAoBE,GAAaC,S,uCAAbD,GAAaC,SAAQsK,GAAEC,YAAY,UAAUoC,KAAK,K,+BAGxC,QAA/B5M,GAAaE,eAA0D,QAA/BF,GAAaE,gB,WAArEwJ,EAAAA,EAAAA,IAiBWsC,EAAAA,GAAA,CAAA5H,IAAA,KAhBT2F,EAAAA,EAAAA,IAGM,MAHNwE,EAGM,C,eAFJxE,EAAAA,EAAAA,IAAW,UAAP,MAAE,KACNH,EAAAA,EAAAA,IAA0Ec,EAAA,CAA/D5K,KAAK,UAAUqG,KAAK,QAASwE,QAAOhD,I,kBAAW,IAAIiD,EAAA,MAAAA,EAAA,M,QAAJ,W,+BAG5DlB,EAAAA,EAAAA,IAUesC,EAAAA,GAAA,MAAAwC,EAAAA,EAAAA,IATaxO,GAAaK,QAAO,CAAtCoO,EAAQrM,M,WADlB4I,EAAAA,EAAAA,IAUeZ,EAAA,CARZhG,IAAKhC,EACLiI,MAAK,MAAU3B,OAAOC,aAAa,GAAKvG,I,kBAEzC,IAIM,EAJN2H,EAAAA,EAAAA,IAIM,MAJN2E,EAIM,EAHJ9E,EAAAA,EAAAA,IAAiEU,EAAA,C,WAA9CmE,EAAOnO,K,yBAAPmO,EAAOnO,KAAIiK,EAAEC,YAAY,W,8CAC5CZ,EAAAA,EAAAA,IAA0D+E,GAAA,C,WAApCF,EAAOlO,U,yBAAPkO,EAAOlO,UAASgK,G,kBAAE,IAAIK,EAAA,MAAAA,EAAA,M,QAAJ,W,yDACxChB,EAAAA,EAAAA,IAAoGc,EAAA,CAAzF5K,KAAK,SAAS8O,KAAK,SAASC,OAAA,GAAO1I,KAAK,QAASwE,QAAKJ,GAAE3C,GAAaxF,I,2DAKlC,QAA/BpC,GAAaE,gB,WAChC8K,EAAAA,EAAAA,IAKeZ,EAAA,C,MALDC,MAAM,OAAOiB,KAAK,kB,kBAC9B,IAGiB,EAHjB1B,EAAAA,EAAAA,IAGiBkF,GAAA,C,WAHQ9O,GAAaG,e,uCAAbH,GAAaG,eAAcoK,I,kBAClD,IAAkC,EAAlCX,EAAAA,EAAAA,IAAkCmF,GAAA,CAAxB1E,MAAM,MAAI,C,iBAAC,IAAEO,EAAA,MAAAA,EAAA,M,QAAF,S,eACrBhB,EAAAA,EAAAA,IAAkCmF,GAAA,CAAxB1E,MAAM,MAAI,C,iBAAC,IAAEO,EAAA,MAAAA,EAAA,M,QAAF,S,6DAMzBI,EAAAA,EAAAA,IAEeZ,EAAA,C,MAFDC,MAAM,OAAOiB,KAAK,kB,kBAC9B,IAA0G,EAA1G1B,EAAAA,EAAAA,IAA0GU,EAAA,CAAhGxK,KAAK,W,WAAoBE,GAAaG,e,uCAAbH,GAAaG,eAAcoK,GAAEC,YAAY,UAAUoC,KAAK,K,sFAcnGhD,EAAAA,EAAAA,IA0DY4C,GAAA,CAzDVrN,MAAM,O,WACGZ,GAAAb,M,uCAAAa,GAAoBb,MAAA6M,GAC7Bc,MAAM,S,kBAEN,IAoDM,CApDKtN,GAAAL,Q,WAAXgM,EAAAA,EAAAA,IAoDM,MApDNsF,EAoDM,EAnDJjF,EAAAA,EAAAA,IAAgC,WAAA4B,EAAAA,EAAAA,IAAzB5N,GAAAL,MAAYyB,OAAK,IAExByK,EAAAA,EAAAA,IAmBUC,EAAA,CAnBDC,MAAM,mBAAiB,C,iBAC9B,IAiBM,EAjBNC,EAAAA,EAAAA,IAiBM,MAjBNkF,EAiBM,EAhBJlF,EAAAA,EAAAA,IAGM,MAHNmF,EAGM,C,eAFJnF,EAAAA,EAAAA,IAAuC,OAAlCD,MAAM,oBAAmB,OAAG,KACjCC,EAAAA,EAAAA,IAA4E,MAA5EoF,GAA4ExD,EAAAA,EAAAA,IAA3CjN,GAAeE,gBAAkB,GAAJ,MAEhEmL,EAAAA,EAAAA,IAGM,MAHNqF,EAGM,C,eAFJrF,EAAAA,EAAAA,IAAwC,OAAnCD,MAAM,oBAAmB,QAAI,KAClCC,EAAAA,EAAAA,IAA6E,MAA7EsF,GAA6E1D,EAAAA,EAAAA,IAA5CjN,GAAeG,iBAAmB,GAAJ,MAEjEkL,EAAAA,EAAAA,IAGM,MAHNuF,EAGM,C,eAFJvF,EAAAA,EAAAA,IAAuC,OAAlCD,MAAM,oBAAmB,OAAG,KACjCC,EAAAA,EAAAA,IAAwE,MAAxEwF,GAAwE5D,EAAAA,EAAAA,IAAvCjN,GAAeI,WAAa,GAAI,IAAC,MAEpEiL,EAAAA,EAAAA,IAGM,MAHNyF,EAGM,C,eAFJzF,EAAAA,EAAAA,IAAuC,OAAlCD,MAAM,oBAAmB,OAAG,KACjCC,EAAAA,EAAAA,IAA2E,MAA3E0F,GAA2E9D,EAAAA,EAAAA,IAA1CjN,GAAeK,eAAiB,GAAJ,S,4BAKnEiM,EAAAA,EAAAA,IA2BWC,EAAA,CA3BA9J,KAAM3C,GAAAd,MAAawN,OAAA,GAAOC,OAAA,GAAO9F,MAAA,oC,kBAC1C,IAA2C,EAA3CuE,EAAAA,EAAAA,IAA2CwB,EAAA,CAA1BtL,KAAK,QAAQuL,MAAM,QACpCzB,EAAAA,EAAAA,IAAoDwB,EAAA,CAAnCE,KAAK,eAAejB,MAAM,UAC3CT,EAAAA,EAAAA,IAMkBwB,EAAA,CANDE,KAAK,QAAQjB,MAAM,KAAKgB,MAAM,MAAMqE,SAAA,I,CACxCnE,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,EACvBzB,EAAAA,EAAAA,IAEO,QAFAD,OAAK6F,EAAAA,EAAAA,IAAEnE,EAAM7I,IAAIvC,OAASrC,GAAAL,MAAY8B,WAAa,aAAe,gB,QACpEgM,EAAM7I,IAAIvC,OAAK,K,OAIxBwJ,EAAAA,EAAAA,IAIkBwB,EAAA,CAJDf,MAAM,OAAOgB,MAAM,O,CACvBE,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,E,kBACpBI,EAAAA,EAAAA,IAAAC,EAAAA,EAAAD,CAAWJ,EAAM7I,IAAIN,UAAW,qBAAF,K,OAGrCuH,EAAAA,EAAAA,IAMkBwB,EAAA,CANDf,MAAM,KAAKgB,MAAM,O,CACrBE,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,EACvB5B,EAAAA,EAAAA,IAES6B,EAAA,CAFA3L,KAAM0L,EAAM7I,IAAIvC,OAASrC,GAAAL,MAAY8B,WAAa,UAAY,U,kBACrE,IAA8D,E,iBAA3DgM,EAAM7I,IAAIvC,OAASrC,GAAAL,MAAY8B,WAAa,KAAO,OAAV,K,6BAIlDoK,EAAAA,EAAAA,IAIkBwB,EAAA,CAJDf,MAAM,KAAKgB,MAAM,MAAMU,MAAM,S,CACjCR,SAAOT,EAAAA,EAAAA,IAAEU,GAAK,EACvB5B,EAAAA,EAAAA,IAA0Fc,EAAA,CAA/E5K,KAAK,UAAUqG,KAAK,QAASwE,QAAKJ,GAAEjB,GAAiBkC,EAAM7I,M,kBAAM,IAAEiI,EAAA,MAAAA,EAAA,M,QAAF,S,gEAxBYnM,GAAAf,a,uDChUtG,MAAMkS,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,oCCNA,KAEE5O,QAAAA,CAAS6O,GACP,OAAOC,EAAAA,EAAIC,IAAI,aAAc,CAAEF,UACjC,EAGA7L,OAAAA,CAAQ3E,GACN,OAAOyQ,EAAAA,EAAIC,IAAI,cAAc1Q,IAC/B,EAGAyF,UAAAA,CAAWb,GACT,OAAO6L,EAAAA,EAAIE,KAAK,aAAc/L,EAChC,EAGAY,UAAAA,CAAWxF,EAAI4E,GACb,OAAO6L,EAAAA,EAAIG,IAAI,cAAc5Q,IAAM4E,EACrC,EAGAO,UAAAA,CAAWnF,GACT,OAAOyQ,EAAAA,EAAII,OAAO,cAAc7Q,IAClC,EAGAkG,gBAAAA,CAAiBC,GACf,OAAOsK,EAAAA,EAAIC,IAAI,cAAcvK,cAC/B,EAGAwD,cAAAA,CAAexD,EAAQ2K,GACrB,OAAOL,EAAAA,EAAIE,KAAK,cAAcxK,cAAoB2K,EACpD,EAGApH,cAAAA,CAAeqH,EAAYD,GACzB,OAAOL,EAAAA,EAAIG,IAAI,wBAAwBG,IAAcD,EACvD,EAGAtK,cAAAA,CAAeuK,GACb,OAAON,EAAAA,EAAII,OAAO,wBAAwBE,IAC5C,EAGAC,eAAAA,CAAgB7K,EAAQ8K,GACtB,OAAOR,EAAAA,EAAIE,KAAK,cAAcxK,qBAA2B8K,EAC3D,EAGAxJ,uBAAAA,CAAwBtB,EAAQgB,GAC9B,OAAOsJ,EAAAA,EAAIE,KAAK,cAAcxK,0BAAgCgB,EAAU,CACtE+J,QAAS,CACP,eAAgB,wBAGtB,EAGAC,UAAAA,CAAWjI,GACT,OAAOuH,EAAAA,EAAIE,KAAK,oBAAqBzH,EACvC,EAGA5G,cAAAA,CAAe6D,EAAQqK,GACrB,OAAOC,EAAAA,EAAIC,IAAI,cAAcvK,YAC/B,EAGAiL,iBAAAA,CAAkBlP,GAChB,OAAOuO,EAAAA,EAAIC,IAAI,sBAAsBxO,YACvC,E,uBCrEK,SAASsK,EAAW6E,EAAMC,EAAS,uBAExC,GADA1T,QAAQC,IAAIwT,IACPA,EAAM,MAAO,GAElB,MAAME,EAA+B,IAAIC,KAAKH,GAE9C,GADAzT,QAAQC,IAAI0T,GACRE,MAAMF,EAAEG,WAAY,MAAO,GAE/B,MAAMC,EAAI,CACR,KAAMJ,EAAEK,WAAa,EACrB,KAAML,EAAEM,UACR,KAAMN,EAAEO,WACR,KAAMP,EAAEQ,aACR,KAAMR,EAAES,aACR,KAAMrP,KAAKsP,OAAOV,EAAEK,WAAa,GAAK,GACtC,EAAKL,EAAEW,mBAIL,OAAOC,KAAKb,KACdA,EAASA,EAAOc,QAAQC,OAAOC,IAAKf,EAAEgB,cAAgB,IAAIzI,UAAU,EAAIuI,OAAOC,GAAGjR,UAIpF,IAAK,IAAImR,KAAKb,EACR,IAAIU,OAAO,IAAMG,EAAI,KAAKL,KAAKb,KACjCA,EAASA,EAAOc,QACdC,OAAOC,GACc,IAArBD,OAAOC,GAAGjR,OAAesQ,EAAEa,IAAM,KAAOb,EAAEa,IAAI1I,WAAW,GAAK6H,EAAEa,IAAInR,UAK1E,OADAzD,QAAQC,IAAIyT,GACLA,CACT,C", "sources": ["webpack://ms/./src/views/exams/ExamList.vue", "webpack://ms/./src/views/exams/ExamList.vue?a85b", "webpack://ms/./src/services/examService.js", "webpack://ms/./src/utils/dateFormat.js"], "sourcesContent": ["<template>\r\n  <div class=\"exam-list-container\">\r\n    <el-card class=\"filter-card\">\r\n      <div class=\"filter-container\">\r\n        <el-form :model=\"filterForm\" inline>\r\n          <el-form-item label=\"考试标题\">\r\n            <el-input v-model=\"filterForm.title\" placeholder=\"请输入考试标题\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button @click=\"resetFilter\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n    \r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>考试列表</span>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddExam\" v-if=\"canManageExams\">新增考试</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table :data=\"examList\" stripe border style=\"width: 100%\" v-loading=\"loading\">\r\n        <el-table-column type=\"index\" width=\"50\" />\r\n        <el-table-column prop=\"title\" label=\"考试标题\" min-width=\"200\" />\r\n        <el-table-column prop=\"description\" label=\"考试描述\" min-width=\"200\" show-overflow-tooltip />\r\n        <el-table-column prop=\"duration\" label=\"考试时长(分钟)\" width=\"120\" />\r\n        <el-table-column prop=\"pass_score\" label=\"及格分数\" width=\"100\" />\r\n        <el-table-column prop=\"total_score\" label=\"总分\" width=\"80\" />\r\n        <!-- Add remaining attempts column for students -->\r\n        <el-table-column label=\"考试机会\" width=\"120\" v-if=\"isStudent\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"remainingAttempts[scope.row.id] > 0 ? 'success' : 'danger'\" effect=\"plain\">\r\n              已用 {{ 2 - (remainingAttempts[scope.row.id]) }}/2 次\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n        \r\n            {{ formatDate(scope.row.created_at, 'YYYY-MM-DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"320\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n        \r\n            <template v-if=\"canManageExams\">\r\n           \r\n              <el-button type=\"primary\" size=\"small\" @click=\"handleManageQuestions(scope.row)\">试题管理</el-button>\r\n              <el-button type=\"success\" size=\"small\" @click=\"handleViewResults(scope.row)\">成绩查看</el-button>\r\n              <el-button type=\"warning\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"small\" \r\n                @click=\"handleTakeExam(scope.row)\" \r\n                :disabled=\"remainingAttempts[scope.row.id] === 0\"\r\n                :loading=\"loading && currentLoadingExam === scope.row.id\"\r\n              >\r\n                {{ remainingAttempts[scope.row.id] === 0 ? '已无机会' : '参加考试' }}\r\n              </el-button>\r\n            \r\n            </template>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/编辑考试对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form :model=\"examForm\" :rules=\"examRules\" ref=\"examFormRef\" label-width=\"100px\">\r\n        <el-form-item label=\"考试标题\" prop=\"title\">\r\n          <el-input v-model=\"examForm.title\" placeholder=\"请输入考试标题\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"考试描述\" prop=\"description\">\r\n          <el-input type=\"textarea\" v-model=\"examForm.description\" placeholder=\"请输入考试描述\" rows=\"3\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"考试时长\" prop=\"duration\">\r\n          \r\n              <el-input-number v-model=\"examForm.duration\" :min=\"1\" :max=\"240\" placeholder=\"分钟\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"及格分数\" prop=\"pass_score\">\r\n              <el-input-number v-model=\"examForm.pass_score\" :min=\"1\" :max=\"examForm.total_score\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"总分\" prop=\"total_score\">\r\n              <el-input-number v-model=\"examForm.total_score\" :min=\"1\" :max=\"1000\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题管理对话框 -->\r\n    <el-dialog\r\n      title=\"试题管理\"\r\n      v-model=\"questionDialogVisible\"\r\n      width=\"850px\"\r\n      :fullscreen=\"false\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div v-if=\"currentExam\" class=\"question-dialog-content\">\r\n        <div class=\"question-header\">\r\n          <h3>{{ currentExam.title }}</h3>\r\n          <div class=\"question-actions\">\r\n            <el-button type=\"primary\" @click=\"handleAddQuestion\">新增试题</el-button>\r\n            <el-button type=\"success\" @click=\"handleImportQuestions\">批量导入</el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"question-table-wrapper\">\r\n          <el-table \r\n            :data=\"questionList\" \r\n            stripe \r\n            border \r\n            style=\"width: 100%; margin-top: 15px;\" \r\n            v-loading=\"questionLoading\"\r\n            :max-height=\"550\"\r\n            :show-header=\"true\"\r\n          >\r\n            <el-table-column type=\"index\" width=\"50\" />\r\n            <el-table-column label=\"题目内容\" min-width=\"300\">\r\n              <template #default=\"scope\">\r\n                <div v-html=\"formatQuestionContent(scope.row.question)\"></div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"question_type\" label=\"题型\" width=\"100\" />\r\n            <el-table-column prop=\"score\" label=\"分值\" width=\"80\" />\r\n            <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"warning\" size=\"small\" @click=\"handleEditQuestion(scope.row)\">编辑</el-button>\r\n                <el-button type=\"danger\" size=\"small\" @click=\"handleDeleteQuestion(scope.row)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        \r\n        <!-- 移除分页，使用滚动条 -->\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题导入对话框 -->\r\n    <el-dialog\r\n      title=\"批量导入试题\"\r\n      v-model=\"importDialogVisible\"\r\n      width=\"500px\"\r\n    >\r\n      <div class=\"import-dialog-content\">\r\n        <el-alert\r\n          title=\"请上传Word格式的试题模板文件\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-bottom: 20px;\"\r\n        />\r\n        \r\n        <el-upload\r\n          class=\"upload-demo\"\r\n          drag\r\n          action=\"#\"\r\n          :http-request=\"handleFileUpload\"\r\n          :before-upload=\"beforeUpload\"\r\n          :limit=\"1\"\r\n          :file-list=\"fileList\"\r\n          :auto-upload=\"false\"\r\n          :on-change=\"handleFileChange\"\r\n          accept=\".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\r\n        >\r\n          <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n          <div class=\"el-upload__text\">\r\n            拖拽文件到此处或 <em>点击上传</em>\r\n          </div>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              仅支持 .doc/.docx 格式文件\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n        \r\n        <div class=\"template-download\">\r\n          <span>没有模板？</span>\r\n          <el-button type=\"primary\" link @click=\"downloadTemplate\">下载试题模板</el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploadLoading\">上传</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"questionDialogTitle\"\r\n      v-model=\"questionEditDialogVisible\"\r\n      width=\"700px\"\r\n    >\r\n      <el-form :model=\"questionForm\" :rules=\"questionRules\" ref=\"questionFormRef\" label-width=\"100px\">\r\n        <el-form-item label=\"题目类型\" prop=\"question_type\">\r\n          <el-select v-model=\"questionForm.question_type\" style=\"width: 100%\">\r\n            <el-option label=\"单选题\" value=\"单选题\"></el-option>\r\n            <el-option label=\"多选题\" value=\"多选题\"></el-option>\r\n            <el-option label=\"判断题\" value=\"判断题\"></el-option>\r\n            <el-option label=\"简答题\" value=\"简答题\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"分值\" prop=\"score\">\r\n          <el-input-number v-model=\"questionForm.score\" :min=\"1\" :max=\"100\" style=\"width: 100%\"></el-input-number>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"题目内容\" prop=\"question\">\r\n          <el-input type=\"textarea\" v-model=\"questionForm.question\" placeholder=\"请输入题目内容\" rows=\"4\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <template v-if=\"questionForm.question_type === '单选题' || questionForm.question_type === '多选题'\">\r\n          <div class=\"options-header\">\r\n            <h4>选项</h4>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addOption\">添加选项</el-button>\r\n          </div>\r\n          \r\n          <el-form-item \r\n            v-for=\"(option, index) in questionForm.options\"\r\n            :key=\"index\"\r\n            :label=\"'选项 ' + String.fromCharCode(65 + index)\"\r\n          >\r\n            <div class=\"option-item\">\r\n              <el-input v-model=\"option.text\" placeholder=\"请输入选项内容\"></el-input>\r\n              <el-checkbox v-model=\"option.isCorrect\">正确答案</el-checkbox>\r\n              <el-button type=\"danger\" icon=\"Delete\" circle size=\"small\" @click=\"removeOption(index)\"></el-button>\r\n            </div>\r\n          </el-form-item>\r\n        </template>\r\n        \r\n        <template v-else-if=\"questionForm.question_type === '判断题'\">\r\n          <el-form-item label=\"正确答案\" prop=\"correct_answer\">\r\n            <el-radio-group v-model=\"questionForm.correct_answer\">\r\n              <el-radio label=\"正确\">正确</el-radio>\r\n              <el-radio label=\"错误\">错误</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </template>\r\n        \r\n        <template v-else>\r\n          <el-form-item label=\"参考答案\" prop=\"correct_answer\">\r\n            <el-input type=\"textarea\" v-model=\"questionForm.correct_answer\" placeholder=\"请输入参考答案\" rows=\"3\"></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      \r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"questionEditDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitQuestionForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 成绩查看对话框 -->\r\n    <el-dialog\r\n      title=\"考试成绩\"\r\n      v-model=\"resultsDialogVisible\"\r\n      width=\"800px\"\r\n    >\r\n      <div v-if=\"currentExam\" class=\"results-dialog-content\">\r\n        <h3>{{ currentExam.title }}</h3>\r\n        \r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-items\">\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">总人数</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.total_students || 0 }}</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">及格人数</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.passed_students || 0 }}</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">及格率</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.pass_rate || 0 }}%</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">平均分</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.average_score || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n        \r\n        <el-table :data=\"resultsList\" stripe border style=\"width: 100%; margin-top: 15px;\" v-loading=\"resultsLoading\">\r\n          <el-table-column type=\"index\" width=\"50\" />\r\n          <el-table-column prop=\"student_name\" label=\"学生姓名\" />\r\n          <el-table-column prop=\"score\" label=\"得分\" width=\"100\" sortable>\r\n            <template #default=\"scope\">\r\n              <span :class=\"scope.row.score >= currentExam.pass_score ? 'pass-score' : 'fail-score'\">\r\n                {{ scope.row.score }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"考试时间\" width=\"160\">\r\n            <template #default=\"scope\">\r\n              {{ formatDate(scope.row.exam_date, 'YYYY-MM-DD HH:mm') }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"scope.row.score >= currentExam.pass_score ? 'success' : 'danger'\">\r\n                {{ scope.row.score >= currentExam.pass_score ? '及格' : '不及格' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"100\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"handleViewDetail(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { UploadFilled } from '@element-plus/icons-vue'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter } from 'vue-router'\r\nimport examService from '@/services/examService'\r\nimport { formatDate, formatRelativeTime } from '@/utils/dateFormat'\r\n\r\nconst store = useStore()\r\nconst router = useRouter()\r\nconst loading = ref(true)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst dialogVisible = ref(false)\r\nconst dialogTitle = ref('新增考试')\r\nconst examFormRef = ref(null)\r\nconst currentLoadingExam = ref(null) // 当前正在加载的考试ID\r\nlet userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : {}\r\n// 用户角色\r\nconst userRole = ref('')\r\nconst isAdmin = ref(userInfo?.role == 'admin')\r\nconst isTeacher = ref(userInfo?.role == 'teacher')\r\nconst isStudent = ref(userInfo?.role == 'student')\r\nconsole.log(isStudent, isTeacher, '---123')\r\nconst canManageExams = ref(userInfo?.role !== 'student')\r\n\r\n// 监听用户角色变化\r\nwatch(() => store.state.user, (newUser) => {\r\n  console.log('用户信息变更:', newUser)\r\n  checkUserRole()\r\n}, { deep: true })\r\n\r\n// 检查用户角色\r\nconst checkUserRole = () => {\r\n  // 使用正确的localStorage键名获取角色\r\n  userRole.value = localStorage.getItem('userRole') || ''\r\n  console.log('当前用户角色:', userRole.value)\r\n  console.log('管理员权限:', isAdmin.value)\r\n  console.log('教师权限:', isTeacher.value)\r\n  console.log('学生权限:', isStudent.value)\r\n  console.log('可管理考试:', canManageExams.value)\r\n}\r\n\r\n// 试题管理相关\r\nconst questionDialogVisible = ref(false)\r\nconst questionEditDialogVisible = ref(false)\r\nconst questionDialogTitle = ref('新增试题')\r\nconst questionFormRef = ref(null)\r\nconst currentExam = ref(null)\r\nconst questionList = ref([])\r\nconst questionLoading = ref(false)\r\nconst questionCurrentPage = ref(1)\r\nconst questionTotal = ref(0)\r\n\r\n// 导入试题相关\r\nconst importDialogVisible = ref(false)\r\nconst fileList = ref([])\r\nconst uploadLoading = ref(false)\r\n\r\n// 成绩查看相关\r\nconst resultsDialogVisible = ref(false)\r\nconst resultsList = ref([])\r\nconst resultsLoading = ref(false)\r\nconst examStatistics = reactive({\r\n  total_students: 0,\r\n  passed_students: 0,\r\n  pass_rate: 0,\r\n  average_score: 0\r\n})\r\n\r\n// Add remaining attempts tracking\r\nconst remainingAttempts = ref({});\r\nconst attemptedExams = ref({}); // 记录已参加过的考试\r\n\r\n// 检查学生是否参加过某个考试\r\nconst hasAttemptedExam = (examId) => {\r\n  if (!examId) return false;\r\n  return attemptedExams.value[examId] === true;\r\n};\r\n\r\n// 过滤条件\r\nconst filterForm = reactive({\r\n  title: ''\r\n})\r\n\r\n// 考试表单\r\nconst examForm = reactive({\r\n  id: null,\r\n  title: '',\r\n  description: '',\r\n  duration: 60,\r\n  pass_score: 60,\r\n  total_score: 100\r\n})\r\n\r\n// 验证规则\r\nconst examRules = {\r\n  title: [\r\n    { required: true, message: '请输入考试标题', trigger: 'blur' }\r\n  ],\r\n  duration: [\r\n    { required: true, message: '请输入考试时长', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '考试时长必须大于0', trigger: 'blur' }\r\n  ],\r\n  pass_score: [\r\n    { required: true, message: '请输入及格分数', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '及格分数必须大于0', trigger: 'blur' }\r\n  ],\r\n  total_score: [\r\n    { required: true, message: '请输入总分', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '总分必须大于0', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 试题表单\r\nconst questionForm = reactive({\r\n  id: null,\r\n  question: '',\r\n  question_type: '单选题',\r\n  correct_answer: '',\r\n  score: 5,\r\n  options: [\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false }\r\n  ]\r\n})\r\n\r\n// 试题验证规则\r\nconst questionRules = {\r\n  question: [\r\n    { required: true, message: '请输入题目内容', trigger: 'blur' }\r\n  ],\r\n  question_type: [\r\n    { required: true, message: '请选择题目类型', trigger: 'change' }\r\n  ],\r\n  score: [\r\n    { required: true, message: '请输入分值', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '分值必须大于0', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 模拟考试数据\r\nconst examList = ref([])\r\n\r\n// 模拟试题数据\r\nconst mockQuestions = [\r\n  {\r\n    id: 1,\r\n    question: '公司的上班时间是几点到几点？',\r\n    options: [\r\n      { text: 'A. 8:30-17:30', isCorrect: true },\r\n      { text: 'B. 9:00-18:00', isCorrect: false },\r\n      { text: 'C. 9:30-18:30', isCorrect: false },\r\n      { text: 'D. 8:00-17:00', isCorrect: false }\r\n    ],\r\n    correct_answer: 'A',\r\n    question_type: '单选题',\r\n    score: 5,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 2,\r\n    question: '公司允许员工在工作时间做以下哪些事情？（多选）',\r\n    options: [\r\n      { text: 'A. 喝水', isCorrect: true },\r\n      { text: 'B. 短暂休息', isCorrect: true },\r\n      { text: 'C. 玩游戏', isCorrect: false },\r\n      { text: 'D. 睡觉', isCorrect: false }\r\n    ],\r\n    correct_answer: 'AB',\r\n    question_type: '多选题',\r\n    score: 10,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 3,\r\n    question: '公司规定必须遵守考勤制度。',\r\n    options: [],\r\n    correct_answer: '正确',\r\n    question_type: '判断题',\r\n    score: 5,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 4,\r\n    question: '请简述公司的请假流程。',\r\n    options: [],\r\n    correct_answer: '1. 提前向部门负责人口头说明\\n2. 在OA系统填写请假申请\\n3. 等待审批通过\\n4. 销假时提交相关证明',\r\n    question_type: '简答题',\r\n    score: 15,\r\n    exam_id: 1\r\n  }\r\n]\r\n\r\n// 模拟成绩数据\r\nconst mockResults = [\r\n  {\r\n    id: 1,\r\n    student_id: 1,\r\n    student_name: '张三',\r\n    exam_id: 1,\r\n    score: 85,\r\n    exam_date: '2023-01-20 10:30:00'\r\n  },\r\n  {\r\n    id: 2,\r\n    student_id: 2,\r\n    student_name: '李四',\r\n    exam_id: 1,\r\n    score: 75,\r\n    exam_date: '2023-01-20 10:45:00'\r\n  },\r\n  {\r\n    id: 3,\r\n    student_id: 3,\r\n    student_name: '王五',\r\n    exam_id: 1,\r\n    score: 55,\r\n    exam_date: '2023-01-20 11:00:00'\r\n  },\r\n  {\r\n    id: 4,\r\n    student_id: 4,\r\n    student_name: '赵六',\r\n    exam_id: 1,\r\n    score: 92,\r\n    exam_date: '2023-01-20 10:15:00'\r\n  }\r\n]\r\n\r\n// 设置总数\r\ntotal.value = examList.length\r\n\r\n// 获取所有考试\r\nconst fetchExams = async () => {\r\n  loading.value = true;\r\n  \r\n  // 重新检查用户角色和权限\r\n  checkUserRole();\r\n  \r\n  // 确保vuex中的状态是最新的\r\n  if (localStorage.getItem('userRole') && store.state.role !== localStorage.getItem('userRole')) {\r\n    store.commit('SET_ROLE', localStorage.getItem('userRole'));\r\n  }\r\n  \r\n  try {\r\n    const response = await examService.getExams({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      title: filterForm.title\r\n    });\r\n    \r\n    examList.value = response.data.data;\r\n    total.value = response.data.total || examList.value.length;\r\n    \r\n    // If student, check attempts for each exam\r\n    if (isStudent.value) {\r\n      await checkRemainingAttempts();\r\n    }\r\n    \r\n    loading.value = false;\r\n  } catch (error) {\r\n    console.error('获取考试列表失败', error);\r\n    ElMessage.error('获取考试列表失败');\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// Check remaining attempts for each exam\r\nconst checkRemainingAttempts = async () => {\r\n \r\n  \r\n  \r\n  try {\r\n    console.log('正在获取所有考试的尝试次数...学生ID:', studentId);\r\n    // 创建一个Promise数组，同时请求所有考试的尝试次数\r\n    const attemptPromises = examList.value.map(async (exam) => {\r\n      try {\r\n        console.log(`正在查询考试 ${exam.id} (${exam.title}) 的尝试记录...`);\r\n        const response = await examService.getExamResults(exam.id, { student_id: studentId });\r\n        console.log(`考试 ${exam.id} 返回数据:`, response.data);\r\n        \r\n        // 确保我们有结果数据\r\n        if (response.data && response.data.data) {\r\n          const results = response.data.data.results || [];\r\n          const attempts = results.length;\r\n          const remaining = Math.max(0, 2 - attempts);\r\n          \r\n          console.log(`考试 ${exam.id} (${exam.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\r\n          remainingAttempts.value[exam.id] = remaining;\r\n          attemptedExams.value[exam.id] = attempts > 0; // 只有实际参加过才标记为true\r\n          \r\n          // 记录每次尝试的详情，便于调试\r\n          if (results.length > 0) {\r\n            results.forEach((result, index) => {\r\n              console.log(`  尝试 ${index + 1}: 得分 ${result.score}，时间 ${result.exam_date}`);\r\n            });\r\n          }\r\n        } else {\r\n          console.warn(`考试 ${exam.id} 未返回有效数据，设置默认剩余次数为2`);\r\n          remainingAttempts.value[exam.id] = 2;\r\n          attemptedExams.value[exam.id] = false; // 没有数据，标记为未参加\r\n        }\r\n      } catch (err) {\r\n        console.error(`获取考试 ${exam.id} 尝试次数失败:`, err);\r\n        remainingAttempts.value[exam.id] = 2;\r\n        attemptedExams.value[exam.id] = false; // 出错，标记为未参加\r\n      }\r\n    });\r\n    \r\n    // 等待所有请求完成\r\n    await Promise.all(attemptPromises);\r\n    console.log('所有考试尝试次数获取完成:', remainingAttempts.value);\r\n    console.log('已参加过的考试:', attemptedExams.value);\r\n  } catch (error) {\r\n    console.error('获取考试尝试次数失败:', error);\r\n    // 设置默认值\r\n    examList.value.forEach(exam => {\r\n      remainingAttempts.value[exam.id] = 2;\r\n      attemptedExams.value[exam.id] = false; // 出错，标记为未参加\r\n    });\r\n  }\r\n};\r\n\r\n// 参加考试\r\nconst handleTakeExam = (row) => {\r\n  // 设置当前正在加载的考试ID\r\n  currentLoadingExam.value = row.id;\r\n  \r\n  // 确保已经加载了尝试次数\r\n  if (!remainingAttempts.value[row.id] && remainingAttempts.value[row.id] !== 0) {\r\n    // 如果还没加载尝试次数信息，先加载\r\n    const studentId = userInfo.student_id;\r\n    if (!studentId) {\r\n      ElMessage.warning('未找到有效的学生ID，请重新登录');\r\n      currentLoadingExam.value = null;\r\n      return;\r\n    }\r\n    \r\n    ElMessage.info('正在获取考试尝试信息，请稍候...');\r\n    \r\n    // 立即获取该考试的尝试次数\r\n    examService.getExamResults(row.id, { student_id: studentId })\r\n      .then(response => {\r\n        console.log(`考试 ${row.id} 尝试信息:`, response.data);\r\n        \r\n        if (response.data && response.data.data) {\r\n          const results = response.data.data.results || [];\r\n          const attempts = results.length;\r\n          const remaining = Math.max(0, 2 - attempts);\r\n          \r\n          console.log(`考试 ${row.id} (${row.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\r\n          remainingAttempts.value[row.id] = remaining;\r\n          attemptedExams.value[row.id] = attempts > 0;\r\n          \r\n          // 获取到尝试次数后，继续参加考试逻辑\r\n          currentLoadingExam.value = null;\r\n          continueToTakeExam(row);\r\n        } else {\r\n          console.warn(`考试 ${row.id} 未返回有效数据，设置默认剩余次数为2`);\r\n          remainingAttempts.value[row.id] = 2;\r\n          attemptedExams.value[row.id] = false;\r\n          currentLoadingExam.value = null;\r\n          continueToTakeExam(row);\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('获取考试尝试次数失败', error);\r\n        ElMessage.error('获取考试尝试次数失败，请刷新页面重试');\r\n        currentLoadingExam.value = null;\r\n      });\r\n    return;\r\n  }\r\n  \r\n  currentLoadingExam.value = null;\r\n  continueToTakeExam(row);\r\n};\r\n\r\n// 继续参加考试流程\r\nconst continueToTakeExam = (row) => {\r\n  // Check if student still has attempts left\r\n  if (remainingAttempts.value[row.id] === 0) {\r\n    ElMessage.warning('您已达到该考试的最大尝试次数（2次）');\r\n    return;\r\n  }\r\n  \r\n  // Confirm before taking the exam\r\n  ElMessageBox.confirm(\r\n    `您总共有2次尝试机会，已使用 ${2 - remainingAttempts.value[row.id]} 次，还剩 ${remainingAttempts.value[row.id]} 次机会。确定要参加考试吗？`,\r\n    '参加考试',\r\n    {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }\r\n  ).then(() => {\r\n    // 记录考试ID和剩余次数到localStorage，以便考试页面使用\r\n    localStorage.setItem('currentExamId', row.id);\r\n    localStorage.setItem('currentExamTitle', row.title);\r\n    localStorage.setItem('currentExamRemainingAttempts', remainingAttempts.value[row.id]);\r\n    \r\n    // 跳转到考试页面\r\n    router.push(`/exams/take/${row.id}`);\r\n  }).catch(() => {\r\n    console.log('用户取消参加考试');\r\n  });\r\n};\r\n\r\n// 查看我的成绩\r\nconst handleViewMyResult = async (row) => {\r\n  try {\r\n    const studentId = localStorage.getItem('studentId');\r\n    if (!studentId) {\r\n      ElMessage.error('未找到有效的学生ID，请重新登录');\r\n      return;\r\n    }\r\n    \r\n    console.log('查询成绩 - 学生ID:', studentId, '考试ID:', row.id);\r\n    \r\n    const response = await examService.getExamResults(row.id, { student_id: studentId });\r\n    console.log('查询成绩 - 返回数据:', response.data);\r\n    \r\n    // 检查是否有考试记录\r\n    if (response.data.data.results && response.data.data.results.length > 0) {\r\n      // Get all attempts sorted by score (highest first)\r\n      const myResults = response.data.data.results\r\n        .filter(r => r.student_id === parseInt(studentId))\r\n        .sort((a, b) => b.score - a.score);\r\n      \r\n      console.log('查询成绩 - 过滤后的结果:', myResults);\r\n      \r\n      if (myResults.length > 0) {\r\n        // Format results to show all attempts\r\n        const attemptsList = myResults.map((result, index) => {\r\n          return `<div class=\"result-item ${result.score >= row.pass_score ? 'pass' : 'fail'}\">\r\n            <h4>尝试 ${index + 1}</h4>\r\n            <p><strong>得分:</strong> ${result.score} / ${row.total_score}</p>\r\n            <p><strong>考试时间:</strong> ${formatDate(result.exam_date, 'YYYY-MM-DD HH:mm:ss')}</p>\r\n            <p><strong>状态:</strong> ${result.score >= row.pass_score ? '通过' : '未通过'}</p>\r\n          </div>`;\r\n        }).join('<hr>');\r\n        \r\n        ElMessageBox.alert(\r\n          `<div class=\"results-container\">\r\n            <h3>您的考试成绩</h3>\r\n            <div class=\"results-list\">${attemptsList}</div>\r\n            <div class=\"attempts-info\">\r\n              <p>总尝试次数: ${myResults.length}/2</p>\r\n              <p>剩余次数: ${Math.max(0, 2 - myResults.length)}</p>\r\n              <p class=\"view-all-results\"><a href=\"#/exams/my-results\">查看我的所有考试成绩 &raquo;</a></p>\r\n            </div>\r\n          </div>`,\r\n          '考试成绩',\r\n          {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定',\r\n            customClass: 'result-dialog'\r\n          }\r\n        );\r\n        return;\r\n      }\r\n    }\r\n    \r\n    ElMessage.info('您暂无该考试的成绩记录');\r\n  } catch (error) {\r\n    console.error('获取成绩失败', error);\r\n    ElMessage.error('获取成绩失败: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nonMounted(() => {\r\n  // 确保先检查用户角色并强制刷新store状态\r\n  reloadUserRoleState();\r\n  fetchExams();\r\n})\r\n\r\n// 强制重新加载用户角色状态\r\nconst reloadUserRoleState = () => {\r\n  // 从localStorage获取最新角色\r\n  const currentUserRole = localStorage.getItem('userRole');\r\n  \r\n  // 确保store中的角色是最新的\r\n  if (currentUserRole) {\r\n    console.log('从localStorage加载角色:', currentUserRole);\r\n    store.commit('SET_ROLE', currentUserRole);\r\n    \r\n    // 如果有用户ID，确保用户信息也是最新的\r\n    const userId = localStorage.getItem('userId');\r\n    if (userId) {\r\n      // 可以选择是否强制刷新用户信息\r\n      store.dispatch('fetchUserProfile').catch(err => {\r\n        console.error('获取用户信息失败:', err);\r\n      });\r\n    }\r\n  } else {\r\n    console.warn('未找到用户角色信息');\r\n  }\r\n  \r\n  // 执行常规角色检查\r\n  checkUserRole();\r\n}\r\n\r\n// 重置过滤条件\r\nconst resetFilter = () => {\r\n  filterForm.title = ''\r\n  handleSearch()\r\n}\r\n\r\n// 搜索\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchExams()\r\n}\r\n\r\n// 新增考试\r\nconst handleAddExam = () => {\r\n  dialogTitle.value = '新增考试'\r\n  dialogVisible.value = true\r\n  // 重置表单\r\n  examForm.id = null\r\n  examForm.title = ''\r\n  examForm.description = ''\r\n  examForm.duration = 60\r\n  examForm.pass_score = 60\r\n  examForm.total_score = 100\r\n}\r\n\r\n// 编辑考试\r\nconst handleEdit = (row) => {\r\n  dialogTitle.value = '编辑考试'\r\n  dialogVisible.value = true\r\n  \r\n  // 查询完整的考试信息\r\n  examService.getExam(row.id)\r\n    .then(response => {\r\n      // 填充表单数据\r\n      const examData = response.data.data;\r\n      Object.keys(examForm).forEach(key => {\r\n        // 确保数值型字段为数字类型\r\n        if (['duration', 'pass_score', 'total_score'].includes(key)) {\r\n          examForm[key] = Number(examData[key] || 0);\r\n        } else {\r\n          examForm[key] = examData[key];\r\n        }\r\n      });\r\n    })\r\n    .catch(error => {\r\n      console.error('获取考试详情失败', error);\r\n      ElMessage.error('获取考试详情失败');\r\n      \r\n      // 如果获取详情失败，仍然使用表格行数据填充\r\n      Object.keys(examForm).forEach(key => {\r\n        examForm[key] = row[key];\r\n      });\r\n    });\r\n}\r\n\r\n// 删除考试\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除考试 ${row.title} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await examService.deleteExam(row.id)\r\n      ElMessage.success(`考试 ${row.title} 已删除`)\r\n      fetchExams() // 重新加载列表\r\n    } catch (error) {\r\n      console.error('删除考试失败', error)\r\n      ElMessage.error('删除考试失败，请重试')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!examFormRef.value) return\r\n  \r\n  await examFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (examForm.id) {\r\n          // 编辑模式\r\n          await examService.updateExam(examForm.id, examForm)\r\n          ElMessage.success(`考试 ${examForm.title} 信息已更新`)\r\n        } else {\r\n          // 新增模式\r\n          await examService.createExam(examForm)\r\n          ElMessage.success(`考试 ${examForm.title} 添加成功`)\r\n        }\r\n        dialogVisible.value = false\r\n        fetchExams() // 重新加载列表\r\n      } catch (error) {\r\n        console.error('保存考试失败', error)\r\n        ElMessage.error('保存考试失败，请重试')\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 试题管理\r\nconst handleManageQuestions = (row) => {\r\n  currentExam.value = row\r\n  questionDialogVisible.value = true\r\n  // 设置为第一页（即使我们不再使用分页）\r\n  questionCurrentPage.value = 1\r\n  \r\n  // 加载试题数据\r\n  loadQuestions(row.id)\r\n  \r\n  // 确保对话框完全打开后调整表格高度\r\n  setTimeout(() => {\r\n    const questionTable = document.querySelector('.question-table-wrapper .el-table')\r\n    if (questionTable) {\r\n      questionTable.style.height = '550px'\r\n    }\r\n  }, 100)\r\n}\r\n\r\n// 加载试题数据\r\nconst loadQuestions = async (examId) => {\r\n  questionLoading.value = true\r\n  \r\n  try {\r\n    // 不使用分页参数，一次性获取所有试题\r\n    const response = await examService.getExamQuestions(examId)\r\n    \r\n    // 检查返回数据结构\r\n    console.log('加载试题数据返回:', response.data)\r\n    \r\n    // 确保获取完整的数据列表\r\n    if (response.data && response.data.data) {\r\n      questionList.value = response.data.data\r\n      // 依然保存总数，用于展示\r\n      questionTotal.value = response.data.count || questionList.value.length\r\n      console.log(`加载了 ${questionList.value.length} 道试题`)\r\n    } else {\r\n      questionList.value = []\r\n      questionTotal.value = 0\r\n      console.warn('未找到试题数据')\r\n    }\r\n    \r\n    questionLoading.value = false\r\n  } catch (error) {\r\n    console.error('获取试题失败', error)\r\n    ElMessage.error('获取试题失败')\r\n    questionLoading.value = false\r\n  }\r\n}\r\n\r\n// 新增试题\r\nconst handleAddQuestion = () => {\r\n  questionDialogTitle.value = '新增试题'\r\n  questionEditDialogVisible.value = true\r\n  // 重置表单\r\n  questionForm.id = null\r\n  questionForm.question = ''\r\n  questionForm.question_type = '单选题'\r\n  questionForm.correct_answer = ''\r\n  questionForm.score = 5\r\n  questionForm.options = [\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false }\r\n  ]\r\n}\r\n\r\n// 编辑试题\r\nconst handleEditQuestion = (row) => {\r\n  questionDialogTitle.value = '编辑试题'\r\n  questionEditDialogVisible.value = true\r\n  \r\n  // 填充表单数据\r\n  questionForm.id = row.id\r\n  questionForm.question = row.question\r\n  questionForm.question_type = row.question_type\r\n  questionForm.correct_answer = row.correct_answer\r\n  questionForm.score = row.score\r\n  \r\n  // 根据题型处理选项\r\n  if (row.question_type === '单选题' || row.question_type === '多选题') {\r\n    questionForm.options = row.options ? [...row.options] : []\r\n  } else if (row.question_type === '判断题') {\r\n    questionForm.correct_answer = row.correct_answer\r\n  }\r\n}\r\n\r\n// 删除试题\r\nconst handleDeleteQuestion = (row) => {\r\n  ElMessageBox.confirm(`确定要删除该试题吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await examService.deleteQuestion(row.id)\r\n      ElMessage.success('试题已删除')\r\n      // 重新加载试题列表\r\n      loadQuestions(currentExam.value.id)\r\n    } catch (error) {\r\n      console.error('删除试题失败', error)\r\n      ElMessage.error('删除试题失败，请重试')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量导入试题\r\nconst handleImportQuestions = () => {\r\n  importDialogVisible.value = true\r\n  fileList.value = []\r\n}\r\n\r\n// 文件上传前检查\r\nconst beforeUpload = (file) => {\r\n  const isWordDoc = \r\n    file.type === 'application/msword' || \r\n    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\r\n  \r\n  if (!isWordDoc) {\r\n    ElMessage.error('请上传Word格式的文件!')\r\n    return false\r\n  }\r\n  \r\n  const isLt10M = file.size / 1024 / 1024 < 10\r\n  \r\n  if (!isLt10M) {\r\n    ElMessage.error('文件大小不能超过10MB!')\r\n    return false\r\n  }\r\n  \r\n  return true\r\n}\r\n\r\n// 处理文件选择\r\nconst handleFileChange = (file, uploadFileList) => {\r\n  // 记录最新选择的文件\r\n  console.log('文件选择变化:', file, uploadFileList)\r\n  fileList.value = uploadFileList\r\n}\r\n\r\n// 处理文件上传\r\nconst handleFileUpload = (options) => {\r\n  const { file } = options\r\n  // 这个函数只在auto-upload=true时调用\r\n  // 我们设置为false，所以这里只需返回false\r\n  return false // 阻止默认上传行为，使用我们自己的提交方式\r\n}\r\n\r\n// 提交上传\r\nconst submitUpload = async () => {\r\n  console.log('当前文件列表:', fileList.value)\r\n  if (!fileList.value || fileList.value.length === 0) {\r\n    ElMessage.warning('请选择要上传的文件')\r\n    return\r\n  }\r\n  \r\n  uploadLoading.value = true\r\n  \r\n  try {\r\n    // 创建FormData对象\r\n    const formData = new FormData()\r\n    // 获取原始文件对象\r\n    const fileObject = fileList.value[0]\r\n    const rawFile = fileObject.raw || fileObject\r\n    console.log('上传文件:', rawFile)\r\n    \r\n    // 使用field name 'file' 匹配后端控制器\r\n    formData.append('template', rawFile)\r\n    \r\n    // 调用实际API\r\n    const response = await examService.importQuestionsFromWord(currentExam.value.id, formData)\r\n    \r\n    // 处理成功响应\r\n    ElMessage.success(`试题导入成功，共导入${response.data.count || 0}道题目`)\r\n    importDialogVisible.value = false\r\n    \r\n    // 重新加载试题列表\r\n    loadQuestions(currentExam.value.id)\r\n  } catch (error) {\r\n    console.error('导入试题失败', error)\r\n    let errorMsg = '导入试题失败'\r\n    \r\n    // 获取详细错误信息\r\n    if (error.response && error.response.data && error.response.data.message) {\r\n      errorMsg += `：${error.response.data.message}`\r\n    }\r\n    \r\n    ElMessage.error(errorMsg)\r\n  } finally {\r\n    uploadLoading.value = false\r\n  }\r\n}\r\n\r\n// 下载模板\r\nconst downloadTemplate = async () => {\r\n  try {\r\n    ElMessage.success('模板下载中...')\r\n    \r\n    // 创建下载链接\r\n    const link = document.createElement('a')\r\n    // 设置下载链接为后端API地址\r\n    link.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/exams/template/download`\r\n    \r\n    // 添加token到URL，以便通过授权\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      link.href += `?token=${token}`\r\n    }\r\n    \r\n    link.download = '试题导入模板.docx'\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    document.body.removeChild(link)\r\n  } catch (error) {\r\n    console.error('下载模板失败', error)\r\n    ElMessage.error('下载模板失败，请重试')\r\n  }\r\n}\r\n\r\n// 添加选项\r\nconst addOption = () => {\r\n  questionForm.options.push({ text: '', isCorrect: false })\r\n}\r\n\r\n// 移除选项\r\nconst removeOption = (index) => {\r\n  if (questionForm.options.length <= 2) {\r\n    ElMessage.warning('至少需要2个选项')\r\n    return\r\n  }\r\n  questionForm.options.splice(index, 1)\r\n}\r\n\r\n// 提交试题表单\r\nconst submitQuestionForm = async () => {\r\n  if (!questionFormRef.value) return\r\n  \r\n  await questionFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      // 选择题验证选项\r\n      if ((questionForm.question_type === '单选题' || questionForm.question_type === '多选题')) {\r\n        // 验证选项内容\r\n        const emptyOption = questionForm.options.find(opt => !opt.text.trim())\r\n        if (emptyOption) {\r\n          ElMessage.error('选项内容不能为空')\r\n          return\r\n        }\r\n        \r\n        // 验证是否选择了正确答案\r\n        const hasCorrect = questionForm.options.some(opt => opt.isCorrect)\r\n        if (!hasCorrect) {\r\n          ElMessage.error('请至少选择一个正确答案')\r\n          return\r\n        }\r\n        \r\n        // 单选题只能有一个正确答案\r\n        if (questionForm.question_type === '单选题') {\r\n          const correctCount = questionForm.options.filter(opt => opt.isCorrect).length\r\n          if (correctCount > 1) {\r\n            ElMessage.error('单选题只能有一个正确答案')\r\n            return\r\n          }\r\n        }\r\n      }\r\n      \r\n      try {\r\n        // 准备提交的数据\r\n        const submitData = { ...questionForm }\r\n        \r\n        // 处理正确答案\r\n        if (questionForm.question_type === '单选题') {\r\n          const correctOption = questionForm.options.find(opt => opt.isCorrect)\r\n          if (correctOption) {\r\n            // 获取正确选项的索引，转换为A、B、C...\r\n            const index = questionForm.options.findIndex(opt => opt.isCorrect)\r\n            submitData.correct_answer = String.fromCharCode(65 + index) // A, B, C...\r\n          }\r\n        } else if (questionForm.question_type === '多选题') {\r\n          // 将所有正确选项索引转换为字符串，如\"ABC\"\r\n          const correctAnswers = questionForm.options\r\n            .map((opt, index) => opt.isCorrect ? String.fromCharCode(65 + index) : null)\r\n            .filter(Boolean)\r\n            .join('')\r\n          submitData.correct_answer = correctAnswers\r\n        }\r\n        \r\n        // 提交表单\r\n        if (questionForm.id) {\r\n          // 编辑模式\r\n          await examService.updateQuestion(questionForm.id, submitData)\r\n          ElMessage.success('试题更新成功')\r\n        } else {\r\n          // 新增模式\r\n          await examService.createQuestion(currentExam.value.id, submitData)\r\n          ElMessage.success('试题添加成功')\r\n        }\r\n        \r\n        questionEditDialogVisible.value = false\r\n        // 重新加载试题列表\r\n        loadQuestions(currentExam.value.id)\r\n      } catch (error) {\r\n        console.error('保存试题失败', error)\r\n        ElMessage.error('保存试题失败，请重试')\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 格式化试题内容\r\nconst formatQuestionContent = (content) => {\r\n  if (!content) return ''\r\n  // 最多显示100个字符\r\n  return content.length > 100 ? content.substring(0, 100) + '...' : content\r\n}\r\n\r\n// 试题不再使用分页\r\n\r\n// 查看考试成绩\r\nconst handleViewResults = async (row) => {\r\n  currentExam.value = row\r\n  resultsDialogVisible.value = true\r\n  resultsLoading.value = true\r\n  \r\n  try {\r\n    const response = await examService.getExamResults(row.id)\r\n    const data = response.data.data\r\n    \r\n    resultsList.value = data.results || []\r\n    \r\n    // 设置统计数据\r\n    examStatistics.total_students = data.summary.total_students || 0\r\n    examStatistics.passed_students = data.summary.passed_students || 0\r\n    examStatistics.pass_rate = data.summary.pass_rate || 0\r\n    examStatistics.average_score = data.summary.average_score || 0\r\n    \r\n    resultsLoading.value = false\r\n  } catch (error) {\r\n    console.error('获取成绩列表失败', error)\r\n    ElMessage.error('获取成绩列表失败')\r\n    resultsLoading.value = false\r\n  }\r\n}\r\n\r\n// 查看考试详情\r\nconst handleViewDetail = (row) => {\r\n  ElMessage.success(`查看 ${row.student_name} 的考试详情`)\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  fetchExams()\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchExams()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 试题管理样式 */\r\n.question-dialog-content {\r\n  min-height: 300px;\r\n}\r\n\r\n.question-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 添加表格滚动容器样式 */\r\n.question-table-wrapper {\r\n  max-height: 600px; /* 增加高度以显示更多内容 */\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.question-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.options-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.options-header h4 {\r\n  margin: 0;\r\n}\r\n\r\n.option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* 试题导入样式 */\r\n.import-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n.upload-demo {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.template-download {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  gap: 5px;\r\n}\r\n\r\n/* 成绩查看样式 */\r\n.results-dialog-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 15px;\r\n  font-size: 18px;\r\n}\r\n\r\n.statistics-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.statistics-items {\r\n  display: flex;\r\n  justify-content: space-around;\r\n}\r\n\r\n.statistics-item {\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.statistics-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.pass-score {\r\n  color: #67C23A;\r\n  font-weight: bold;\r\n}\r\n\r\n.fail-score {\r\n  color: #F56C6C;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n}\r\n\r\n:deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n}\r\n\r\n/* Exam results styles */\r\n:deep(.result-dialog) {\r\n  min-width: 400px;\r\n}\r\n\r\n:deep(.results-container) {\r\n  padding: 10px;\r\n}\r\n\r\n:deep(.result-item) {\r\n  padding: 10px;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.result-item.pass) {\r\n  background-color: rgba(103, 194, 58, 0.1);\r\n  border-left: 3px solid #67C23A;\r\n}\r\n\r\n:deep(.result-item.fail) {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border-left: 3px solid #F56C6C;\r\n}\r\n\r\n:deep(.results-list) {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n:deep(.attempts-info) {\r\n  font-weight: bold;\r\n  border-top: 1px solid #eee;\r\n  padding-top: 10px;\r\n}\r\n\r\n:deep(.view-all-results) {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n}\r\n\r\n:deep(.view-all-results a) {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.view-all-results a:hover) {\r\n  text-decoration: underline;\r\n}\r\n</style> ", "import script from \"./ExamList.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./ExamList.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./ExamList.vue?vue&type=style&index=0&id=69cdaa63&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-69cdaa63\"]])\n\nexport default __exports__", "import api from '../utils/api'\r\n\r\nexport default {\r\n  // 获取考试列表\r\n  getExams(params) {\r\n    return api.get('/api/exams', { params })\r\n  },\r\n  \r\n  // 获取单个考试详情\r\n  getExam(id) {\r\n    return api.get(`/api/exams/${id}`)\r\n  },\r\n  \r\n  // 创建考试\r\n  createExam(examData) {\r\n    return api.post('/api/exams', examData)\r\n  },\r\n  \r\n  // 更新考试\r\n  updateExam(id, examData) {\r\n    return api.put(`/api/exams/${id}`, examData)\r\n  },\r\n  \r\n  // 删除考试\r\n  deleteExam(id) {\r\n    return api.delete(`/api/exams/${id}`)\r\n  },\r\n  \r\n  // 获取考试题目\r\n  getExamQuestions(examId) {\r\n    return api.get(`/api/exams/${examId}/questions`)\r\n  },\r\n  \r\n  // 创建考试题目\r\n  createQuestion(examId, questionData) {\r\n    return api.post(`/api/exams/${examId}/questions`, questionData)\r\n  },\r\n  \r\n  // 更新考试题目\r\n  updateQuestion(questionId, questionData) {\r\n    return api.put(`/api/exams/questions/${questionId}`, questionData)\r\n  },\r\n  \r\n  // 删除考试题目\r\n  deleteQuestion(questionId) {\r\n    return api.delete(`/api/exams/questions/${questionId}`)\r\n  },\r\n  \r\n  // 批量导入考试题目\r\n  importQuestions(examId, questions) {\r\n    return api.post(`/api/exams/${examId}/questions/import`, questions)\r\n  },\r\n  \r\n  // 从Word文档导入考试题目\r\n  importQuestionsFromWord(examId, formData) {\r\n    return api.post(`/api/exams/${examId}/questions/import-word`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    })\r\n  },\r\n  \r\n  // 提交考试答案\r\n  submitExam(submitData) {\r\n    return api.post('/api/exams/submit', submitData)\r\n  },\r\n  \r\n  // 获取考试成绩汇总\r\n  getExamResults(examId, params) {\r\n    return api.get(`/api/exams/${examId}/results`)\r\n  },\r\n  \r\n  // 获取学生的所有考试成绩\r\n  getStudentResults(studentId) {\r\n    return api.get(`/api/exams/student/${studentId}/results`)\r\n  }\r\n} ", "/**\r\n * 格式化日期时间\r\n * @param {string|Date} date - 要格式化的日期\r\n * @param {string} format - 格式化模式，默认 'YYYY-MM-DD HH:mm:ss'\r\n * @returns {string} 格式化后的日期字符串\r\n */\r\nexport function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {\r\n  console.log(date)\r\n  if (!date) return '';\r\n  \r\n  const d = typeof date === 'string' ? new Date(date) : new Date(date);\r\n  console.log(d)\r\n  if (isNaN(d.getTime())) return '';\r\n  \r\n  const o = {\r\n    'M+': d.getMonth() + 1, // 月份\r\n    'D+': d.getDate(), // 日\r\n    'H+': d.getHours(), // 小时\r\n    'm+': d.getMinutes(), // 分\r\n    's+': d.getSeconds(), // 秒\r\n    'q+': Math.floor((d.getMonth() + 3) / 3), // 季度\r\n    'S': d.getMilliseconds() // 毫秒\r\n  };\r\n  \r\n  // 替换年份\r\n  if (/(Y+)/.test(format)) {\r\n    format = format.replace(RegExp.$1, (d.getFullYear() + '').substring(4 - RegExp.$1.length));\r\n  }\r\n  \r\n  // 替换其他时间单位\r\n  for (let k in o) {\r\n    if (new RegExp('(' + k + ')').test(format)) {\r\n      format = format.replace(\r\n        RegExp.$1,\r\n        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substring(('' + o[k]).length)\r\n      );\r\n    }\r\n  }\r\n  console.log(format)\r\n  return format;\r\n}\r\n\r\n/**\r\n * 相对时间格式化（如：3分钟前，2小时前，昨天，等）\r\n * @param {string|Date} date - 要格式化的日期\r\n * @returns {string} 格式化后的相对时间字符串\r\n */\r\nexport function formatRelativeTime(date) {\r\n  if (!date) return '';\r\n  \r\n  const d = typeof date === 'string' ? new Date(date.replace(/-/g, '/')) : new Date(date);\r\n  \r\n  if (isNaN(d.getTime())) return '';\r\n  \r\n  const now = new Date();\r\n  const diff = now.getTime() - d.getTime(); // 时间差（毫秒）\r\n  \r\n  // 转换为秒\r\n  const seconds = Math.floor(diff / 1000);\r\n  \r\n  if (seconds < 60) {\r\n    return '刚刚';\r\n  } else if (seconds < 3600) { // 小于1小时\r\n    return Math.floor(seconds / 60) + '分钟前';\r\n  } else if (seconds < 86400) { // 小于1天\r\n    return Math.floor(seconds / 3600) + '小时前';\r\n  } else if (seconds < 172800) { // 小于2天\r\n    return '昨天';\r\n  } else if (seconds < 2592000) { // 小于30天\r\n    return Math.floor(seconds / 86400) + '天前';\r\n  } else if (seconds < 31536000) { // 小于1年\r\n    return Math.floor(seconds / 2592000) + '个月前';\r\n  } else {\r\n    return Math.floor(seconds / 31536000) + '年前';\r\n  }\r\n} "], "names": ["store", "useStore", "router", "useRouter", "loading", "ref", "currentPage", "pageSize", "total", "dialogVisible", "dialogTitle", "examFormRef", "currentLoadingExam", "userInfo", "localStorage", "getItem", "JSON", "parse", "userRole", "isAdmin", "role", "<PERSON><PERSON><PERSON>er", "isStudent", "console", "log", "canManageExams", "watch", "state", "user", "newUser", "checkUserRole", "deep", "value", "questionDialogVisible", "questionEditDialogVisible", "questionDialogTitle", "questionFormRef", "currentExam", "questionList", "questionLoading", "questionCurrentPage", "questionTotal", "importDialogVisible", "fileList", "uploadLoading", "resultsDialogVisible", "resultsList", "resultsLoading", "examStatistics", "reactive", "total_students", "passed_students", "pass_rate", "average_score", "remainingAttempts", "attemptedExams", "filterForm", "title", "examForm", "id", "description", "duration", "pass_score", "total_score", "examRules", "required", "message", "trigger", "type", "min", "questionForm", "question", "question_type", "correct_answer", "score", "options", "text", "isCorrect", "questionRules", "examList", "length", "fetchExams", "async", "commit", "response", "examService", "getExams", "page", "limit", "data", "checkRemainingAttempts", "error", "ElMessage", "studentId", "attemptPromises", "map", "exam", "getExamResults", "student_id", "results", "attempts", "remaining", "Math", "max", "for<PERSON>ach", "result", "index", "exam_date", "warn", "err", "Promise", "all", "handleTakeExam", "row", "info", "then", "continueToTakeExam", "catch", "warning", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "setItem", "push", "onMounted", "reloadUserRoleState", "currentUserRole", "userId", "dispatch", "resetFilter", "handleSearch", "handleAddExam", "handleEdit", "getExam", "examData", "Object", "keys", "key", "includes", "Number", "handleDelete", "deleteExam", "success", "submitForm", "validate", "valid", "updateExam", "createExam", "handleManageQuestions", "loadQuestions", "setTimeout", "questionTable", "document", "querySelector", "style", "height", "getExamQuestions", "examId", "count", "handleAddQuestion", "handleEditQuestion", "handleDeleteQuestion", "deleteQuestion", "handleImportQuestions", "beforeUpload", "file", "isWordDoc", "isLt10M", "size", "handleFileChange", "uploadFileList", "handleFileUpload", "submitUpload", "formData", "FormData", "fileObject", "rawFile", "raw", "append", "importQuestionsFromWord", "errorMsg", "downloadTemplate", "link", "createElement", "href", "import", "token", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "addOption", "removeOption", "splice", "submitQuestionForm", "emptyOption", "find", "opt", "trim", "hasCorrect", "some", "correctCount", "filter", "submitData", "correctOption", "findIndex", "String", "fromCharCode", "correctAnswers", "Boolean", "join", "updateQuestion", "createQuestion", "formatQuestionContent", "content", "substring", "handleViewResults", "summary", "handleViewDetail", "student_name", "handleSizeChange", "handleCurrentChange", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "class", "_createElementVNode", "_hoisted_2", "_component_el_form", "model", "inline", "_component_el_form_item", "label", "_component_el_input", "$event", "placeholder", "clearable", "_component_el_button", "onClick", "_cache", "header", "_withCtx", "_hoisted_3", "_createBlock", "_component_el_table", "stripe", "border", "_component_el_table_column", "width", "prop", "default", "scope", "_component_el_tag", "effect", "_toDisplayString", "_unref", "formatDate", "created_at", "fixed", "_Fragment", "disabled", "_hoisted_4", "_component_el_pagination", "background", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "footer", "_hoisted_5", "rules", "rows", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_input_number", "fullscreen", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "innerHTML", "_hoisted_13", "_hoisted_11", "_component_el_alert", "closable", "_component_el_upload", "drag", "action", "accept", "tip", "_component_el_icon", "UploadFilled", "_hoisted_12", "_hoisted_16", "_component_el_select", "_component_el_option", "_hoisted_14", "_renderList", "option", "_hoisted_15", "_component_el_checkbox", "icon", "circle", "_component_el_radio_group", "_component_el_radio", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "sortable", "_normalizeClass", "__exports__", "params", "api", "get", "post", "put", "delete", "questionData", "questionId", "importQuestions", "questions", "headers", "submitExam", "getStudentResults", "date", "format", "d", "Date", "isNaN", "getTime", "o", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "floor", "getMilliseconds", "test", "replace", "RegExp", "$1", "getFullYear", "k"], "sourceRoot": ""}
{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"user-list-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_4 = {\n  class: \"student-option\"\n};\nconst _hoisted_5 = {\n  class: \"student-school\"\n};\nconst _hoisted_6 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_7 = {\n  class: \"import-container\"\n};\nconst _hoisted_8 = {\n  class: \"template-download\",\n  style: {\n    \"margin-bottom\": \"20px\"\n  }\n};\nconst _hoisted_9 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 搜索和操作区域 \"), _createVNode(_component_el_card, {\n    class: \"search-card\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      inline: true,\n      model: $setup.searchForm,\n      class: \"search-form\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.username,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchForm.username = $event),\n          placeholder: \"请输入用户名\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"手机号\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.phone,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchForm.phone = $event),\n          placeholder: \"请输入手机号\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSearch\n        }, {\n          default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"查询\")])),\n          _: 1 /* STABLE */,\n          __: [16]\n        }), _createVNode(_component_el_button, {\n          onClick: $setup.resetForm\n        }, {\n          default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [17]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 表格区域 \"), _createVNode(_component_el_card, {\n    class: \"table-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"用户列表\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: $setup.handleImport\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Upload\"])]),\n        _: 1 /* STABLE */\n      }), _cache[18] || (_cache[18] = _createTextVNode(\" 批量导入 \"))]),\n      _: 1 /* STABLE */,\n      __: [18]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleAdd\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Plus\"])]),\n        _: 1 /* STABLE */\n      }), _cache[19] || (_cache[19] = _createTextVNode(\" 新增用户 \"))]),\n      _: 1 /* STABLE */,\n      __: [19]\n    }), _createVNode(_component_el_button, {\n      type: \"danger\",\n      disabled: $setup.multipleSelection.length === 0,\n      onClick: $setup.handleBatchDelete\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Delete\"])]),\n        _: 1 /* STABLE */\n      }), _cache[20] || (_cache[20] = _createTextVNode(\" 批量删除 \"))]),\n      _: 1 /* STABLE */,\n      __: [20]\n    }, 8 /* PROPS */, [\"disabled\"]), _createVNode(_component_el_button, {\n      onClick: $setup.refreshTable\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Refresh\"])]),\n        _: 1 /* STABLE */\n      }), _cache[21] || (_cache[21] = _createTextVNode(\" 刷新 \"))]),\n      _: 1 /* STABLE */,\n      __: [21]\n    })])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.userList,\n      onSelectionChange: $setup.handleSelectionChange,\n      style: {\n        \"width\": \"100%\"\n      },\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        width: \"55\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"id\",\n        label: \"ID\",\n        width: \"80\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"username\",\n        label: \"用户名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"name\",\n        label: \"姓名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"phone\",\n        label: \"手机号\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"email\",\n        label: \"邮箱\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"role\",\n        label: \"角色\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [scope.row.role === 'admin' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: \"danger\"\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"管理员\")])),\n          _: 1 /* STABLE */,\n          __: [23]\n        })) : scope.row.role === 'teacher' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"带教老师\")])),\n          _: 1 /* STABLE */,\n          __: [24]\n        })) : scope.row.role === 'student' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 2,\n          type: \"success\"\n        }, {\n          default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"学生\")])),\n          _: 1 /* STABLE */,\n          __: [25]\n        })) : (_openBlock(), _createBlock(_component_el_tag, {\n          key: 3,\n          type: \"info\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.role), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"student_id\",\n        label: \"学生ID\",\n        width: \"120\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"created_at\",\n        label: \"创建时间\",\n        width: \"160\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        fixed: \"right\",\n        label: \"操作\",\n        width: \"180\",\n        align: \"center\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $event => $setup.handleEdit(scope.row)\n        }, {\n          default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [26]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"danger\",\n          onClick: $event => $setup.handleDelete(scope.row)\n        }, {\n          default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [27]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [10, 20, 50, 100],\n      total: $setup.total,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 用户表单对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.dialogVisible = $event),\n    title: $setup.dialogType === 'add' ? '新增用户' : '编辑用户',\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n      onClick: _cache[12] || (_cache[12] = $event => $setup.dialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [29]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitForm\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [30]\n    })])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.userForm,\n      rules: $setup.userFormRules,\n      ref: \"userFormRef\",\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.username,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.userForm.username = $event),\n          placeholder: \"请输入用户名\",\n          disabled: $setup.dialogType === 'edit'\n        }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"姓名\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.name,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.userForm.name = $event),\n          placeholder: \"请输入姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"密码\",\n        prop: \"password\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.password,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.userForm.password = $event),\n          type: \"password\",\n          placeholder: \"请输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.phone,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.userForm.phone = $event),\n          placeholder: \"请输入手机号\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.email,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.userForm.email = $event),\n          placeholder: \"请输入邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"角色\",\n        prop: \"role\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.userForm.role,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.userForm.role = $event),\n          placeholder: \"请选择角色\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"管理员\",\n            value: \"admin\"\n          }), _createVNode(_component_el_option, {\n            label: \"带教老师\",\n            value: \"teacher\"\n          }), _createVNode(_component_el_option, {\n            label: \"学生\",\n            value: \"student\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 当角色是学生时，显示实习生选择器 \"), $setup.userForm.role === 'student' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"实习生\",\n        prop: \"student_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.userForm.student_id,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.userForm.student_id = $event),\n          placeholder: \"请选择关联的实习生\",\n          filterable: \"\",\n          loading: $setup.studentListLoading\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.studentList, item => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: `${item.name} - ${item.school}`,\n              value: item.id\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_5, _toDisplayString(item.school), 1 /* TEXT */)])]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"loading\"]), _cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n          class: \"form-tip\"\n        }, \"将用户账号关联到现有实习生\", -1 /* CACHED */))]),\n        _: 1 /* STABLE */,\n        __: [28]\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 当角色不是学生时，隐藏学号字段 \"), $setup.userForm.role !== 'student' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 2,\n        label: \"学号\",\n        prop: \"student_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.student_id,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.userForm.student_id = $event),\n          placeholder: \"请输入学号（选填）\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"]), _createCommentVNode(\" 批量导入对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.importDialogVisible,\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $setup.importDialogVisible = $event),\n    title: \"批量导入用户\",\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_9, [_createVNode(_component_el_button, {\n      onClick: _cache[14] || (_cache[14] = $event => $setup.importDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[35] || (_cache[35] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [35]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitImport,\n      loading: $setup.importLoading\n    }, {\n      default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 确认导入 \")])),\n      _: 1 /* STABLE */,\n      __: [36]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_alert, {\n      title: \"导入说明\",\n      type: \"info\",\n      closable: false,\n      style: {\n        \"margin-bottom\": \"20px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[31] || (_cache[31] = [_createElementVNode(\"p\", null, \"1. 请下载Excel模板，按照模板格式填写数据\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"2. 支持的文件格式：.xls, .xlsx\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"3. 文件大小不超过10MB\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"4. 必填字段：用户名、密码、姓名\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"5. 角色字段：管理员、教师、学生（默认为学生）\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"6. 注意：用户导入时不会自动关联实习生，需要后续手动关联\", -1 /* CACHED */)])),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.downloadTemplate\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Download\"])]),\n        _: 1 /* STABLE */\n      }), _cache[32] || (_cache[32] = _createTextVNode(\" 下载Excel模板 \"))]),\n      _: 1 /* STABLE */,\n      __: [32]\n    })]), _createVNode(_component_el_upload, {\n      ref: \"uploadRef\",\n      class: \"upload-demo\",\n      drag: \"\",\n      \"auto-upload\": false,\n      limit: 1,\n      \"on-change\": $setup.handleFileChange,\n      \"before-upload\": $setup.beforeUpload,\n      accept: \".xls,.xlsx\"\n    }, {\n      tip: _withCtx(() => _cache[33] || (_cache[33] = [_createElementVNode(\"div\", {\n        class: \"el-upload__tip\"\n      }, \" 只能上传 .xls/.xlsx 文件，且不超过10MB \", -1 /* CACHED */)])),\n      default: _withCtx(() => [_createVNode(_component_el_icon, {\n        class: \"el-icon--upload\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"UploadFilled\"])]),\n        _: 1 /* STABLE */\n      }), _cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n        class: \"el-upload__text\"\n      }, [_createTextVNode(\" 将Excel文件拖到此处，或\"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [34]\n    }, 512 /* NEED_PATCH */)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_el_card", "_component_el_form", "inline", "model", "$setup", "searchForm", "_component_el_form_item", "label", "_component_el_input", "username", "$event", "placeholder", "clearable", "phone", "_component_el_button", "type", "onClick", "handleSearch", "_cache", "resetForm", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "handleImport", "_component_el_icon", "handleAdd", "disabled", "multipleSelection", "length", "handleBatchDelete", "refreshTable", "_createBlock", "_component_el_table", "data", "userList", "onSelectionChange", "handleSelectionChange", "border", "_component_el_table_column", "width", "align", "prop", "default", "scope", "row", "role", "_component_el_tag", "formatDate", "created_at", "fixed", "size", "handleEdit", "handleDelete", "loading", "_hoisted_3", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "dialogVisible", "title", "dialogType", "footer", "_hoisted_6", "submitForm", "userForm", "rules", "userFormRules", "ref", "name", "password", "email", "_component_el_select", "_component_el_option", "value", "student_id", "filterable", "studentListLoading", "_Fragment", "_renderList", "studentList", "item", "key", "id", "school", "_hoisted_4", "_toDisplayString", "_hoisted_5", "importDialogVisible", "_hoisted_9", "submitImport", "importLoading", "_hoisted_7", "_component_el_alert", "closable", "_hoisted_8", "downloadTemplate", "_component_el_upload", "drag", "limit", "handleFileChange", "beforeUpload", "accept", "tip"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\views\\users\\UserList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-list-container\">\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card class=\"search-card\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"用户名\">\r\n          <el-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\">\r\n          <el-input v-model=\"searchForm.phone\" placeholder=\"请输入手机号\" clearable></el-input>\r\n        </el-form-item>\r\n    \r\n      \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 表格区域 -->\r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>用户列表</span>\r\n          <div>\r\n            <el-button type=\"success\" @click=\"handleImport\">\r\n              <el-icon><Upload /></el-icon> 批量导入\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"handleAdd\">\r\n              <el-icon><Plus /></el-icon> 新增用户\r\n            </el-button>\r\n            <el-button type=\"danger\" :disabled=\"multipleSelection.length === 0\" @click=\"handleBatchDelete\">\r\n              <el-icon><Delete /></el-icon> 批量删除\r\n            </el-button>\r\n            <el-button @click=\"refreshTable\">\r\n              <el-icon><Refresh /></el-icon> 刷新\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n        border\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"username\" label=\"用户名\" />\r\n        <el-table-column prop=\"name\" label=\"姓名\" />\r\n        <el-table-column prop=\"phone\" label=\"手机号\" />\r\n        <el-table-column prop=\"email\" label=\"邮箱\" />\r\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag v-if=\"scope.row.role === 'admin'\" type=\"danger\">管理员</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'teacher'\" type=\"warning\">带教老师</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'student'\" type=\"success\">学生</el-tag>\r\n            <el-tag v-else type=\"info\">{{ scope.row.role }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"student_id\" label=\"学生ID\" width=\"120\" />\r\n       \r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"danger\" \r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :total=\"total\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 用户表单对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"dialogType === 'add' ? '新增用户' : '编辑用户'\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        :model=\"userForm\"\r\n        :rules=\"userFormRules\"\r\n        ref=\"userFormRef\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"userForm.username\" placeholder=\"请输入用户名\" :disabled=\"dialogType === 'edit'\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"userForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"dialogType === 'add'\" label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色\" prop=\"role\">\r\n          <el-select v-model=\"userForm.role\" placeholder=\"请选择角色\">\r\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n            <el-option label=\"带教老师\" value=\"teacher\"></el-option>\r\n            <el-option label=\"学生\" value=\"student\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色是学生时，显示实习生选择器 -->\r\n        <el-form-item v-if=\"userForm.role === 'student'\" label=\"实习生\" prop=\"student_id\">\r\n          <el-select \r\n            v-model=\"userForm.student_id\" \r\n            placeholder=\"请选择关联的实习生\" \r\n            filterable\r\n            :loading=\"studentListLoading\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in studentList\"\r\n              :key=\"item.id\"\r\n              :label=\"`${item.name} - ${item.school}`\"\r\n              :value=\"item.id\"\r\n            >\r\n              <div class=\"student-option\">\r\n                <span>{{ item.name }}</span>\r\n                <span class=\"student-school\">{{ item.school }}</span>\r\n              </div>\r\n            </el-option>\r\n          </el-select>\r\n          <div class=\"form-tip\">将用户账号关联到现有实习生</div>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色不是学生时，隐藏学号字段 -->\r\n        <el-form-item v-if=\"userForm.role !== 'student'\" label=\"学号\" prop=\"student_id\">\r\n          <el-input v-model=\"userForm.student_id\" placeholder=\"请输入学号（选填）\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"批量导入用户\"\r\n      width=\"600px\"\r\n    >\r\n      <div class=\"import-container\">\r\n        <el-alert\r\n          title=\"导入说明\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px\"\r\n        >\r\n          <template #default>\r\n            <p>1. 请下载Excel模板，按照模板格式填写数据</p>\r\n            <p>2. 支持的文件格式：.xls, .xlsx</p>\r\n            <p>3. 文件大小不超过10MB</p>\r\n            <p>4. 必填字段：用户名、密码、姓名</p>\r\n            <p>5. 角色字段：管理员、教师、学生（默认为学生）</p>\r\n            <p>6. 注意：用户导入时不会自动关联实习生，需要后续手动关联</p>\r\n          </template>\r\n        </el-alert>\r\n\r\n        <div class=\"template-download\" style=\"margin-bottom: 20px\">\r\n          <el-button type=\"primary\" @click=\"downloadTemplate\">\r\n            <el-icon><Download /></el-icon> 下载Excel模板\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-upload\r\n          ref=\"uploadRef\"\r\n          class=\"upload-demo\"\r\n          drag\r\n          :auto-upload=\"false\"\r\n          :limit=\"1\"\r\n          :on-change=\"handleFileChange\"\r\n          :before-upload=\"beforeUpload\"\r\n          accept=\".xls,.xlsx\"\r\n        >\r\n          <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n          <div class=\"el-upload__text\">\r\n            将Excel文件拖到此处，或<em>点击上传</em>\r\n          </div>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              只能上传 .xls/.xlsx 文件，且不超过10MB\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitImport\" :loading=\"importLoading\">\r\n            确认导入\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Plus, Delete, Refresh, Upload, Download, UploadFilled } from '@element-plus/icons-vue'\r\nimport userService from '@/services/userService'\r\nimport studentService from '@/services/studentService'\r\n\r\nconst loading = ref(false)\r\nconst studentListLoading = ref(false)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst multipleSelection = ref([])\r\nconst dialogVisible = ref(false)\r\nconst dialogType = ref('add') // 'add' or 'edit'\r\nconst userFormRef = ref(null)\r\nconst studentList = ref([]) // 学生列表\r\n\r\n// 导入相关变量\r\nconst importDialogVisible = ref(false)\r\nconst importLoading = ref(false)\r\nconst uploadRef = ref(null)\r\nconst fileList = ref([])\r\n\r\n// 搜索表单\r\nconst searchForm = reactive({\r\n  username: '',\r\n  phone: '',\r\n  status: ''\r\n})\r\n\r\n// 用户表单\r\nconst userForm = reactive({\r\n  id: '',\r\n  username: '',\r\n  name: '',\r\n  password: '',\r\n  phone: '',\r\n  email: '',\r\n  role: 'student',\r\n  student_id: '',\r\n  status: 1\r\n})\r\n\r\n// 获取学生列表\r\nconst fetchStudentList = async () => {\r\n  studentListLoading.value = true\r\n  try {\r\n    const response = await studentService.getStudents()\r\n    studentList.value = response.data.data\r\n  } catch (error) {\r\n    console.error('获取学生列表失败:', error)\r\n    ElMessage.error('获取学生列表失败')\r\n  } finally {\r\n    studentListLoading.value = false\r\n  }\r\n}\r\n\r\n// 当角色选择为学生时，加载学生列表\r\nwatch(() => userForm.role, (newRole) => {\r\n  if (newRole === 'student' && studentList.value.length === 0) {\r\n    fetchStudentList()\r\n  }\r\n})\r\n\r\n// 当对话框打开时，如果角色是学生且学生列表为空，则获取学生列表\r\nwatch(() => dialogVisible.value, (newVal) => {\r\n  if (newVal && userForm.role === 'student' && studentList.value.length === 0) {\r\n    fetchStudentList()\r\n  }\r\n})\r\n\r\n// 表单校验规则\r\nconst userFormRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { required: true, message: '请输入手机号', trigger: 'blur' },\r\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  role: [\r\n    { required: true, message: '请选择角色', trigger: 'change' }\r\n  ]\r\n}\r\n\r\n// 用户数据\r\nconst userList = ref([])\r\n\r\nonMounted(() => {\r\n  fetchData()\r\n})\r\n\r\n// 格式化日期\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return ''\r\n  const date = new Date(dateString)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hours}:${minutes}`\r\n}\r\n\r\n// 获取数据\r\nconst fetchData = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await userService.getUsers({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      username: searchForm.username || undefined,\r\n      phone: searchForm.phone || undefined,\r\n      status: searchForm.status || undefined\r\n    })\r\n    userList.value = response.data.data\r\n    total.value = response.data.count || 0\r\n  } catch (error) {\r\n    console.error('获取用户列表失败:', error)\r\n    ElMessage.error('获取用户列表失败')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 查询\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchData()\r\n}\r\n\r\n// 重置表单\r\nconst resetForm = () => {\r\n  Object.keys(searchForm).forEach(key => {\r\n    searchForm[key] = ''\r\n  })\r\n  handleSearch()\r\n}\r\n\r\n// 刷新表格\r\nconst refreshTable = () => {\r\n  fetchData()\r\n}\r\n\r\n// 多选变化\r\nconst handleSelectionChange = (selection) => {\r\n  multipleSelection.value = selection\r\n}\r\n\r\n// 新增用户\r\nconst handleAdd = () => {\r\n  dialogType.value = 'add'\r\n  resetUserForm()\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 编辑用户\r\nconst handleEdit = (row) => {\r\n  dialogType.value = 'edit'\r\n  resetUserForm()\r\n  Object.keys(userForm).forEach(key => {\r\n    if (key !== 'password') {\r\n      userForm[key] = row[key]\r\n    }\r\n  })\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 重置用户表单\r\nconst resetUserForm = () => {\r\n  if (userFormRef.value) {\r\n    userFormRef.value.resetFields()\r\n  }\r\n  Object.assign(userForm, {\r\n    id: '',\r\n    username: '',\r\n    name: '',\r\n    password: '',\r\n    phone: '',\r\n    email: '',\r\n    role: 'student',\r\n    student_id: '',\r\n    status: 1\r\n  })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!userFormRef.value) return\r\n  \r\n  await userFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (dialogType.value === 'add') {\r\n          // 新增用户\r\n          await userService.createUser(userForm)\r\n          ElMessage.success('新增用户成功')\r\n        } else {\r\n          // 编辑用户\r\n          await userService.updateUser(userForm.id, userForm)\r\n          ElMessage.success('编辑用户成功')\r\n        }\r\n        dialogVisible.value = false\r\n        fetchData()\r\n      } catch (error) {\r\n        console.error('保存用户失败:', error)\r\n        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 删除用户\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.deleteUser(row.id)\r\n      ElMessage.success(`用户 ${row.username} 已删除`)\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error)\r\n      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量删除\r\nconst handleBatchDelete = () => {\r\n  if (multipleSelection.value.length === 0) {\r\n    ElMessage.warning('请至少选择一条记录')\r\n    return\r\n  }\r\n  \r\n  const names = multipleSelection.value.map(item => item.username).join('、')\r\n  const ids = multipleSelection.value.map(item => item.id)\r\n  \r\n  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.batchDeleteUsers(ids)\r\n      ElMessage.success('批量删除成功')\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('批量删除失败:', error)\r\n      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 修改状态\r\nconst handleStatusChange = async (val, row) => {\r\n  try {\r\n    await userService.updateUserStatus(row.id, val)\r\n    const status = val === 1 ? '启用' : '禁用'\r\n    ElMessage.success(`已${status}用户 ${row.username}`)\r\n  } catch (error) {\r\n    console.error('修改状态失败:', error)\r\n    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message))\r\n    // 回滚状态\r\n    row.status = val === 1 ? 0 : 1\r\n  }\r\n}\r\n\r\n// 分页大小变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  fetchData()\r\n}\r\n\r\n// 页码变化\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchData()\r\n}\r\n\r\n// 导入相关方法\r\nconst handleImport = () => {\r\n  importDialogVisible.value = true\r\n  fileList.value = []\r\n}\r\n\r\nconst downloadTemplate = () => {\r\n  // 动态导入XLSX库\r\n  import('xlsx').then(XLSX => {\r\n    // 创建Excel模板数据\r\n    const templateData = [\r\n      {\r\n        '用户名': 'zhangsan',\r\n        '密码': '123456',\r\n        '姓名': '张三',\r\n        '角色': '学生',\r\n        '邮箱': '<EMAIL>',\r\n        '电话': '13800138000'\r\n      }\r\n    ]\r\n\r\n    // 创建工作簿\r\n    const ws = XLSX.utils.json_to_sheet(templateData)\r\n    const wb = XLSX.utils.book_new()\r\n    XLSX.utils.book_append_sheet(wb, ws, '用户信息')\r\n\r\n    // 下载文件\r\n    XLSX.writeFile(wb, '用户导入模板.xlsx')\r\n  }).catch(error => {\r\n    console.error('XLSX库加载失败:', error)\r\n    ElMessage.error('下载模板失败，请刷新页面重试')\r\n  })\r\n}\r\n\r\nconst beforeUpload = (file) => {\r\n  const isExcel = file.type === 'application/vnd.ms-excel' ||\r\n                 file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n\r\n  if (!isExcel) {\r\n    ElMessage.error('请上传Excel格式的文件!')\r\n    return false\r\n  }\r\n\r\n  const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n  if (!isLt10M) {\r\n    ElMessage.error('文件大小不能超过10MB!')\r\n    return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\nconst handleFileChange = (file, uploadFileList) => {\r\n  fileList.value = uploadFileList\r\n}\r\n\r\nconst submitImport = async () => {\r\n  if (fileList.value.length === 0) {\r\n    ElMessage.error('请选择要上传的文件')\r\n    return\r\n  }\r\n\r\n  importLoading.value = true\r\n\r\n  try {\r\n    const formData = new FormData()\r\n    const fileObject = fileList.value[0]\r\n    const rawFile = fileObject.raw || fileObject\r\n\r\n    formData.append('file', rawFile)\r\n\r\n    const response = await userService.importUsers(formData)\r\n\r\n    ElMessage.success(`导入成功！共导入 ${response.data.data.imported} 条记录`)\r\n    importDialogVisible.value = false\r\n\r\n    // 清理文件列表\r\n    fileList.value = []\r\n    if (uploadRef.value) {\r\n      uploadRef.value.clearFiles()\r\n    }\r\n\r\n    // 重新加载用户列表\r\n    fetchData()\r\n\r\n  } catch (error) {\r\n    console.error('导入失败:', error)\r\n    let errorMsg = '导入失败'\r\n\r\n    if (error.response && error.response.data) {\r\n      if (error.response.data.errors && error.response.data.errors.length > 0) {\r\n        errorMsg += '：\\n' + error.response.data.errors.join('\\n')\r\n      } else if (error.response.data.message) {\r\n        errorMsg += '：' + error.response.data.message\r\n      }\r\n    }\r\n\r\n    ElMessage.error(errorMsg)\r\n  } finally {\r\n    importLoading.value = false\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.user-list-container {\r\n  padding: 10px;\r\n}\r\n\r\n.search-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.student-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.student-school {\r\n  color: #909399;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAsBrBA,KAAK,EAAC;AAAa;;EA4DrBA,KAAK,EAAC;AAAsB;;EA8DpBA,KAAK,EAAC;AAAgB;;EAEnBA,KAAK,EAAC;AAAgB;;EAa/BA,KAAK,EAAC;AAAe;;EAavBA,KAAK,EAAC;AAAkB;;EAiBtBA,KAAK,EAAC,mBAAmB;EAACC,KAA2B,EAA3B;IAAA;EAAA;;;EA6BzBD,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;uBA1NjCE,mBAAA,CAkOM,OAlONC,UAkOM,GAjOJC,mBAAA,aAAgB,EAChBC,YAAA,CAeUC,kBAAA;IAfDN,KAAK,EAAC;EAAa;sBAC1B,MAaU,CAbVK,YAAA,CAaUE,kBAAA;MAbAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAEX,KAAK,EAAC;;wBAChD,MAEe,CAFfK,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC;MAAK;0BACvB,MAAkF,CAAlFR,YAAA,CAAkFS,mBAAA;sBAA/DJ,MAAA,CAAAC,UAAU,CAACI,QAAQ;qEAAnBL,MAAA,CAAAC,UAAU,CAACI,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACC,SAAS,EAAT;;;UAE/Db,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC;MAAK;0BACvB,MAA+E,CAA/ER,YAAA,CAA+ES,mBAAA;sBAA5DJ,MAAA,CAAAC,UAAU,CAACQ,KAAK;qEAAhBT,MAAA,CAAAC,UAAU,CAACQ,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACC,SAAS,EAAT;;;UAI5Db,YAAA,CAGeO,uBAAA;0BAFb,MAA8D,CAA9DP,YAAA,CAA8De,oBAAA;UAAnDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEZ,MAAA,CAAAa;;4BAAc,MAAEC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;YAClDnB,YAAA,CAA4Ce,oBAAA;UAAhCE,OAAK,EAAEZ,MAAA,CAAAe;QAAS;4BAAE,MAAED,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;;;MAKtCpB,mBAAA,UAAa,EACbC,YAAA,CAyEUC,kBAAA;IAzEDN,KAAK,EAAC;EAAY;IACd0B,MAAM,EAAAC,QAAA,CACf,MAgBM,CAhBNC,mBAAA,CAgBM,OAhBNC,UAgBM,G,4BAfJD,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAaM,cAZJvB,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAoB;;wBAChC,MAA6B,CAA7BzB,YAAA,CAA6B0B,kBAAA;0BAApB,MAAU,CAAV1B,YAAA,CAAUK,MAAA,Y;;uDAAU,QAC/B,G;;;QACAL,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAsB;;wBAChC,MAA2B,CAA3B3B,YAAA,CAA2B0B,kBAAA;0BAAlB,MAAQ,CAAR1B,YAAA,CAAQK,MAAA,U;;uDAAU,QAC7B,G;;;QACAL,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,QAAQ;MAAEY,QAAQ,EAAEvB,MAAA,CAAAwB,iBAAiB,CAACC,MAAM;MAASb,OAAK,EAAEZ,MAAA,CAAA0B;;wBAC1E,MAA6B,CAA7B/B,YAAA,CAA6B0B,kBAAA;0BAApB,MAAU,CAAV1B,YAAA,CAAUK,MAAA,Y;;uDAAU,QAC/B,G;;;qCACAL,YAAA,CAEYe,oBAAA;MAFAE,OAAK,EAAEZ,MAAA,CAAA2B;IAAY;wBAC7B,MAA8B,CAA9BhC,YAAA,CAA8B0B,kBAAA;0BAArB,MAAW,CAAX1B,YAAA,CAAWK,MAAA,a;;uDAAU,MAChC,G;;;;sBAKN,MAsCW,C,+BAtCX4B,YAAA,CAsCWC,mBAAA;MApCRC,IAAI,EAAE9B,MAAA,CAAA+B,QAAQ;MACdC,iBAAgB,EAAEhC,MAAA,CAAAiC,qBAAqB;MACxC1C,KAAmB,EAAnB;QAAA;MAAA,CAAmB;MACnB2C,MAAM,EAAN;;wBAEA,MAA8D,CAA9DvC,YAAA,CAA8DwC,0BAAA;QAA7CxB,IAAI,EAAC,WAAW;QAACyB,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACnD1C,YAAA,CAAkEwC,0BAAA;QAAjDG,IAAI,EAAC,IAAI;QAACnC,KAAK,EAAC,IAAI;QAACiC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACvD1C,YAAA,CAA+CwC,0BAAA;QAA9BG,IAAI,EAAC,UAAU;QAACnC,KAAK,EAAC;UACvCR,YAAA,CAA0CwC,0BAAA;QAAzBG,IAAI,EAAC,MAAM;QAACnC,KAAK,EAAC;UACnCR,YAAA,CAA4CwC,0BAAA;QAA3BG,IAAI,EAAC,OAAO;QAACnC,KAAK,EAAC;UACpCR,YAAA,CAA2CwC,0BAAA;QAA1BG,IAAI,EAAC,OAAO;QAACnC,KAAK,EAAC;UACpCR,YAAA,CAOkBwC,0BAAA;QAPDG,IAAI,EAAC,MAAM;QAACnC,KAAK,EAAC,IAAI;QAACiC,KAAK,EAAC;;QACjCG,OAAO,EAAAtB,QAAA,CACoDuB,KAD7C,KACTA,KAAK,CAACC,GAAG,CAACC,IAAI,gB,cAA5Bd,YAAA,CAAoEe,iBAAA;;UAA1BhC,IAAI,EAAC;;4BAAS,MAAGG,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;cACxC0B,KAAK,CAACC,GAAG,CAACC,IAAI,kB,cAAjCd,YAAA,CAA6Ee,iBAAA;;UAA5BhC,IAAI,EAAC;;4BAAU,MAAIG,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;cACjD0B,KAAK,CAACC,GAAG,CAACC,IAAI,kB,cAAjCd,YAAA,CAA2Ee,iBAAA;;UAA1BhC,IAAI,EAAC;;4BAAU,MAAEG,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;6BAClEc,YAAA,CAAwDe,iBAAA;;UAAzChC,IAAI,EAAC;;4BAAO,MAAoB,C,kCAAjB6B,KAAK,CAACC,GAAG,CAACC,IAAI,iB;;;;UAGhD/C,YAAA,CAA8DwC,0BAAA;QAA7CG,IAAI,EAAC,YAAY;QAACnC,KAAK,EAAC,MAAM;QAACiC,KAAK,EAAC;UAEtDzC,YAAA,CAIkBwC,0BAAA;QAJDG,IAAI,EAAC,YAAY;QAACnC,KAAK,EAAC,MAAM;QAACiC,KAAK,EAAC;;QACzCG,OAAO,EAAAtB,QAAA,CACsBuB,KADf,K,kCACpBxC,MAAA,CAAA4C,UAAU,CAACJ,KAAK,CAACC,GAAG,CAACI,UAAU,kB;;UAGtClD,YAAA,CASkBwC,0BAAA;QATDW,KAAK,EAAC,OAAO;QAAC3C,KAAK,EAAC,IAAI;QAACiC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QAC/CE,OAAO,EAAAtB,QAAA,CACqDuB,KAD9C,KACvB7C,YAAA,CAAqEe,oBAAA;UAA1DqC,IAAI,EAAC,OAAO;UAAEnC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAgD,UAAU,CAACR,KAAK,CAACC,GAAG;;4BAAG,MAAE3B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DACzDnB,YAAA,CAIee,oBAAA;UAHbqC,IAAI,EAAC,OAAO;UACZpC,IAAI,EAAC,QAAQ;UACZC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAiD,YAAY,CAACT,KAAK,CAACC,GAAG;;4BAC/B,MAAE3B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDAlCId,MAAA,CAAAkD,OAAO,E,GAwCpBhC,mBAAA,CAUM,OAVNiC,UAUM,GATJxD,YAAA,CAQEyD,wBAAA;MAPQ,cAAY,EAAEpD,MAAA,CAAAqD,WAAW;kEAAXrD,MAAA,CAAAqD,WAAW,GAAA/C,MAAA;MACzB,WAAS,EAAEN,MAAA,CAAAsD,QAAQ;+DAARtD,MAAA,CAAAsD,QAAQ,GAAAhD,MAAA;MAC1B,YAAU,EAAE,iBAAiB;MAC7BiD,KAAK,EAAEvD,MAAA,CAAAuD,KAAK;MACbC,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAEzD,MAAA,CAAA0D,gBAAgB;MAC7BC,eAAc,EAAE3D,MAAA,CAAA4D;;;MAKvBlE,mBAAA,aAAgB,EAChBC,YAAA,CAoEYkE,oBAAA;gBAnED7D,MAAA,CAAA8D,aAAa;iEAAb9D,MAAA,CAAA8D,aAAa,GAAAxD,MAAA;IACrByD,KAAK,EAAE/D,MAAA,CAAAgE,UAAU;IAClB5B,KAAK,EAAC;;IA2DK6B,MAAM,EAAAhD,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNgD,UAGM,GAFJvE,YAAA,CAAwDe,oBAAA;MAA5CE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAR,MAAA,IAAEN,MAAA,CAAA8D,aAAa;;wBAAU,MAAEhD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CnB,YAAA,CAA4De,oBAAA;MAAjDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAmE;;wBAAY,MAAErD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBA5DpD,MAwDU,CAxDVnB,YAAA,CAwDUE,kBAAA;MAvDPE,KAAK,EAAEC,MAAA,CAAAoE,QAAQ;MACfC,KAAK,EAAErE,MAAA,CAAAsE,aAAa;MACrBC,GAAG,EAAC,aAAa;MACjB,aAAW,EAAC;;wBAEZ,MAEe,CAFf5E,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACmC,IAAI,EAAC;;0BAC7B,MAAwG,CAAxG3C,YAAA,CAAwGS,mBAAA;sBAArFJ,MAAA,CAAAoE,QAAQ,CAAC/D,QAAQ;qEAAjBL,MAAA,CAAAoE,QAAQ,CAAC/D,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAAEgB,QAAQ,EAAEvB,MAAA,CAAAgE,UAAU;;;UAElFrE,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC5B,MAAiE,CAAjE3C,YAAA,CAAiES,mBAAA;sBAA9CJ,MAAA,CAAAoE,QAAQ,CAACI,IAAI;qEAAbxE,MAAA,CAAAoE,QAAQ,CAACI,IAAI,GAAAlE,MAAA;UAAEC,WAAW,EAAC;;;UAE5BP,MAAA,CAAAgE,UAAU,c,cAA9BpC,YAAA,CAEe1B,uBAAA;;QAF2BC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BACxD,MAAmG,CAAnG3C,YAAA,CAAmGS,mBAAA;sBAAhFJ,MAAA,CAAAoE,QAAQ,CAACK,QAAQ;qEAAjBzE,MAAA,CAAAoE,QAAQ,CAACK,QAAQ,GAAAnE,MAAA;UAAEK,IAAI,EAAC,UAAU;UAACJ,WAAW,EAAC,OAAO;UAAC,eAAa,EAAb;;;+CAE5EZ,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACmC,IAAI,EAAC;;0BAC7B,MAAmE,CAAnE3C,YAAA,CAAmES,mBAAA;sBAAhDJ,MAAA,CAAAoE,QAAQ,CAAC3D,KAAK;qEAAdT,MAAA,CAAAoE,QAAQ,CAAC3D,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC;;;UAEjDZ,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC5B,MAAkE,CAAlE3C,YAAA,CAAkES,mBAAA;sBAA/CJ,MAAA,CAAAoE,QAAQ,CAACM,KAAK;qEAAd1E,MAAA,CAAAoE,QAAQ,CAACM,KAAK,GAAApE,MAAA;UAAEC,WAAW,EAAC;;;UAEjDZ,YAAA,CAMeO,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC5B,MAIY,CAJZ3C,YAAA,CAIYgF,oBAAA;sBAJQ3E,MAAA,CAAAoE,QAAQ,CAAC1B,IAAI;qEAAb1C,MAAA,CAAAoE,QAAQ,CAAC1B,IAAI,GAAApC,MAAA;UAAEC,WAAW,EAAC;;4BAC7C,MAAiD,CAAjDZ,YAAA,CAAiDiF,oBAAA;YAAtCzE,KAAK,EAAC,KAAK;YAAC0E,KAAK,EAAC;cAC7BlF,YAAA,CAAoDiF,oBAAA;YAAzCzE,KAAK,EAAC,MAAM;YAAC0E,KAAK,EAAC;cAC9BlF,YAAA,CAAkDiF,oBAAA;YAAvCzE,KAAK,EAAC,IAAI;YAAC0E,KAAK,EAAC;;;;;UAIhCnF,mBAAA,sBAAyB,EACLM,MAAA,CAAAoE,QAAQ,CAAC1B,IAAI,kB,cAAjCd,YAAA,CAoBe1B,uBAAA;;QApBkCC,KAAK,EAAC,KAAK;QAACmC,IAAI,EAAC;;0BAChE,MAiBY,CAjBZ3C,YAAA,CAiBYgF,oBAAA;sBAhBD3E,MAAA,CAAAoE,QAAQ,CAACU,UAAU;uEAAnB9E,MAAA,CAAAoE,QAAQ,CAACU,UAAU,GAAAxE,MAAA;UAC5BC,WAAW,EAAC,WAAW;UACvBwE,UAAU,EAAV,EAAU;UACT7B,OAAO,EAAElD,MAAA,CAAAgF;;4BAGR,MAA2B,E,kBAD7BxF,mBAAA,CAUYyF,SAAA,QAAAC,WAAA,CATKlF,MAAA,CAAAmF,WAAW,EAAnBC,IAAI;iCADbxD,YAAA,CAUYgD,oBAAA;cARTS,GAAG,EAAED,IAAI,CAACE,EAAE;cACZnF,KAAK,KAAKiF,IAAI,CAACZ,IAAI,MAAMY,IAAI,CAACG,MAAM;cACpCV,KAAK,EAAEO,IAAI,CAACE;;gCAEb,MAGM,CAHNpE,mBAAA,CAGM,OAHNsE,UAGM,GAFJtE,mBAAA,CAA4B,cAAAuE,gBAAA,CAAnBL,IAAI,CAACZ,IAAI,kBAClBtD,mBAAA,CAAqD,QAArDwE,UAAqD,EAAAD,gBAAA,CAArBL,IAAI,CAACG,MAAM,iB;;;;;kFAIjDrE,mBAAA,CAAyC;UAApC5B,KAAK,EAAC;QAAU,GAAC,eAAa,oB;;;+CAGrCI,mBAAA,qBAAwB,EACJM,MAAA,CAAAoE,QAAQ,CAAC1B,IAAI,kB,cAAjCd,YAAA,CAEe1B,uBAAA;;QAFkCC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC/D,MAA2E,CAA3E3C,YAAA,CAA2ES,mBAAA;sBAAxDJ,MAAA,CAAAoE,QAAQ,CAACU,UAAU;uEAAnB9E,MAAA,CAAAoE,QAAQ,CAACU,UAAU,GAAAxE,MAAA;UAAEC,WAAW,EAAC;;;;;;;8CAW1Db,mBAAA,aAAgB,EAChBC,YAAA,CA0DYkE,oBAAA;gBAzDD7D,MAAA,CAAA2F,mBAAmB;iEAAnB3F,MAAA,CAAA2F,mBAAmB,GAAArF,MAAA;IAC5ByD,KAAK,EAAC,QAAQ;IACd3B,KAAK,EAAC;;IA+CK6B,MAAM,EAAAhD,QAAA,CACf,MAKO,CALPC,mBAAA,CAKO,QALP0E,UAKO,GAJLjG,YAAA,CAA8De,oBAAA;MAAlDE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAR,MAAA,IAAEN,MAAA,CAAA2F,mBAAmB;;wBAAU,MAAE7E,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAClDnB,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAA6F,YAAY;MAAG3C,OAAO,EAAElD,MAAA,CAAA8F;;wBAAe,MAEzEhF,MAAA,SAAAA,MAAA,Q,iBAFyE,QAEzE,E;;;;sBAlDJ,MA2CM,CA3CNI,mBAAA,CA2CM,OA3CN6E,UA2CM,GA1CJpG,YAAA,CAcWqG,mBAAA;MAbTjC,KAAK,EAAC,MAAM;MACZpD,IAAI,EAAC,MAAM;MACVsF,QAAQ,EAAE,KAAK;MAChB1G,KAA2B,EAA3B;QAAA;MAAA;;MAEWgD,OAAO,EAAAtB,QAAA,CAChB,MAA+BH,MAAA,SAAAA,MAAA,QAA/BI,mBAAA,CAA+B,WAA5B,0BAAwB,oBAC3BA,mBAAA,CAA6B,WAA1B,wBAAsB,oBACzBA,mBAAA,CAAqB,WAAlB,gBAAc,oBACjBA,mBAAA,CAAwB,WAArB,mBAAiB,oBACpBA,mBAAA,CAA+B,WAA5B,0BAAwB,oBAC3BA,mBAAA,CAAoC,WAAjC,+BAA6B,mB;;QAIpCA,mBAAA,CAIM,OAJNgF,UAIM,GAHJvG,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAmG;;wBAChC,MAA+B,CAA/BxG,YAAA,CAA+B0B,kBAAA;0BAAtB,MAAY,CAAZ1B,YAAA,CAAYK,MAAA,c;;uDAAU,aACjC,G;;;UAGFL,YAAA,CAmBYyG,oBAAA;MAlBV7B,GAAG,EAAC,WAAW;MACfjF,KAAK,EAAC,aAAa;MACnB+G,IAAI,EAAJ,EAAI;MACH,aAAW,EAAE,KAAK;MAClBC,KAAK,EAAE,CAAC;MACR,WAAS,EAAEtG,MAAA,CAAAuG,gBAAgB;MAC3B,eAAa,EAAEvG,MAAA,CAAAwG,YAAY;MAC5BC,MAAM,EAAC;;MAMIC,GAAG,EAAAzF,QAAA,CACZ,MAEMH,MAAA,SAAAA,MAAA,QAFNI,mBAAA,CAEM;QAFD5B,KAAK,EAAC;MAAgB,GAAC,+BAE5B,mB;wBAPF,MAA4D,CAA5DK,YAAA,CAA4D0B,kBAAA;QAAnD/B,KAAK,EAAC;MAAiB;0BAAC,MAAiB,CAAjBK,YAAA,CAAiBK,MAAA,kB;;sCAClDkB,mBAAA,CAEM;QAFD5B,KAAK,EAAC;MAAiB,I,iBAAC,iBACb,GAAA4B,mBAAA,CAAa,YAAT,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
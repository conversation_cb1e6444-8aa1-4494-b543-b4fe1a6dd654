{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"reset-password-container\"\n};\nconst _hoisted_2 = {\n  class: \"reset-password-card\"\n};\nconst _hoisted_3 = {\n  class: \"login-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"logo-wrapper\"\n  }, [_createElementVNode(\"div\", {\n    class: \"logo-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"el-icon-key\"\n  })]), _createElementVNode(\"div\", {\n    class: \"logo-text\"\n  }, \" 重置密码 \")], -1 /* CACHED */)), _createVNode(_component_el_form, {\n    model: $setup.resetForm,\n    rules: $setup.resetRules,\n    ref: \"resetFormRef\",\n    class: \"reset-form\"\n  }, {\n    default: _withCtx(() => [_cache[5] || (_cache[5] = _createElementVNode(\"p\", {\n      class: \"form-subtitle\"\n    }, \"请输入新密码\", -1 /* CACHED */)), _createVNode(_component_el_form_item, {\n      prop: \"newPassword\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.resetForm.newPassword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.resetForm.newPassword = $event),\n        type: \"password\",\n        placeholder: \"新密码\",\n        \"prefix-icon\": $setup.Lock,\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      prop: \"confirmPassword\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.resetForm.confirmPassword,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.resetForm.confirmPassword = $event),\n        type: \"password\",\n        placeholder: \"确认新密码\",\n        \"prefix-icon\": $setup.Lock,\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        loading: $setup.loading,\n        onClick: $setup.handleReset,\n        class: \"reset-button\"\n      }, {\n        default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\" 重置密码 \")])),\n        _: 1 /* STABLE */,\n        __: [2]\n      }, 8 /* PROPS */, [\"loading\"])]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_3, [_cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"记住密码了？\", -1 /* CACHED */)), _createVNode(_component_router_link, {\n      to: \"/login\"\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"返回登录\")])),\n      _: 1 /* STABLE */,\n      __: [3]\n    })])]),\n    _: 1 /* STABLE */,\n    __: [5]\n  }, 8 /* PROPS */, [\"model\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_form", "model", "$setup", "resetForm", "rules", "resetRules", "ref", "_component_el_form_item", "prop", "_component_el_input", "newPassword", "$event", "type", "placeholder", "Lock", "confirmPassword", "_component_el_button", "loading", "onClick", "handleReset", "_cache", "_hoisted_3", "_component_router_link", "to"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\views\\ResetPasswordView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"reset-password-container\">\r\n    <div class=\"reset-password-card\">\r\n      <div class=\"logo-wrapper\">\r\n        <div class=\"logo-icon\">\r\n          <i class=\"el-icon-key\"></i>\r\n        </div>\r\n        <div class=\"logo-text\">\r\n          重置密码\r\n        </div>\r\n      </div>\r\n      \r\n      <el-form :model=\"resetForm\" :rules=\"resetRules\" ref=\"resetFormRef\" class=\"reset-form\">\r\n        <p class=\"form-subtitle\">请输入新密码</p>\r\n        \r\n        <el-form-item prop=\"newPassword\">\r\n          <el-input \r\n            v-model=\"resetForm.newPassword\" \r\n            type=\"password\" \r\n            placeholder=\"新密码\" \r\n            :prefix-icon=\"Lock\"\r\n            show-password>\r\n          </el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item prop=\"confirmPassword\">\r\n          <el-input \r\n            v-model=\"resetForm.confirmPassword\" \r\n            type=\"password\" \r\n            placeholder=\"确认新密码\" \r\n            :prefix-icon=\"Lock\"\r\n            show-password>\r\n          </el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item>\r\n          <el-button type=\"primary\" :loading=\"loading\" @click=\"handleReset\" class=\"reset-button\">\r\n            重置密码\r\n          </el-button>\r\n        </el-form-item>\r\n        \r\n        <div class=\"login-link\">\r\n          <span>记住密码了？</span>\r\n          <router-link to=\"/login\">返回登录</router-link>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Lock } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nconst route = useRoute()\r\nconst router = useRouter()\r\nconst resetFormRef = ref(null)\r\nconst loading = ref(false)\r\nconst token = ref('')\r\n\r\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\r\n\r\n// 表单数据\r\nconst resetForm = reactive({\r\n  newPassword: '',\r\n  confirmPassword: ''\r\n})\r\n\r\n// 自定义验证规则\r\nconst validatePass2 = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入密码'))\r\n  } else if (value !== resetForm.newPassword) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\n// 表单验证规则\r\nconst resetRules = {\r\n  newPassword: [\r\n    { required: true, message: '请输入新密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  confirmPassword: [\r\n    { required: true, message: '请确认密码', trigger: 'blur' },\r\n    { validator: validatePass2, trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 处理重置密码\r\nconst handleReset = async () => {\r\n  if (!resetFormRef.value) return\r\n  \r\n  try {\r\n    await resetFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        loading.value = true\r\n        \r\n        try {\r\n          const response = await axios.post(`${API_URL}/auth/reset-password`, {\r\n            token: token.value,\r\n            newPassword: resetForm.newPassword\r\n          })\r\n          \r\n          ElMessage.success('密码重置成功，请使用新密码登录')\r\n          router.push('/login')\r\n        } catch (error) {\r\n          console.error('重置密码失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '重置密码失败，请检查链接是否有效')\r\n        } finally {\r\n          loading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    loading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 组件加载时从URL参数中获取token\r\nonMounted(() => {\r\n  token.value = route.query.token\r\n  \r\n  if (!token.value) {\r\n    ElMessage.error('无效的重置链接，请重新获取')\r\n    router.push('/login')\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.reset-password-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: rgb(124, 181, 239);\r\n}\r\n\r\n.reset-password-card {\r\n  width: 400px;\r\n  background-color: white;\r\n  border-radius: 12px;\r\n  padding: 40px;\r\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  justify-content: center;\r\n}\r\n\r\n.logo-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background-color: #409EFF;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.form-subtitle {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.reset-form :deep(.el-input__wrapper) {\r\n  padding: 0 15px;\r\n  height: 50px;\r\n  box-shadow: 0 0 0 1px #e4e7ed inset;\r\n}\r\n\r\n.reset-form :deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n.reset-button {\r\n  width: 100%;\r\n  height: 50px;\r\n  border-radius: 6px;\r\n  font-size: 16px;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.login-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.login-link a {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAqB;;EAuCvBA,KAAK,EAAC;AAAY;;;;;;;uBAxC7BC,mBAAA,CA8CM,OA9CNC,UA8CM,GA7CJC,mBAAA,CA4CM,OA5CNC,UA4CM,G,0BA3CJD,mBAAA,CAOM;IAPDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAW,IACpBG,mBAAA,CAA2B;IAAxBH,KAAK,EAAC;EAAa,G,GAExBG,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAW,GAAC,QAEvB,E,qBAGFK,YAAA,CAiCUC,kBAAA;IAjCAC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAAGC,KAAK,EAAEF,MAAA,CAAAG,UAAU;IAAEC,GAAG,EAAC,cAAc;IAACZ,KAAK,EAAC;;sBACvE,MAAmC,C,0BAAnCG,mBAAA,CAAmC;MAAhCH,KAAK,EAAC;IAAe,GAAC,QAAM,qBAE/BK,YAAA,CAQeQ,uBAAA;MARDC,IAAI,EAAC;IAAa;wBAC9B,MAMW,CANXT,YAAA,CAMWU,mBAAA;oBALAP,MAAA,CAAAC,SAAS,CAACO,WAAW;mEAArBR,MAAA,CAAAC,SAAS,CAACO,WAAW,GAAAC,MAAA;QAC9BC,IAAI,EAAC,UAAU;QACfC,WAAW,EAAC,KAAK;QAChB,aAAW,EAAEX,MAAA,CAAAY,IAAI;QAClB,eAAa,EAAb;;;QAIJf,YAAA,CAQeQ,uBAAA;MARDC,IAAI,EAAC;IAAiB;wBAClC,MAMW,CANXT,YAAA,CAMWU,mBAAA;oBALAP,MAAA,CAAAC,SAAS,CAACY,eAAe;mEAAzBb,MAAA,CAAAC,SAAS,CAACY,eAAe,GAAAJ,MAAA;QAClCC,IAAI,EAAC,UAAU;QACfC,WAAW,EAAC,OAAO;QAClB,aAAW,EAAEX,MAAA,CAAAY,IAAI;QAClB,eAAa,EAAb;;;QAIJf,YAAA,CAIeQ,uBAAA;wBAHb,MAEY,CAFZR,YAAA,CAEYiB,oBAAA;QAFDJ,IAAI,EAAC,SAAS;QAAEK,OAAO,EAAEf,MAAA,CAAAe,OAAO;QAAGC,OAAK,EAAEhB,MAAA,CAAAiB,WAAW;QAAEzB,KAAK,EAAC;;0BAAe,MAEvF0B,MAAA,QAAAA,MAAA,O,iBAFuF,QAEvF,E;;;;;QAGFvB,mBAAA,CAGM,OAHNwB,UAGM,G,0BAFJxB,mBAAA,CAAmB,cAAb,QAAM,qBACZE,YAAA,CAA2CuB,sBAAA;MAA9BC,EAAE,EAAC;IAAQ;wBAAC,MAAIH,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
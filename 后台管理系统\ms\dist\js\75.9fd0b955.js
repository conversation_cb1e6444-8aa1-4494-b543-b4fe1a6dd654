"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[75],{9075:function(e,a,l){l.r(a),l.d(a,{default:function(){return k}});var t=l(6768),o=l(4232);const r={class:"student-list-container"},d={class:"filter-container"},n={class:"card-header"},s={class:"pagination-container"},u={class:"dialog-footer"};function i(e,a,l,i,c,m){const _=(0,t.g2)("el-input"),p=(0,t.g2)("el-form-item"),g=(0,t.g2)("Search"),h=(0,t.g2)("el-icon"),b=(0,t.g2)("el-button"),f=(0,t.g2)("el-form"),k=(0,t.g2)("el-card"),F=(0,t.g2)("Plus"),v=(0,t.g2)("el-table-column"),V=(0,t.g2)("el-tag"),y=(0,t.g2)("el-table"),w=(0,t.g2)("el-pagination"),C=(0,t.g2)("el-radio"),S=(0,t.g2)("el-radio-group"),U=(0,t.g2)("el-date-picker"),$=(0,t.g2)("el-switch"),z=(0,t.g2)("el-dialog"),A=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.bF)(k,{class:"filter-card"},{default:(0,t.k6)(()=>[(0,t.Lk)("div",d,[(0,t.bF)(f,{inline:!0,model:i.searchForm,class:"search-form"},{default:(0,t.k6)(()=>[(0,t.bF)(p,{label:"姓名"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.searchForm.name,"onUpdate:modelValue":a[0]||(a[0]=e=>i.searchForm.name=e),placeholder:"搜索实习生姓名",clearable:""},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"学校"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.searchForm.school,"onUpdate:modelValue":a[1]||(a[1]=e=>i.searchForm.school=e),placeholder:"搜索实习生学校",clearable:""},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)(()=>[(0,t.bF)(b,{type:"primary",onClick:i.handleSearch},{default:(0,t.k6)(()=>[(0,t.bF)(h,null,{default:(0,t.k6)(()=>[(0,t.bF)(g)]),_:1}),a[23]||(a[23]=(0,t.eW)(" 搜索 "))]),_:1,__:[23]},8,["onClick"]),(0,t.bF)(b,{onClick:i.resetSearch},{default:(0,t.k6)(()=>a[24]||(a[24]=[(0,t.eW)("重置")])),_:1,__:[24]},8,["onClick"])]),_:1})]),_:1},8,["model"])])]),_:1}),(0,t.bF)(k,{class:"table-card"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",n,[a[27]||(a[27]=(0,t.Lk)("span",null,"实习生列表",-1)),(0,t.Lk)("div",null,[(0,t.bF)(b,{type:"danger",onClick:i.handleAdd},{default:(0,t.k6)(()=>[(0,t.bF)(h,null,{default:(0,t.k6)(()=>[(0,t.bF)(F)]),_:1}),a[25]||(a[25]=(0,t.eW)(" 批量导入 "))]),_:1,__:[25]},8,["onClick"]),(0,t.bF)(b,{type:"primary",onClick:i.handleAdd},{default:(0,t.k6)(()=>[(0,t.bF)(h,null,{default:(0,t.k6)(()=>[(0,t.bF)(F)]),_:1}),a[26]||(a[26]=(0,t.eW)(" 添加实习生 "))]),_:1,__:[26]},8,["onClick"])])])]),default:(0,t.k6)(()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(y,{data:i.tableData,stripe:"",border:"",style:{width:"100%"}},{default:(0,t.k6)(()=>[(0,t.bF)(v,{prop:"id",label:"ID","min-width":"60"}),(0,t.bF)(v,{prop:"name",label:"姓名","min-width":"100"}),(0,t.bF)(v,{prop:"gender",label:"性别","min-width":"60"}),(0,t.bF)(v,{prop:"phone",label:"手机号","min-width":"120"}),(0,t.bF)(v,{prop:"school",label:"学校","min-width":"140"}),(0,t.bF)(v,{prop:"major",label:"专业","min-width":"140"}),(0,t.bF)(v,{label:"实习期间","min-width":"160"},{default:(0,t.k6)(e=>[(0,t.eW)((0,o.v_)(i.formatDateDisplay(e.row.start_date))+" 至 "+(0,o.v_)(i.formatDateDisplay(e.row.end_date)),1)]),_:1}),(0,t.bF)(v,{prop:"is_leader",label:"组长","min-width":"70",align:"center"},{default:(0,t.k6)(e=>[(0,t.bF)(V,{type:e.row.is_leader?"success":"info"},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(e.row.is_leader?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(v,{prop:"is_living_outside",label:"外宿","min-width":"70",align:"center"},{default:(0,t.k6)(e=>[(0,t.bF)(V,{type:e.row.is_living_outside?"warning":"info"},{default:(0,t.k6)(()=>[(0,t.eW)((0,o.v_)(e.row.is_living_outside?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),(0,t.bF)(v,{label:"操作","min-width":"120",align:"center",fixed:"right"},{default:(0,t.k6)(e=>[(0,t.bF)(b,{size:"small",onClick:a=>i.handleEdit(e.row)},{default:(0,t.k6)(()=>a[28]||(a[28]=[(0,t.eW)("编辑")])),_:2,__:[28]},1032,["onClick"]),(0,t.bF)(b,{size:"small",type:"danger",onClick:a=>i.handleDelete(e.row)},{default:(0,t.k6)(()=>a[29]||(a[29]=[(0,t.eW)("删除")])),_:2,__:[29]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,i.loading]]),(0,t.Lk)("div",s,[(0,t.bF)(w,{"current-page":i.currentPage,"onUpdate:currentPage":a[2]||(a[2]=e=>i.currentPage=e),"page-size":i.pageSize,"onUpdate:pageSize":a[3]||(a[3]=e=>i.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:i.total,onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),(0,t.bF)(z,{modelValue:i.dialogVisible,"onUpdate:modelValue":a[22]||(a[22]=e=>i.dialogVisible=e),title:"add"===i.dialogType?"添加实习生":"编辑实习生",width:"500px"},{footer:(0,t.k6)(()=>[(0,t.Lk)("span",u,[(0,t.bF)(b,{onClick:a[21]||(a[21]=e=>i.dialogVisible=!1)},{default:(0,t.k6)(()=>a[32]||(a[32]=[(0,t.eW)("取消")])),_:1,__:[32]}),(0,t.bF)(b,{type:"primary",onClick:i.submitForm},{default:(0,t.k6)(()=>a[33]||(a[33]=[(0,t.eW)("确认")])),_:1,__:[33]},8,["onClick"])])]),default:(0,t.k6)(()=>[(0,t.bF)(f,{model:i.form,rules:i.rules,ref:"formRef","label-width":"100px"},{default:(0,t.k6)(()=>[(0,t.bF)(p,{label:"姓名",prop:"name"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.name,"onUpdate:modelValue":a[4]||(a[4]=e=>i.form.name=e),placeholder:"请输入实习生姓名"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"性别",prop:"gender"},{default:(0,t.k6)(()=>[(0,t.bF)(S,{modelValue:i.form.gender,"onUpdate:modelValue":a[5]||(a[5]=e=>i.form.gender=e)},{default:(0,t.k6)(()=>[(0,t.bF)(C,{label:"男"},{default:(0,t.k6)(()=>a[30]||(a[30]=[(0,t.eW)("男")])),_:1,__:[30]}),(0,t.bF)(C,{label:"女"},{default:(0,t.k6)(()=>a[31]||(a[31]=[(0,t.eW)("女")])),_:1,__:[31]})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"手机号",prop:"phone"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.phone,"onUpdate:modelValue":a[6]||(a[6]=e=>i.form.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"学校",prop:"school"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.school,"onUpdate:modelValue":a[7]||(a[7]=e=>i.form.school=e),placeholder:"请输入学校"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"专业",prop:"major"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.major,"onUpdate:modelValue":a[8]||(a[8]=e=>i.form.major=e),placeholder:"请输入专业"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"开始日期",prop:"start_date"},{default:(0,t.k6)(()=>[(0,t.bF)(U,{modelValue:i.form.start_date,"onUpdate:modelValue":a[9]||(a[9]=e=>i.form.start_date=e),type:"date",placeholder:"选择开始日期"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"结束日期",prop:"end_date"},{default:(0,t.k6)(()=>[(0,t.bF)(U,{modelValue:i.form.end_date,"onUpdate:modelValue":a[10]||(a[10]=e=>i.form.end_date=e),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"家庭住址",prop:"home_address"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.home_address,"onUpdate:modelValue":a[11]||(a[11]=e=>i.form.home_address=e),placeholder:"请输入家庭住址"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"当前住址",prop:"current_address"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.current_address,"onUpdate:modelValue":a[12]||(a[12]=e=>i.form.current_address=e),placeholder:"请输入当前住址"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"是否外宿",prop:"is_living_outside"},{default:(0,t.k6)(()=>[(0,t.bF)($,{modelValue:i.form.is_living_outside,"onUpdate:modelValue":a[13]||(a[13]=e=>i.form.is_living_outside=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),i.form.is_living_outside?((0,t.uX)(),(0,t.Wv)(p,{key:0,label:"同宿联系人",prop:"roommate_contact"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.roommate_contact,"onUpdate:modelValue":a[14]||(a[14]=e=>i.form.roommate_contact=e),placeholder:"请输入同宿联系人"},null,8,["modelValue"])]),_:1})):(0,t.Q3)("",!0),(0,t.bF)(p,{label:"是否组长",prop:"is_leader"},{default:(0,t.k6)(()=>[(0,t.bF)($,{modelValue:i.form.is_leader,"onUpdate:modelValue":a[15]||(a[15]=e=>i.form.is_leader=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"备注",prop:"notes"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.notes,"onUpdate:modelValue":a[16]||(a[16]=e=>i.form.notes=e),type:"textarea",placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"校方联系人",prop:"school_contact_name"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.school_contact_name,"onUpdate:modelValue":a[17]||(a[17]=e=>i.form.school_contact_name=e),placeholder:"请输入校方联系人"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"校方电话",prop:"school_contact_phone"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.school_contact_phone,"onUpdate:modelValue":a[18]||(a[18]=e=>i.form.school_contact_phone=e),placeholder:"请输入校方联系电话"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"家庭联系人",prop:"family_contact_name"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.family_contact_name,"onUpdate:modelValue":a[19]||(a[19]=e=>i.form.family_contact_name=e),placeholder:"请输入家庭联系人"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"家庭电话",prop:"family_contact_phone"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:i.form.family_contact_phone,"onUpdate:modelValue":a[20]||(a[20]=e=>i.form.family_contact_phone=e),placeholder:"请输入家庭联系电话"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}var c=l(144),m=l(7477),_=l(1219),p=l(2933),g=l(4373),h={name:"StudentList",setup(){const e=(0,c.KR)(!1),a=(0,c.KR)(1),l=(0,c.KR)(10),o=(0,c.KR)(0),r=(0,c.KR)([]),d=(0,c.KR)(!1),n=(0,c.KR)("add"),s=(0,c.KR)(null),u=(0,c.Kh)({name:"",school:""}),i=(0,c.Kh)({id:"",name:"",gender:"男",phone:"",school:"",major:"",start_date:"",end_date:"",home_address:"",current_address:"",school_contact_name:"",school_contact_phone:"",family_contact_name:"",family_contact_phone:"",notes:"",is_leader:0,is_living_outside:0,roommate_contact:""}),h={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],gender:[{required:!0,message:"请选择性别",trigger:"change"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式",trigger:"blur"}],school:[{required:!0,message:"请输入学校",trigger:"blur"}],major:[{required:!0,message:"请输入专业",trigger:"blur"}],start_date:[{required:!0,message:"请选择开始日期",trigger:"change"}],end_date:[{required:!0,message:"请选择结束日期",trigger:"change"}],home_address:[{required:!0,message:"请输入家庭住址",trigger:"blur"}],current_address:[{required:!0,message:"请输入当前住址",trigger:"blur"}],school_contact_name:[{required:!0,message:"请输入校方联系人",trigger:"blur"}],school_contact_phone:[{required:!0,message:"请输入校方联系电话",trigger:"blur"}],family_contact_name:[{required:!0,message:"请输入家庭联系人",trigger:"blur"}],family_contact_phone:[{required:!0,message:"请输入家庭联系电话",trigger:"blur"}]},b={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_BASE_URL||"http://localhost:3000/api",f=()=>localStorage.getItem("token")||"",k=()=>({headers:{Authorization:`Bearer ${f()}`}}),F=async()=>{e.value=!0;try{const e=await g.A.get(`${b}/students`,k());if(e.data.success){o.value=e.data.data.length;const t=(a.value-1)*l.value;r.value=e.data.data.slice(t,t+l.value)}else _.nk.error(e.data.message||"获取学生列表失败")}catch(t){console.error("获取学生列表失败:",t),_.nk.error("获取学生列表失败")}finally{e.value=!1}};(0,t.sV)(()=>{F()});const v=()=>{u.name="",u.school="",F()},V=async()=>{a.value=1,e.value=!0;try{if(!u.name&&!u.school)return void await F();const e={};u.name&&(e.name=u.name),u.school&&(e.school=u.school);const a=await g.A.get(`${b}/students/search`,{params:e,headers:{Authorization:`Bearer ${f()}`}});a.data.success?(o.value=a.data.data.length,r.value=a.data.data.slice(0,l.value),_.nk.success(`找到 ${a.data.count||a.data.data.length} 条匹配记录`)):_.nk.error(a.data.message||"搜索学生失败")}catch(t){console.error("搜索学生失败:",t),_.nk.error("搜索学生失败")}finally{e.value=!1}},y=e=>{l.value=e,F()},w=e=>{a.value=e,F()},C=()=>{n.value="add",d.value=!0,s.value&&s.value.resetFields(),Object.assign(i,{id:"",name:"",gender:"男",phone:"",school:"",major:"",start_date:"",end_date:"",home_address:"",current_address:"",school_contact_name:"",school_contact_phone:"",family_contact_name:"",family_contact_phone:"",notes:"",is_leader:0,is_living_outside:0,roommate_contact:""})},S=async a=>{n.value="edit",d.value=!0,e.value=!0;try{const e=await g.A.get(`${b}/students/${a.id}`,k());e.data.success?Object.assign(i,e.data.data):_.nk.error(e.data.message||"获取学生详情失败")}catch(l){console.error("获取学生详情失败:",l),_.nk.error("获取学生详情失败")}finally{e.value=!1}},U=e=>{p.s.confirm(`确定要删除实习生 ${e.name} 吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const a=await g.A.delete(`${b}/students/${e.id}`,k());a.data.success?(_.nk.success("删除成功"),F()):_.nk.error(a.data.message||"删除失败")}catch(a){console.error("删除学生失败:",a),_.nk.error("删除学生失败")}}).catch(()=>{})},$=e=>{if(!e)return"-";const a=new Date(e);return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`},z=()=>{s.value.validate(async a=>{if(!a)return!1;e.value=!0;try{let e;const a=k().headers,l={...i};l.start_date&&(l.start_date=A(l.start_date)),l.end_date&&(l.end_date=A(l.end_date)),e="add"===n.value?await g.A.post(`${b}/students`,l,{headers:a}):await g.A.put(`${b}/students/${l.id}`,l,{headers:a}),e.data.success?(_.nk.success("add"===n.value?"添加成功":"修改成功"),d.value=!1,F()):_.nk.error(e.data.message||("add"===n.value?"添加失败":"修改失败"))}catch(l){console.error("add"===n.value?"添加学生失败:":"更新学生失败:",l),l.response&&l.response.data?_.nk.error(l.response.data.message||("add"===n.value?"添加学生失败":"更新学生失败")):_.nk.error("add"===n.value?"添加学生失败":"更新学生失败")}finally{e.value=!1}})},A=e=>{if(!e)return"";"string"===typeof e&&(e=new Date(e));const a=e.getFullYear(),l=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0");return`${a}-${l}-${t}`};return{loading:e,searchForm:u,currentPage:a,pageSize:l,total:o,tableData:r,dialogVisible:d,dialogType:n,form:i,rules:h,formRef:s,Search:m.Search,Plus:m.Plus,formatDateDisplay:$,handleSearch:V,resetSearch:v,handleSizeChange:y,handleCurrentChange:w,handleAdd:C,handleEdit:S,handleDelete:U,submitForm:z}}},b=l(1241);const f=(0,b.A)(h,[["render",i],["__scopeId","data-v-57bff1f2"]]);var k=f}}]);
//# sourceMappingURL=75.9fd0b955.js.map
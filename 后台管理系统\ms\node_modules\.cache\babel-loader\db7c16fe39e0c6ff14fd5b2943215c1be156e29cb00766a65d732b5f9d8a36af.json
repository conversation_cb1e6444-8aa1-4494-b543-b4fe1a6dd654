{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, mergeProps, unref, renderSlot } from 'vue';\nimport { iconProps } from './icon.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nconst __default__ = defineComponent({\n  name: \"ElIcon\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: iconProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"icon\");\n    const style = computed(() => {\n      const {\n        size,\n        color\n      } = props;\n      if (!size && !color) return {};\n      return {\n        fontSize: isUndefined(size) ? void 0 : addUnit(size),\n        \"--color\": color\n      };\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"i\", mergeProps({\n        class: unref(ns).b(),\n        style: unref(style)\n      }, _ctx.$attrs), [renderSlot(_ctx.$slots, \"default\")], 16);\n    };\n  }\n});\nvar Icon = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"icon.vue\"]]);\nexport { Icon as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "ns", "useNamespace", "style", "computed", "size", "color", "props", "fontSize", "isUndefined", "addUnit"], "sources": ["../../../../../../packages/components/icon/src/icon.vue"], "sourcesContent": ["<template>\n  <i :class=\"ns.b()\" :style=\"style\" v-bind=\"$attrs\">\n    <slot />\n  </i>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { addUnit, isUndefined } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { iconProps } from './icon'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElIcon',\n  inheritAttrs: false,\n})\nconst props = defineProps(iconProps)\nconst ns = useNamespace('icon')\n\nconst style = computed<CSSProperties>(() => {\n  const { size, color } = props\n  if (!size && !color) return {}\n\n  return {\n    fontSize: isUndefined(size) ? undefined : addUnit(size),\n    '--color': color,\n  }\n})\n</script>\n"], "mappings": ";;;;;;mCAcc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;IAEM,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IAExB,MAAAC,KAAA,GAAQC,QAAA,CAAwB,MAAM;MACpC;QAAEC,IAAM;QAAAC;MAAA,CAAU,GAAAC,KAAA;MACxB,IAAI,CAACF,IAAA,IAAQ,CAACC,KAAA,EAEP;MAAA;QAELE,QAAW,EAAAC,WAAA,CAAAJ,IAAA,aAAAK,OAAA,CAAAL,IAAA;QACb,WAAAC;MAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ref, getCurrentInstance, inject, computed, unref } from 'vue';\nimport { isNumber } from '../../utils/types.mjs';\nimport { isClient } from '@vueuse/core';\nimport { debugWarn } from '../../utils/error.mjs';\nconst initial = {\n  current: 0\n};\nconst zIndex = ref(0);\nconst defaultInitialZIndex = 2e3;\nconst ZINDEX_INJECTION_KEY = Symbol(\"elZIndexContextKey\");\nconst zIndexContextKey = Symbol(\"zIndexContextKey\");\nconst useZIndex = zIndexOverrides => {\n  const increasingInjection = getCurrentInstance() ? inject(ZINDEX_INJECTION_KEY, initial) : initial;\n  const zIndexInjection = zIndexOverrides || (getCurrentInstance() ? inject(zIndexContextKey, void 0) : void 0);\n  const initialZIndex = computed(() => {\n    const zIndexFromInjection = unref(zIndexInjection);\n    return isNumber(zIndexFromInjection) ? zIndexFromInjection : defaultInitialZIndex;\n  });\n  const currentZIndex = computed(() => initialZIndex.value + zIndex.value);\n  const nextZIndex = () => {\n    increasingInjection.current++;\n    zIndex.value = increasingInjection.current;\n    return currentZIndex.value;\n  };\n  if (!isClient && !inject(ZINDEX_INJECTION_KEY)) {\n    debugWarn(\"ZIndexInjection\", `Looks like you are using server rendering, you must provide a z-index provider to ensure the hydration process to be succeed\nusage: app.provide(ZINDEX_INJECTION_KEY, { current: 0 })`);\n  }\n  return {\n    initialZIndex,\n    currentZIndex,\n    nextZIndex\n  };\n};\nexport { ZINDEX_INJECTION_KEY, defaultInitialZIndex, useZIndex, zIndexContextKey };", "map": {"version": 3, "names": ["initial", "current", "zIndex", "ref", "defaultInitialZIndex", "ZINDEX_INJECTION_KEY", "Symbol", "zIndexContextKey", "useZIndex", "zIndexOverrides", "increasingInjection", "getCurrentInstance", "inject", "zIndexInjection", "initialZIndex", "computed", "zIndexFromInjection", "unref", "isNumber", "currentZIndex", "value", "nextZIndex", "isClient", "debugWarn"], "sources": ["../../../../../packages/hooks/use-z-index/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref, unref } from 'vue'\nimport { debugWarn, isClient, isNumber } from '@element-plus/utils'\n\nimport type { InjectionKey, Ref } from 'vue'\n\nexport interface ElZIndexInjectionContext {\n  current: number\n}\n\nconst initial: ElZIndexInjectionContext = {\n  current: 0,\n}\n\nconst zIndex = ref(0)\n\nexport const defaultInitialZIndex = 2000\n\n// For SSR\nexport const ZINDEX_INJECTION_KEY: InjectionKey<ElZIndexInjectionContext> =\n  Symbol('elZIndexContextKey')\n\nexport const zIndexContextKey: InjectionKey<Ref<number | undefined>> =\n  Symbol('zIndexContextKey')\n\nexport const useZIndex = (zIndexOverrides?: Ref<number>) => {\n  const increasingInjection = getCurrentInstance()\n    ? inject(ZINDEX_INJECTION_KEY, initial)\n    : initial\n\n  const zIndexInjection =\n    zIndexOverrides ||\n    (getCurrentInstance() ? inject(zIndexContextKey, undefined) : undefined)\n\n  const initialZIndex = computed(() => {\n    const zIndexFromInjection = unref(zIndexInjection)\n    return isNumber(zIndexFromInjection)\n      ? zIndexFromInjection\n      : defaultInitialZIndex\n  })\n\n  const currentZIndex = computed(() => initialZIndex.value + zIndex.value)\n\n  const nextZIndex = () => {\n    increasingInjection.current++\n    zIndex.value = increasingInjection.current\n    return currentZIndex.value\n  }\n\n  if (!isClient && !inject(ZINDEX_INJECTION_KEY)) {\n    debugWarn(\n      'ZIndexInjection',\n      `Looks like you are using server rendering, you must provide a z-index provider to ensure the hydration process to be succeed\nusage: app.provide(ZINDEX_INJECTION_KEY, { current: 0 })`\n    )\n  }\n\n  return {\n    initialZIndex,\n    currentZIndex,\n    nextZIndex,\n  }\n}\n\nexport type UseZIndexReturn = ReturnType<typeof useZIndex>\n"], "mappings": ";;;;AAEA,MAAMA,OAAO,GAAG;EACdC,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,MAAM,GAAGC,GAAG,CAAC,CAAC,CAAC;AACT,MAACC,oBAAoB,GAAG;AACxB,MAACC,oBAAoB,GAAGC,MAAM,CAAC,oBAAoB;AACnD,MAACC,gBAAgB,GAAGD,MAAM,CAAC,kBAAkB;AAC7C,MAACE,SAAS,GAAIC,eAAe,IAAK;EAC5C,MAAMC,mBAAmB,GAAGC,kBAAkB,EAAE,GAAGC,MAAM,CAACP,oBAAoB,EAAEL,OAAO,CAAC,GAAGA,OAAO;EAClG,MAAMa,eAAe,GAAGJ,eAAe,KAAKE,kBAAkB,EAAE,GAAGC,MAAM,CAACL,gBAAgB,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;EAC7G,MAAMO,aAAa,GAAGC,QAAQ,CAAC,MAAM;IACnC,MAAMC,mBAAmB,GAAGC,KAAK,CAACJ,eAAe,CAAC;IAClD,OAAOK,QAAQ,CAACF,mBAAmB,CAAC,GAAGA,mBAAmB,GAAGZ,oBAAoB;EACrF,CAAG,CAAC;EACF,MAAMe,aAAa,GAAGJ,QAAQ,CAAC,MAAMD,aAAa,CAACM,KAAK,GAAGlB,MAAM,CAACkB,KAAK,CAAC;EACxE,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBX,mBAAmB,CAACT,OAAO,EAAE;IAC7BC,MAAM,CAACkB,KAAK,GAAGV,mBAAmB,CAACT,OAAO;IAC1C,OAAOkB,aAAa,CAACC,KAAK;EAC9B,CAAG;EACD,IAAI,CAACE,QAAQ,IAAI,CAACV,MAAM,CAACP,oBAAoB,CAAC,EAAE;IAC9CkB,SAAS,CAAC,iBAAiB,EAAE;AACjC,yDAAyD,CAAC;EAC1D;EACE,OAAO;IACLT,aAAa;IACbK,aAAa;IACbE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
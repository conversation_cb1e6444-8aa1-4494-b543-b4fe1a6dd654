{"ast": null, "code": "import { createStore } from 'vuex';\nimport axios from 'axios';\n\n// 配置axios默认值\naxios.defaults.baseURL = 'http://localhost:3000';\nexport default createStore({\n  state: {\n    user: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : null,\n    token: localStorage.getItem('token') || '',\n    role: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).role : ''\n  },\n  getters: {\n    isAuthenticated: state => !!state.token,\n    isAdmin: state => state.role == 'admin',\n    isTeacher: state => state.role == 'teacher',\n    isStudent: state => state.role == 'student',\n    userRole: state => state.role,\n    currentUser: state => state.user\n  },\n  mutations: {\n    SET_TOKEN(state, token) {\n      state.token = token;\n    },\n    SET_USER(state, user) {\n      state.user = user;\n    },\n    SET_ROLE(state, role) {\n      state.role = role;\n    },\n    LOGOUT(state) {\n      state.token = '';\n      state.user = null;\n      state.role = '';\n    }\n  },\n  actions: {\n    async login({\n      commit\n    }, credentials) {\n      try {\n        const response = await axios.post('/api/auth/login', credentials);\n        const {\n          token,\n          user\n        } = response.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('userId', user.id);\n        localStorage.setItem('userRole', user.role);\n        commit('SET_TOKEN', token);\n        commit('SET_USER', user);\n        commit('SET_ROLE', user.role);\n\n        // 设置axios请求头\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        return response;\n      } catch (error) {\n        throw error;\n      }\n    },\n    logout({\n      commit\n    }) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('userId');\n      localStorage.removeItem('userRole');\n      commit('LOGOUT');\n\n      // 清除axios请求头\n      delete axios.defaults.headers.common['Authorization'];\n    },\n    async fetchUserProfile({\n      commit\n    }) {\n      try {\n        const token = localStorage.getItem('token');\n        if (!token) return;\n\n        // 设置请求头\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        const response = await axios.get('/api/auth/me');\n        const {\n          user\n        } = response.data;\n        commit('SET_USER', user);\n        commit('SET_ROLE', user.role);\n        localStorage.setItem('userRole', user.role);\n        return response;\n      } catch (error) {\n        commit('LOGOUT');\n        throw error;\n      }\n    }\n  },\n  modules: {}\n});", "map": {"version": 3, "names": ["createStore", "axios", "defaults", "baseURL", "state", "user", "localStorage", "getItem", "JSON", "parse", "token", "role", "getters", "isAuthenticated", "isAdmin", "<PERSON><PERSON><PERSON>er", "isStudent", "userRole", "currentUser", "mutations", "SET_TOKEN", "SET_USER", "SET_ROLE", "LOGOUT", "actions", "login", "commit", "credentials", "response", "post", "data", "setItem", "id", "headers", "common", "error", "logout", "removeItem", "fetchUserProfile", "get", "modules"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\nimport axios from 'axios'\n\n// 配置axios默认值\naxios.defaults.baseURL = 'http://localhost:3000'\n\nexport default createStore({\n  state: {\n    user: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : null,\n    token: localStorage.getItem('token') || '',\n    role: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).role : ''\n  },\n  getters: {\n    isAuthenticated: state => !!state.token,\n    isAdmin: state => state.role == 'admin',\n    isTeacher: state => state.role == 'teacher',\n    isStudent: state => state.role == 'student',\n    userRole: state => state.role,\n    currentUser: state => state.user\n  },\n  mutations: {\n    SET_TOKEN(state, token) {\n      state.token = token\n    },\n    SET_USER(state, user) {\n      state.user = user\n    },\n    SET_ROLE(state, role) {\n      state.role = role\n    },\n    LOGOUT(state) {\n      state.token = ''\n      state.user = null\n      state.role = ''\n    }\n  },\n  actions: {\n    async login({ commit }, credentials) {\n      try {\n        const response = await axios.post('/api/auth/login', credentials)\n        const { token, user } = response.data\n        \n        localStorage.setItem('token', token)\n        localStorage.setItem('userId', user.id)\n        localStorage.setItem('userRole', user.role)\n        \n        commit('SET_TOKEN', token)\n        commit('SET_USER', user)\n        commit('SET_ROLE', user.role)\n        \n        // 设置axios请求头\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\n        \n        return response\n      } catch (error) {\n        throw error\n      }\n    },\n    \n    logout({ commit }) {\n      localStorage.removeItem('token')\n      localStorage.removeItem('userId')\n      localStorage.removeItem('userRole')\n      \n      commit('LOGOUT')\n      \n      // 清除axios请求头\n      delete axios.defaults.headers.common['Authorization']\n    },\n    \n    async fetchUserProfile({ commit }) {\n      try {\n        const token = localStorage.getItem('token')\n        if (!token) return\n        \n        // 设置请求头\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\n        \n        const response = await axios.get('/api/auth/me')\n        const { user } = response.data\n        \n        commit('SET_USER', user)\n        commit('SET_ROLE', user.role)\n        localStorage.setItem('userRole', user.role)\n        \n        return response\n      } catch (error) {\n        commit('LOGOUT')\n        throw error\n      }\n    }\n  },\n  modules: {\n  }\n})\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAClC,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACAA,KAAK,CAACC,QAAQ,CAACC,OAAO,GAAG,uBAAuB;AAEhD,eAAeH,WAAW,CAAC;EACzBI,KAAK,EAAE;IACLC,IAAI,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;IAC5FG,KAAK,EAAEJ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;IAC1CI,IAAI,EAAEL,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAACI,IAAI,GAAG;EAC/F,CAAC;EACDC,OAAO,EAAE;IACPC,eAAe,EAAET,KAAK,IAAI,CAAC,CAACA,KAAK,CAACM,KAAK;IACvCI,OAAO,EAAEV,KAAK,IAAIA,KAAK,CAACO,IAAI,IAAI,OAAO;IACvCI,SAAS,EAAEX,KAAK,IAAIA,KAAK,CAACO,IAAI,IAAI,SAAS;IAC3CK,SAAS,EAAEZ,KAAK,IAAIA,KAAK,CAACO,IAAI,IAAI,SAAS;IAC3CM,QAAQ,EAAEb,KAAK,IAAIA,KAAK,CAACO,IAAI;IAC7BO,WAAW,EAAEd,KAAK,IAAIA,KAAK,CAACC;EAC9B,CAAC;EACDc,SAAS,EAAE;IACTC,SAASA,CAAChB,KAAK,EAAEM,KAAK,EAAE;MACtBN,KAAK,CAACM,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDW,QAAQA,CAACjB,KAAK,EAAEC,IAAI,EAAE;MACpBD,KAAK,CAACC,IAAI,GAAGA,IAAI;IACnB,CAAC;IACDiB,QAAQA,CAAClB,KAAK,EAAEO,IAAI,EAAE;MACpBP,KAAK,CAACO,IAAI,GAAGA,IAAI;IACnB,CAAC;IACDY,MAAMA,CAACnB,KAAK,EAAE;MACZA,KAAK,CAACM,KAAK,GAAG,EAAE;MAChBN,KAAK,CAACC,IAAI,GAAG,IAAI;MACjBD,KAAK,CAACO,IAAI,GAAG,EAAE;IACjB;EACF,CAAC;EACDa,OAAO,EAAE;IACP,MAAMC,KAAKA,CAAC;MAAEC;IAAO,CAAC,EAAEC,WAAW,EAAE;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,IAAI,CAAC,iBAAiB,EAAEF,WAAW,CAAC;QACjE,MAAM;UAAEjB,KAAK;UAAEL;QAAK,CAAC,GAAGuB,QAAQ,CAACE,IAAI;QAErCxB,YAAY,CAACyB,OAAO,CAAC,OAAO,EAAErB,KAAK,CAAC;QACpCJ,YAAY,CAACyB,OAAO,CAAC,QAAQ,EAAE1B,IAAI,CAAC2B,EAAE,CAAC;QACvC1B,YAAY,CAACyB,OAAO,CAAC,UAAU,EAAE1B,IAAI,CAACM,IAAI,CAAC;QAE3Ce,MAAM,CAAC,WAAW,EAAEhB,KAAK,CAAC;QAC1BgB,MAAM,CAAC,UAAU,EAAErB,IAAI,CAAC;QACxBqB,MAAM,CAAC,UAAU,EAAErB,IAAI,CAACM,IAAI,CAAC;;QAE7B;QACAV,KAAK,CAACC,QAAQ,CAAC+B,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUxB,KAAK,EAAE;QAElE,OAAOkB,QAAQ;MACjB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IACF,CAAC;IAEDC,MAAMA,CAAC;MAAEV;IAAO,CAAC,EAAE;MACjBpB,YAAY,CAAC+B,UAAU,CAAC,OAAO,CAAC;MAChC/B,YAAY,CAAC+B,UAAU,CAAC,QAAQ,CAAC;MACjC/B,YAAY,CAAC+B,UAAU,CAAC,UAAU,CAAC;MAEnCX,MAAM,CAAC,QAAQ,CAAC;;MAEhB;MACA,OAAOzB,KAAK,CAACC,QAAQ,CAAC+B,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD,CAAC;IAED,MAAMI,gBAAgBA,CAAC;MAAEZ;IAAO,CAAC,EAAE;MACjC,IAAI;QACF,MAAMhB,KAAK,GAAGJ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACG,KAAK,EAAE;;QAEZ;QACAT,KAAK,CAACC,QAAQ,CAAC+B,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUxB,KAAK,EAAE;QAElE,MAAMkB,QAAQ,GAAG,MAAM3B,KAAK,CAACsC,GAAG,CAAC,cAAc,CAAC;QAChD,MAAM;UAAElC;QAAK,CAAC,GAAGuB,QAAQ,CAACE,IAAI;QAE9BJ,MAAM,CAAC,UAAU,EAAErB,IAAI,CAAC;QACxBqB,MAAM,CAAC,UAAU,EAAErB,IAAI,CAACM,IAAI,CAAC;QAC7BL,YAAY,CAACyB,OAAO,CAAC,UAAU,EAAE1B,IAAI,CAACM,IAAI,CAAC;QAE3C,OAAOiB,QAAQ;MACjB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdT,MAAM,CAAC,QAAQ,CAAC;QAChB,MAAMS,KAAK;MACb;IACF;EACF,CAAC;EACDK,OAAO,EAAE,CACT;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/web.dom-exception.stack.js\";\nimport { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport api from './utils/api';\nimport axios from 'axios';\n\n// Import Element Plus\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\n// Import icons\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\n\n// 配置全局axios默认值\naxios.defaults.baseURL = 'http://localhost:3000';\n\n// 初始化检查token\nconst token = localStorage.getItem('token');\nif (token) {\n  // 检查token是否有效\n  try {\n    // JWT格式: header.payload.signature\n    const payloadBase64 = token.split('.')[1];\n    if (!payloadBase64) {\n      // 格式不正确，清除token\n      localStorage.removeItem('token');\n      localStorage.removeItem('userId');\n      localStorage.removeItem('userRole');\n      localStorage.removeItem('studentId');\n    } else {\n      const decodedPayload = JSON.parse(atob(payloadBase64));\n      const expiration = decodedPayload.exp * 1000; // 转换为毫秒\n\n      // 检查是否过期\n      if (Date.now() >= expiration) {\n        // token已过期，清除相关信息\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('studentId');\n\n        // 如果后端支持刷新token，可以尝试刷新\n        // api.refreshToken()\n      }\n    }\n  } catch (error) {\n    console.error('解析token失败:', error);\n    // 解析错误，清除token\n    localStorage.removeItem('token');\n    localStorage.removeItem('userId');\n    localStorage.removeItem('userRole');\n    localStorage.removeItem('studentId');\n  }\n}\nconst app = createApp(App);\n\n// 全局API实例\napp.config.globalProperties.$api = api;\napp.config.globalProperties.$axios = axios;\n\n// Register all icons globally\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\napp.use(store).use(router).use(ElementPlus).mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "api", "axios", "ElementPlus", "ElementPlusIconsVue", "defaults", "baseURL", "token", "localStorage", "getItem", "payloadBase64", "split", "removeItem", "decodedPayload", "JSON", "parse", "atob", "expiration", "exp", "Date", "now", "error", "console", "app", "config", "globalProperties", "$api", "$axios", "key", "component", "Object", "entries", "use", "mount"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport api from './utils/api'\r\nimport axios from 'axios'\r\n\r\n// Import Element Plus\r\nimport ElementPlus from 'element-plus'\r\nimport 'element-plus/dist/index.css'\r\n// Import icons\r\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\r\n\r\n// 配置全局axios默认值\r\naxios.defaults.baseURL = 'http://localhost:3000'\r\n\r\n// 初始化检查token\r\nconst token = localStorage.getItem('token')\r\nif (token) {\r\n  // 检查token是否有效\r\n  try {\r\n    // JWT格式: header.payload.signature\r\n    const payloadBase64 = token.split('.')[1]\r\n    if (!payloadBase64) {\r\n      // 格式不正确，清除token\r\n      localStorage.removeItem('token')\r\n      localStorage.removeItem('userId')\r\n      localStorage.removeItem('userRole')\r\n      localStorage.removeItem('studentId')\r\n    } else {\r\n      const decodedPayload = JSON.parse(atob(payloadBase64))\r\n      const expiration = decodedPayload.exp * 1000 // 转换为毫秒\r\n\r\n      // 检查是否过期\r\n      if (Date.now() >= expiration) {\r\n        // token已过期，清除相关信息\r\n        localStorage.removeItem('token')\r\n        localStorage.removeItem('userId')\r\n        localStorage.removeItem('userRole')\r\n        localStorage.removeItem('studentId')\r\n        \r\n        // 如果后端支持刷新token，可以尝试刷新\r\n        // api.refreshToken()\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('解析token失败:', error)\r\n    // 解析错误，清除token\r\n    localStorage.removeItem('token')\r\n    localStorage.removeItem('userId')\r\n    localStorage.removeItem('userRole')\r\n    localStorage.removeItem('studentId')\r\n  }\r\n}\r\n\r\nconst app = createApp(App)\r\n\r\n// 全局API实例\r\napp.config.globalProperties.$api = api\r\napp.config.globalProperties.$axios = axios\r\n\r\n// Register all icons globally\r\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\r\n  app.component(key, component)\r\n}\r\n\r\napp.use(store).use(router).use(ElementPlus).mount('#app')\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;AACpC;AACA,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;;AAE9D;AACAF,KAAK,CAACG,QAAQ,CAACC,OAAO,GAAG,uBAAuB;;AAEhD;AACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AAC3C,IAAIF,KAAK,EAAE;EACT;EACA,IAAI;IACF;IACA,MAAMG,aAAa,GAAGH,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI,CAACD,aAAa,EAAE;MAClB;MACAF,YAAY,CAACI,UAAU,CAAC,OAAO,CAAC;MAChCJ,YAAY,CAACI,UAAU,CAAC,QAAQ,CAAC;MACjCJ,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;MACnCJ,YAAY,CAACI,UAAU,CAAC,WAAW,CAAC;IACtC,CAAC,MAAM;MACL,MAAMC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACN,aAAa,CAAC,CAAC;MACtD,MAAMO,UAAU,GAAGJ,cAAc,CAACK,GAAG,GAAG,IAAI,EAAC;;MAE7C;MACA,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIH,UAAU,EAAE;QAC5B;QACAT,YAAY,CAACI,UAAU,CAAC,OAAO,CAAC;QAChCJ,YAAY,CAACI,UAAU,CAAC,QAAQ,CAAC;QACjCJ,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;QACnCJ,YAAY,CAACI,UAAU,CAAC,WAAW,CAAC;;QAEpC;QACA;MACF;IACF;EACF,CAAC,CAAC,OAAOS,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC;IACAb,YAAY,CAACI,UAAU,CAAC,OAAO,CAAC;IAChCJ,YAAY,CAACI,UAAU,CAAC,QAAQ,CAAC;IACjCJ,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;IACnCJ,YAAY,CAACI,UAAU,CAAC,WAAW,CAAC;EACtC;AACF;AAEA,MAAMW,GAAG,GAAG1B,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACAyB,GAAG,CAACC,MAAM,CAACC,gBAAgB,CAACC,IAAI,GAAGzB,GAAG;AACtCsB,GAAG,CAACC,MAAM,CAACC,gBAAgB,CAACE,MAAM,GAAGzB,KAAK;;AAE1C;AACA,KAAK,MAAM,CAAC0B,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC3B,mBAAmB,CAAC,EAAE;EAClEmB,GAAG,CAACM,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;AAEAN,GAAG,CAACS,GAAG,CAAChC,KAAK,CAAC,CAACgC,GAAG,CAACjC,MAAM,CAAC,CAACiC,GAAG,CAAC7B,WAAW,CAAC,CAAC8B,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
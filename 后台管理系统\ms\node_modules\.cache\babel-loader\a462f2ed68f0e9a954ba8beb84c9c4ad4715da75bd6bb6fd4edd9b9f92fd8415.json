{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, ref, reactive, computed, watch, provide, toRefs, openBlock, createElementBlock, normalizeClass, unref, renderSlot } from 'vue';\nimport { formContextKey } from './constants.mjs';\nimport { formProps, formEmits } from './form.mjs';\nimport { useFormLabelWidth, filterFields } from './utils.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from './hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { isFunction } from '@vue/shared';\nconst COMPONENT_NAME = \"ElForm\";\nconst __default__ = defineComponent({\n  name: COMPONENT_NAME\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: formProps,\n  emits: formEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const formRef = ref();\n    const fields = reactive([]);\n    const formSize = useFormSize();\n    const ns = useNamespace(\"form\");\n    const formClasses = computed(() => {\n      const {\n        labelPosition,\n        inline\n      } = props;\n      return [ns.b(), ns.m(formSize.value || \"default\"), {\n        [ns.m(`label-${labelPosition}`)]: labelPosition,\n        [ns.m(\"inline\")]: inline\n      }];\n    });\n    const getField = prop => {\n      return filterFields(fields, [prop])[0];\n    };\n    const addField = field => {\n      fields.push(field);\n    };\n    const removeField = field => {\n      if (field.prop) {\n        fields.splice(fields.indexOf(field), 1);\n      }\n    };\n    const resetFields = (properties = []) => {\n      if (!props.model) {\n        debugWarn(COMPONENT_NAME, \"model is required for resetFields to work.\");\n        return;\n      }\n      filterFields(fields, properties).forEach(field => field.resetField());\n    };\n    const clearValidate = (props2 = []) => {\n      filterFields(fields, props2).forEach(field => field.clearValidate());\n    };\n    const isValidatable = computed(() => {\n      const hasModel = !!props.model;\n      if (!hasModel) {\n        debugWarn(COMPONENT_NAME, \"model is required for validate to work.\");\n      }\n      return hasModel;\n    });\n    const obtainValidateFields = props2 => {\n      if (fields.length === 0) return [];\n      const filteredFields = filterFields(fields, props2);\n      if (!filteredFields.length) {\n        debugWarn(COMPONENT_NAME, \"please pass correct props!\");\n        return [];\n      }\n      return filteredFields;\n    };\n    const validate = async callback => validateField(void 0, callback);\n    const doValidateField = async (props2 = []) => {\n      if (!isValidatable.value) return false;\n      const fields2 = obtainValidateFields(props2);\n      if (fields2.length === 0) return true;\n      let validationErrors = {};\n      for (const field of fields2) {\n        try {\n          await field.validate(\"\");\n          if (field.validateState === \"error\") field.resetField();\n        } catch (fields3) {\n          validationErrors = {\n            ...validationErrors,\n            ...fields3\n          };\n        }\n      }\n      if (Object.keys(validationErrors).length === 0) return true;\n      return Promise.reject(validationErrors);\n    };\n    const validateField = async (modelProps = [], callback) => {\n      let result = false;\n      const shouldThrow = !isFunction(callback);\n      try {\n        result = await doValidateField(modelProps);\n        if (result === true) {\n          await (callback == null ? void 0 : callback(result));\n        }\n        return result;\n      } catch (e) {\n        if (e instanceof Error) throw e;\n        const invalidFields = e;\n        if (props.scrollToError) {\n          if (formRef.value) {\n            const formItem = formRef.value.querySelector(`.${ns.b()}-item.is-error`);\n            formItem == null ? void 0 : formItem.scrollIntoView(props.scrollIntoViewOptions);\n          }\n        }\n        !result && (await (callback == null ? void 0 : callback(false, invalidFields)));\n        return shouldThrow && Promise.reject(invalidFields);\n      }\n    };\n    const scrollToField = prop => {\n      var _a;\n      const field = getField(prop);\n      if (field) {\n        (_a = field.$el) == null ? void 0 : _a.scrollIntoView(props.scrollIntoViewOptions);\n      }\n    };\n    watch(() => props.rules, () => {\n      if (props.validateOnRuleChange) {\n        validate().catch(err => debugWarn(err));\n      }\n    }, {\n      deep: true,\n      flush: \"post\"\n    });\n    provide(formContextKey, reactive({\n      ...toRefs(props),\n      emit,\n      resetFields,\n      clearValidate,\n      validateField,\n      getField,\n      addField,\n      removeField,\n      ...useFormLabelWidth()\n    }));\n    expose({\n      validate,\n      validateField,\n      resetFields,\n      clearValidate,\n      scrollToField,\n      getField,\n      fields\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"form\", {\n        ref_key: \"formRef\",\n        ref: formRef,\n        class: normalizeClass(unref(formClasses))\n      }, [renderSlot(_ctx.$slots, \"default\")], 2);\n    };\n  }\n});\nvar Form = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"form.vue\"]]);\nexport { Form as default };", "map": {"version": 3, "names": ["name", "COMPONENT_NAME", "formRef", "ref", "fields", "reactive", "formSize", "useFormSize", "ns", "useNamespace", "formClasses", "computed", "labelPosition", "inline", "props", "b", "m", "value", "getField", "prop", "filterFields", "addField", "field", "push", "removeField", "splice", "indexOf", "resetFields", "properties", "model", "debugWarn", "for<PERSON>ach", "reset<PERSON>ield", "clearValidate", "props2", "isValidatable", "hasModel", "obtainValidateFields", "length", "filteredFields", "validate", "callback", "validateField", "doValidateField", "fields2", "validationErrors", "validateState", "fields3", "Object", "keys", "Promise", "reject", "modelProps", "result", "shouldThrow", "isFunction", "e", "Error", "invalidFields", "scrollToError", "formItem", "querySelector", "scrollIntoView", "scrollIntoViewOptions", "scrollToField", "_a", "$el", "watch", "rules", "validateOnRuleChange", "catch", "err", "deep", "flush", "provide", "formContextKey", "toRefs", "emit", "useFormLabel<PERSON>th", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "ref_key", "class", "normalizeClass", "unref", "renderSlot", "$slots", "Form", "_export_sfc", "_sfc_main"], "sources": ["../../../../../../packages/components/form/src/form.vue"], "sourcesContent": ["<template>\n  <form ref=\"formRef\" :class=\"formClasses\">\n    <slot />\n  </form>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, reactive, ref, toRefs, watch } from 'vue'\nimport { debugWarn, isFunction } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from './hooks'\nimport { formContextKey } from './constants'\nimport { formEmits, formProps } from './form'\nimport { filterFields, useFormLabelWidth } from './utils'\n\nimport type { ValidateFieldsError } from 'async-validator'\nimport type { Arrayable } from '@element-plus/utils'\nimport type {\n  FormContext,\n  FormItemContext,\n  FormValidateCallback,\n  FormValidationResult,\n} from './types'\nimport type { FormItemProp } from './form-item'\n\nconst COMPONENT_NAME = 'ElForm'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(formProps)\nconst emit = defineEmits(formEmits)\n\nconst formRef = ref<HTMLElement>()\nconst fields = reactive<FormItemContext[]>([])\n\nconst formSize = useFormSize()\nconst ns = useNamespace('form')\nconst formClasses = computed(() => {\n  const { labelPosition, inline } = props\n  return [\n    ns.b(),\n    ns.m(formSize.value || 'default'),\n    {\n      [ns.m(`label-${labelPosition}`)]: labelPosition,\n      [ns.m('inline')]: inline,\n    },\n  ]\n})\n\nconst getField: FormContext['getField'] = (prop) => {\n  return filterFields(fields, [prop])[0]\n}\n\nconst addField: FormContext['addField'] = (field) => {\n  fields.push(field)\n}\n\nconst removeField: FormContext['removeField'] = (field) => {\n  if (field.prop) {\n    fields.splice(fields.indexOf(field), 1)\n  }\n}\n\nconst resetFields: FormContext['resetFields'] = (properties = []) => {\n  if (!props.model) {\n    debugWarn(COMPONENT_NAME, 'model is required for resetFields to work.')\n    return\n  }\n  filterFields(fields, properties).forEach((field) => field.resetField())\n}\n\nconst clearValidate: FormContext['clearValidate'] = (props = []) => {\n  filterFields(fields, props).forEach((field) => field.clearValidate())\n}\n\nconst isValidatable = computed(() => {\n  const hasModel = !!props.model\n  if (!hasModel) {\n    debugWarn(COMPONENT_NAME, 'model is required for validate to work.')\n  }\n  return hasModel\n})\n\nconst obtainValidateFields = (props: Arrayable<FormItemProp>) => {\n  if (fields.length === 0) return []\n\n  const filteredFields = filterFields(fields, props)\n  if (!filteredFields.length) {\n    debugWarn(COMPONENT_NAME, 'please pass correct props!')\n    return []\n  }\n  return filteredFields\n}\n\nconst validate = async (\n  callback?: FormValidateCallback\n): FormValidationResult => validateField(undefined, callback)\n\nconst doValidateField = async (\n  props: Arrayable<FormItemProp> = []\n): Promise<boolean> => {\n  if (!isValidatable.value) return false\n\n  const fields = obtainValidateFields(props)\n  if (fields.length === 0) return true\n\n  let validationErrors: ValidateFieldsError = {}\n  for (const field of fields) {\n    try {\n      await field.validate('')\n      if (field.validateState === 'error') field.resetField()\n    } catch (fields) {\n      validationErrors = {\n        ...validationErrors,\n        ...(fields as ValidateFieldsError),\n      }\n    }\n  }\n\n  if (Object.keys(validationErrors).length === 0) return true\n  return Promise.reject(validationErrors)\n}\n\nconst validateField: FormContext['validateField'] = async (\n  modelProps = [],\n  callback\n) => {\n  let result = false\n  const shouldThrow = !isFunction(callback)\n  try {\n    result = await doValidateField(modelProps)\n    // When result is false meaning that the fields are not validatable\n    if (result === true) {\n      await callback?.(result)\n    }\n    return result\n  } catch (e) {\n    if (e instanceof Error) throw e\n\n    const invalidFields = e as ValidateFieldsError\n\n    if (props.scrollToError) {\n      // form-item may be dynamically rendered based on the judgment conditions, and the order in invalidFields is uncertain.\n      // Therefore, the first form field with an error is determined by directly looking for the rendered element.\n      if (formRef.value) {\n        const formItem = formRef.value!.querySelector(\n          `.${ns.b()}-item.is-error`\n        )\n        formItem?.scrollIntoView(props.scrollIntoViewOptions)\n      }\n    }\n    !result && (await callback?.(false, invalidFields))\n    return shouldThrow && Promise.reject(invalidFields)\n  }\n}\n\nconst scrollToField = (prop: FormItemProp) => {\n  const field = getField(prop)\n  if (field) {\n    field.$el?.scrollIntoView(props.scrollIntoViewOptions)\n  }\n}\n\nwatch(\n  () => props.rules,\n  () => {\n    if (props.validateOnRuleChange) {\n      validate().catch((err) => debugWarn(err))\n    }\n  },\n  { deep: true, flush: 'post' }\n)\n\nprovide(\n  formContextKey,\n  reactive({\n    ...toRefs(props),\n    emit,\n\n    resetFields,\n    clearValidate,\n    validateField,\n    getField,\n    addField,\n    removeField,\n\n    ...useFormLabelWidth(),\n  })\n)\n\ndefineExpose({\n  /**\n   * @description Validate the whole form. Receives a callback or returns `Promise`.\n   */\n  validate,\n  /**\n   * @description Validate specified fields.\n   */\n  validateField,\n  /**\n   * @description Reset specified fields and remove validation result.\n   */\n  resetFields,\n  /**\n   * @description Clear validation message for specified fields.\n   */\n  clearValidate,\n  /**\n   * @description Scroll to the specified fields.\n   */\n  scrollToField,\n  /**\n   * @description Get a field context.\n   */\n  getField,\n  /**\n   * @description All fields context.\n   */\n  fields,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;mCA0Bc;EACZA,IAAM,EAAAC;AACR;;;;;;;;;;IAIA,MAAMC,OAAA,GAAUC,GAAiB;IAC3B,MAAAC,MAAA,GAASC,QAA4B,GAAE;IAE7C,MAAMC,QAAA,GAAWC,WAAY;IACvB,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IACxB,MAAAC,WAAA,GAAcC,QAAA,CAAS,MAAM;MAC3B;QAAEC,aAAe;QAAAC;MAAA,CAAW,GAAAC,KAAA;MAC3B,QACLN,EAAA,CAAGO,CAAE,IACLP,EAAG,CAAAQ,CAAA,CAAEV,QAAS,CAAAW,KAAA,IAAS,SAAS,GAChC;QACE,CAACT,EAAG,CAAAQ,CAAA,CAAE,SAASJ,aAAa,EAAE,CAAC,GAAGA,aAAA;QAClC,CAACJ,EAAA,CAAGQ,CAAE,SAAQ,CAAC,GAAGH;MAAA,CACpB,CACF;IAAA,CACD;IAEK,MAAAK,QAAA,GAAqCC,IAAS;MAClD,OAAOC,YAAA,CAAahB,MAAQ,GAACe,IAAI,CAAC,EAAE,CAAC;IAAA,CACvC;IAEM,MAAAE,QAAA,GAAqCC,KAAU;MACnDlB,MAAA,CAAOmB,IAAA,CAAKD,KAAK;IAAA,CACnB;IAEM,MAAAE,WAAA,GAA2CF,KAAU;MACzD,IAAIA,KAAA,CAAMH,IAAM;QACdf,MAAA,CAAOqB,MAAO,CAAArB,MAAA,CAAOsB,OAAQ,CAAAJ,KAAK,GAAG,CAAC;MAAA;IACxC,CACF;IAEA,MAAMK,WAA0C,GAAAA,CAACC,UAAa,KAAO;MAC/D,KAACd,KAAA,CAAMe,KAAO;QAChBC,SAAA,CAAU7B,cAAA,EAAgB,4CAA4C;QACtE;MAAA;MAEWmB,YAAA,CAAAhB,MAAA,EAAQwB,UAAU,CAAE,CAAAG,OAAA,CAAST,KAAU,IAAAA,KAAA,CAAMU,UAAA,EAAY;IAAA,CACxE;IAEA,MAAMC,aAA8C,GAAAA,CAACC,MAAQ,KAAO;MACrDd,YAAA,CAAAhB,MAAA,EAAQ8B,MAAK,CAAE,CAAAH,OAAA,CAAST,KAAU,IAAAA,KAAA,CAAMW,aAAA,EAAe;IAAA,CACtE;IAEM,MAAAE,aAAA,GAAgBxB,QAAA,CAAS,MAAM;MAC7B,MAAAyB,QAAA,GAAW,CAAC,CAACtB,KAAM,CAAAe,KAAA;MACzB,IAAI,CAACO,QAAU;QACbN,SAAA,CAAU7B,cAAA,EAAgB,yCAAyC;MAAA;MAE9D,OAAAmC,QAAA;IAAA,CACR;IAEK,MAAAC,oBAAA,GAAwBH,MAAmC;MAC/D,IAAI9B,MAAO,CAAAkC,MAAA,KAAW,CAAG,EAEnB;MACF,MAAAC,cAAgB,GAAQnB,YAAA,CAAAhB,MAAA,EAAA8B,MAAA;MAC1B,KAAAK,cAAA,CAAAD,MAAA,EAA0B;QAC1BR,SAAQ,CAAA7B,cAAA;QACV;MACA;MACF,OAAAsC,cAAA;IAEA;IAIA,MAAMC,QAAkB,SAAAC,QACW,IAAAC,aACZ,SAAAD,QAAA;IACjB,MAAAE,eAAe,SAAAA,CAAcT,MAAA;MAE3B,KAAAC,aAAA,CAAAlB,KAAA,EACF,YAAkB;MAEtB,MAAA2B,OAAA,GAAAP,oBAA6C,CAAAH,MAAA;MAC7C,IAAAU,OAAW,CAAAN,MAAA,KAAiB,GACtB;MACI,IAAAO,gBAAA,KAAe;MACrB,WAAUvB,KAAA,IAAAsB,OAAA;QACZ;UACqB,MAAAtB,KAAA,CAAAkB,QAAA;UAAA,IACdlB,KAAA,CAAAwB,aAAA,cACHxB,KAAI,CAAAU,UAAA;QAAA,CACN,QAAAe,OAAA;UACFF,gBAAA;YACF,GAAAA,gBAAA;YAEA,GAAAE;UACA,CAAO;QAA+B;MAGxC;MAIE,IAAIC,MAAS,CAAAC,IAAA,CAAAJ,gBAAA,EAAAP,MAAA,QACP;MACF,OAAAY,OAAA,CAAAC,MAAA,CAAAN,gBAAA;IACF,CAAS;IAET,MAAAH,aAAe,GAAM,MAAAA,CAAAU,UAAA,OAAAX,QAAA;MACnB,IAAAY,MAAM;MACR,MAAAC,WAAA,IAAAC,UAAA,CAAAd,QAAA;MACO;QAAAY,MAAA,GACG,MAAAV,eAAA,CAAAS,UAAA;QACN,IAAAC,MAAA;UAEJ,OAAsBZ,QAAA,oBAAAA,QAAA,CAAAY,MAAA;QAEtB;QAGE,OAAAA,MAAA;MACE,CAAM,QAAAG,CAAA;QACJ,IAAAA,CAAA,YAAUC,KAAA,EACZ,MAAAD,CAAA;QACU,MAAAE,aAAA,GAAAF,CAAA;QACZ,IAAA1C,KAAA,CAAA6C,aAAA;UACF,IAAAzD,OAAA,CAAAe,KAAA;YACY,MAAA2C,QAAiB,GAAA1D,OAAA,CAAAe,KAAA,CAAO4C,aAAa,KAAArD,EAAA,CAAAO,CAAA;YAC1C6C,QAAA,WAAuB,SAAAA,QAAoB,CAAAE,cAAA,CAAAhD,KAAA,CAAAiD,qBAAA;UAAA;QACpD;QAGI,CAAAV,MAAA,YAAgBZ,QAAwB,oBAAAA,QAAA,QAAAiB,aAAA;QACtC,OAAAJ,WAAA,IAAqBJ,OAAA,CAAAC,MAAA,CAAAO,aAAA;MAC3B;IACE,CAAM;IACR,MAAAM,aAAA,GAAA7C,IAAA;MACF,IAAA8C,EAAA;MAEA,MAAA3C,KAAA,GAAAJ,QAAA,CAAAC,IAAA;MACE,IAAAG,KAAY;QACN,CAAA2C,EAAA,GAAA3C,KAAA,CAAA4C,GAAA,qBAAAD,EAAA,CAAAH,cAAA,CAAAhD,KAAA,CAAAiD,qBAAA;MACJ;IACE;IACFI,KAAA,OAAArD,KAAA,CAAAsD,KAAA;MACF,IAAAtD,KAAA,CAAAuD,oBAAA;QACE7B,QAAY,GAAA8B,KAAA,CAAAC,GAAc,IAAAzC,SAAA,CAAAyC,GAAA;MAAA;IAG9B;MAAAC,IAAA;MAAAC,KAAA;IAAA;IACEC,OAAA,CAAAC,cAAA,EAAAtE,QAAA;MACA,GAASuE,MAAA,CAAA9D,KAAA;MACP+D,IAAA;MACAlD,WAAA;MAEAM,aAAA;MACAS,aAAA;MACAxB,QAAA;MACAG,QAAA;MACAG,WAAA;MACA,GAAAsD,iBAAA;IAAA;IAEqBC,MACtB;MACHvC,QAAA;MAEaE,aAAA;MAAAf,WAAA;MAAAM,aAAA;MAAA+B,aAAA;MAIX9C,QAAA;MAAAd;IAAA;IAAA,QAAA4E,IAAA,EAAAC,MAAA;MAIA,OAAAC,SAAA,IAAAC,kBAAA;QAAAC,OAAA;QAAAjF,GAAA,EAAAD,OAAA;QAAAmF,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA7E,WAAA;MAAA,CAIA,GAAA8E,UAAA,CAAAR,IAAA,CAAAS,MAAA;IAAA;EAAA;AAIA;AAAA,IAAAC,IAAA,kBAAAC,WAAA,CAAAC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
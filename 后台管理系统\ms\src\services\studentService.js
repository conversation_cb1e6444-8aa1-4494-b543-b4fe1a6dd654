import api from '@/utils/api';

const studentService = {
  /**
   * 获取学生列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getStudents(params) {
    return api.get('/api/students', { params });
  },

  /**
   * 获取单个学生
   * @param {number} id - 学生ID
   * @returns {Promise}
   */
  getStudent(id) {
    return api.get(`/api/students/${id}`);
  },

  /**
   * 搜索学生
   * @param {string} name - 姓名
   * @param {string} school - 学校
   * @returns {Promise}
   */
  searchStudents(name, school) {
    return api.get('/api/students/search', { params: { name, school } });
  },

  /**
   * 批量导入学生
   * @param {FormData} formData - 包含Excel文件的表单数据
   * @returns {Promise}
   */
  importStudents(formData) {
    return api.post('/api/students/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

export default studentService;
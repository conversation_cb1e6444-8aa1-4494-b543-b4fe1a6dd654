{"version": 3, "file": "js/964.82ef6469.js", "mappings": "unBAuJAA,EAAAA,EAAMC,aAAaC,QAAQC,IACzBC,IACE,MAAMC,EAAQC,aAAaC,QAAQ,SAInC,OAHIF,IACFD,EAAOI,QAAQC,cAAgB,UAAUJ,KAEpCD,GAETM,GACSC,QAAQC,OAAOF,IAK1BV,EAAAA,EAAMC,aAAaY,SAASV,IAC1BU,GAAYA,EACZH,IACMA,EAAMG,UAAsC,MAA1BH,EAAMG,SAASC,SAEnCR,aAAaS,WAAW,SACxBT,aAAaS,WAAW,YAES,WAA7BC,OAAOC,SAASC,WAClBC,EAAAA,GAAUT,MAAM,eAChBM,OAAOC,SAASG,KAAO,aAGpBT,QAAQC,OAAOF,KAI1B,MAAMW,GAASC,EAAAA,EAAAA,MACTC,GAAeC,EAAAA,EAAAA,IAAI,MACnBC,GAAUD,EAAAA,EAAAA,KAAI,GACdE,EAAUC,CAAAA,SAAAA,aAAAA,SAAAA,KAAYC,iBAAmB,8BAGzCC,GAAYC,EAAAA,EAAAA,IAAS,CACzBC,SAAU,GACVC,SAAU,GACVC,UAAU,IAGNC,EAAa,CACjBH,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzDL,SAAU,CACR,CAAEG,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,UAIrDG,EAAcC,UAClB,GAAKlB,EAAamB,MAElB,UACQnB,EAAamB,MAAMC,SAASF,UAChC,GAAIG,EAAO,CACTnB,EAAQiB,OAAQ,EAEhB,IACE,MAAM7B,QAAiBb,EAAAA,EAAM6C,KAAK,GAAGnB,eAAsB,CACzDK,SAAUF,EAAUE,SACpBC,SAAUH,EAAUG,YAIhB,MAAE3B,EAAK,KAAEyC,GAASjC,EAASiC,KAgBjC,GAfAxC,aAAayC,QAAQ,QAAS1C,GAG1BwB,EAAUI,SACZ3B,aAAayC,QAAQ,qBAAsBlB,EAAUE,UAErDzB,aAAaS,WAAW,sBAI1BT,aAAayC,QAAQ,WAAYC,KAAKC,UAAUH,IAGhDxC,aAAayC,QAAQ,SAAUD,EAAKI,IACpC5C,aAAayC,QAAQ,WAAYD,EAAKK,MAClCL,EAAKM,WACP9C,aAAayC,QAAQ,YAAaD,EAAKM,YACvCC,QAAQC,IAAI,UAAWR,EAAKM,iBACvB,GAAkB,YAAdN,EAAKK,KAEd,IACE,MAAMI,QAAwBvD,EAAAA,EAAMwD,IAAI,GAAG9B,sBAA4BoB,EAAKI,MACxEK,EAAgBT,KAAKW,SAAWF,EAAgBT,KAAKA,MACvDxC,aAAayC,QAAQ,YAAaQ,EAAgBT,KAAKA,KAAKI,IAC5DG,QAAQC,IAAI,mBAAoBC,EAAgBT,KAAKA,KAAKI,MAE1DG,QAAQ3C,MAAM,qBACdS,EAAAA,GAAUuC,QAAQ,yBAEtB,CAAE,MAAOC,GACPN,QAAQ3C,MAAM,YAAaiD,EAC7B,CAGFN,QAAQC,IAAI,aAAcR,GAE1B3B,EAAAA,GAAUsC,QAAQ,QAGA,YAAdX,EAAKK,KACP9B,EAAOuC,KAAK,iBACW,UAAdd,EAAKK,MAAkC,YAAdL,EAAKK,KACvC9B,EAAOuC,KAAK,kBAEZvC,EAAOuC,KAAK,aAEhB,CAAE,MAAOlD,GACP2C,QAAQ3C,MAAM,QAASA,GACvBS,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,iBACnD,CAAE,QACAX,EAAQiB,OAAQ,CAClB,CACF,GAEJ,CAAE,MAAOhC,GACPe,EAAQiB,OAAQ,EAChBvB,EAAAA,GAAUT,MAAM,SAClB,GAIImD,GAAwBrC,EAAAA,EAAAA,KAAI,GAC5BsC,GAAkBtC,EAAAA,EAAAA,IAAI,MACtBuC,GAAkBvC,EAAAA,EAAAA,KAAI,GAEtBwC,GAAelC,EAAAA,EAAAA,IAAS,CAC5BC,SAAU,GACVC,SAAU,GACViC,gBAAiB,GACjBC,KAAM,GACNC,MAAO,GACPC,MAAO,GACPhB,WAAY,GACZD,KAAM,SAGFkB,EAAeA,CAACC,EAAM5B,EAAO6B,KACnB,KAAV7B,EACF6B,EAAS,IAAIC,MAAM,YACV9B,IAAUsB,EAAahC,SAChCuC,EAAS,IAAIC,MAAM,eAEnBD,KAIEE,EAAgB,CACpB1C,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzDL,SAAU,CACR,CAAEG,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzD4B,gBAAiB,CACf,CAAE9B,UAAU,EAAMC,QAAS,UAAWC,QAAS,QAC/C,CAAEqC,UAAWL,EAAchC,QAAS,SAEtC6B,KAAM,CACJ,CAAE/B,UAAU,EAAMC,QAAS,QAASC,QAAS,SAE/C8B,MAAO,CACL,CAAEQ,KAAM,QAASvC,QAAS,aAAcC,QAAS,SAEnD+B,MAAO,CACL,CAAEQ,QAAS,oBAAqBxC,QAAS,aAAcC,QAAS,UAQ9DwC,EAAiBpC,UACrB,GAAKqB,EAAgBpB,MAErB,UACQoB,EAAgBpB,MAAMC,SAASF,UACnC,GAAIG,EAAO,CACTmB,EAAgBrB,OAAQ,EAExB,IACE,MAAM,gBAAEuB,KAAoBa,GAAiBd,QAEtBhE,EAAAA,EAAM6C,KAAK,GAAGnB,kBAAyBoD,GAE9D3D,EAAAA,GAAUsC,QAAQ,YAClBI,EAAsBnB,OAAQ,EAG9Bb,EAAUE,SAAWiC,EAAajC,SAClCF,EAAUG,SAAW,EACvB,CAAE,MAAOtB,GACP2C,QAAQ3C,MAAM,QAASA,GACvBS,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,aACnD,CAAE,QACA2B,EAAgBrB,OAAQ,CAC1B,CACF,GAEJ,CAAE,MAAOhC,GACPqD,EAAgBrB,OAAQ,EACxBvB,EAAAA,GAAUT,MAAM,SAClB,GAIIqE,GAA8BvD,EAAAA,EAAAA,KAAI,GAClCwD,GAAwBxD,EAAAA,EAAAA,IAAI,MAC5ByD,GAAwBzD,EAAAA,EAAAA,KAAI,GAE5B0D,GAAqBpD,EAAAA,EAAAA,IAAS,CAClCC,SAAU,GACVoC,MAAO,KAGHgB,EAAsB,CAC1BpD,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,SAAUC,QAAS,SAEhD8B,MAAO,CACL,CAAEhC,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEsC,KAAM,QAASvC,QAAS,aAAcC,QAAS,UAQ/C+C,EAAuB3C,UAC3B,GAAKuC,EAAsBtC,MAE3B,UACQsC,EAAsBtC,MAAMC,SAASF,UACzC,GAAIG,EAAO,CACTqC,EAAsBvC,OAAQ,EAE9B,UAEyB1C,EAAAA,EAAM6C,KAAK,GAAGnB,yBAAgCwD,GAErE/D,EAAAA,GAAUsC,QAAQ,kBAClBsB,EAA4BrC,OAAQ,CACtC,CAAE,MAAOhC,GACP2C,QAAQ3C,MAAM,YAAaA,GAC3BS,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,aACnD,CAAE,QACA6C,EAAsBvC,OAAQ,CAChC,CACF,GAEJ,CAAE,MAAOhC,GACPuE,EAAsBvC,OAAQ,EAC9BvB,EAAAA,GAAUT,MAAM,SAClB,GAII2E,EAA0BA,KAC9B,MAAMC,EAAqBhF,aAAaC,QAAQ,sBAC5C+E,IACFzD,EAAUE,SAAWuD,EACrBzD,EAAUI,UAAW,I,OAKzBoD,I,8KA7aEE,EAAAA,EAAAA,IA2IM,MA3INC,EA2IM,EA1IJC,EAAAA,EAAAA,IA6EM,MA7ENC,EA6EM,EA3EJD,EAAAA,EAAAA,IAoCM,MApCNE,EAoCM,C,iZApBJF,EAAAA,EAAAA,IAmBM,MAnBNG,EAmBM,EAlBJH,EAAAA,EAAAA,IAKM,MALNI,EAKM,EAJJJ,EAAAA,EAAAA,IAEM,MAFNK,EAEM,EADJC,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,KAASE,EAAAA,EAAAA,IAAAC,EAAAA,U,uBAEpBT,EAAAA,EAAAA,IAAwC,OAAnCU,MAAM,gBAAe,YAAQ,OAEpCV,EAAAA,EAAAA,IAKM,MALNW,EAKM,EAJJX,EAAAA,EAAAA,IAEM,MAFNY,EAEM,EADJN,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,KAASE,EAAAA,EAAAA,IAAAC,EAAAA,U,uBAEpBT,EAAAA,EAAAA,IAAuC,OAAlCU,MAAM,gBAAe,WAAO,OAEnCV,EAAAA,EAAAA,IAKM,MALNa,EAKM,EAJJb,EAAAA,EAAAA,IAEM,MAFNc,EAEM,EADJR,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,KAASE,EAAAA,EAAAA,IAAAC,EAAAA,U,uBAEpBT,EAAAA,EAAAA,IAAyC,OAApCU,MAAM,gBAAe,aAAS,WAMzCV,EAAAA,EAAAA,IAmCM,MAnCNe,EAmCM,EAlCJf,EAAAA,EAAAA,IAiCM,MAjCNgB,EAiCM,C,eAhCJhB,EAAAA,EAAAA,IAAgC,MAA5BU,MAAM,cAAa,QAAI,I,eAC3BV,EAAAA,EAAAA,IAAsC,KAAnCU,MAAM,iBAAgB,aAAS,KAElCJ,EAAAA,EAAAA,IA4BUW,EAAA,CA5BAC,MAAO9E,EAAY+E,MAAO1E,E,QAAgB,eAAJV,IAAID,EAAe4E,MAAM,c,kBACvE,IAMe,EANfJ,EAAAA,EAAAA,IAMec,EAAA,CANDC,KAAK,YAAU,C,iBAC3B,IAIW,EAJXf,EAAAA,EAAAA,IAIWgB,EAAA,C,WAHAlF,EAAUE,S,qCAAVF,EAAUE,SAAQiF,GAC3BC,YAAY,OACX,eAAahB,EAAAA,EAAAA,IAAAiB,EAAAA,O,8CAIlBnB,EAAAA,EAAAA,IAQec,EAAA,CARDC,KAAK,YAAU,C,iBAC3B,IAMW,EANXf,EAAAA,EAAAA,IAMWgB,EAAA,C,WALAlF,EAAUG,S,qCAAVH,EAAUG,SAAQgF,GAC3BrC,KAAK,WACLsC,YAAY,OACX,eAAahB,EAAAA,EAAAA,IAAAkB,EAAAA,MACd,oB,8CAMJpB,EAAAA,EAAAA,IAIec,EAAA,M,iBAHb,IAEY,EAFZd,EAAAA,EAAAA,IAEYqB,EAAA,CAFDzC,KAAK,UAAWlD,QAASA,EAAAiB,MAAU2E,QAAO7E,EAAa2D,MAAM,gB,kBAAe,IAEvFmB,EAAA,MAAAA,EAAA,M,QAFuF,W,+DAYjGvB,EAAAA,EAAAA,IAiCYwB,EAAA,CAhCVC,MAAM,O,WACG3D,EAAAnB,M,qCAAAmB,EAAqBnB,MAAAsE,GAC9BS,MAAM,QACNC,OAAA,GACA,uB,CAsBWC,QAAMC,EAAAA,EAAAA,IACf,IAGO,EAHPnC,EAAAA,EAAAA,IAGO,OAHPoC,EAGO,EAFL9B,EAAAA,EAAAA,IAAgEqB,EAAA,CAApDC,QAAKC,EAAA,KAAAA,EAAA,GAAAN,GAAEnD,EAAAnB,OAAwB,I,kBAAO,IAAE4E,EAAA,MAAAA,EAAA,M,QAAF,S,eAClDvB,EAAAA,EAAAA,IAA2FqB,EAAA,CAAhFzC,KAAK,UAAWlD,QAASsC,EAAArB,MAAkB2E,QAAOxC,G,kBAAgB,IAAEyC,EAAA,MAAAA,EAAA,M,QAAF,S,iDAvBjF,IAmBU,EAnBVvB,EAAAA,EAAAA,IAmBUW,EAAA,CAnBAC,MAAO3C,EAAe4C,MAAOnC,E,QAAmB,kBAAJjD,IAAIsC,EAAkB,cAAY,Q,kBACtF,IAEe,EAFfiC,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,MAAMhB,KAAK,Y,kBAC7B,IAA0E,EAA1Ef,EAAAA,EAAAA,IAA0EgB,EAAA,C,WAAvD/C,EAAajC,S,qCAAbiC,EAAajC,SAAQiF,GAAEC,YAAY,U,gCAExDlB,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,KAAKhB,KAAK,Y,kBAC5B,IAAuG,EAAvGf,EAAAA,EAAAA,IAAuGgB,EAAA,C,WAApF/C,EAAahC,S,qCAAbgC,EAAahC,SAAQgF,GAAErC,KAAK,WAAWsC,YAAY,QAAQ,oB,gCAEhFlB,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,OAAOhB,KAAK,mB,kBAC9B,IAAgH,EAAhHf,EAAAA,EAAAA,IAAgHgB,EAAA,C,WAA7F/C,EAAaC,gB,qCAAbD,EAAaC,gBAAe+C,GAAErC,KAAK,WAAWsC,YAAY,UAAU,oB,gCAEzFlB,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,KAAKhB,KAAK,Q,kBAC5B,IAAqE,EAArEf,EAAAA,EAAAA,IAAqEgB,EAAA,C,WAAlD/C,EAAaE,K,qCAAbF,EAAaE,KAAI8C,GAAEC,YAAY,S,gCAEpDlB,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,KAAKhB,KAAK,S,kBAC5B,IAAsE,EAAtEf,EAAAA,EAAAA,IAAsEgB,EAAA,C,WAAnD/C,EAAaG,M,qCAAbH,EAAaG,MAAK6C,GAAEC,YAAY,S,gCAErDlB,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,MAAMhB,KAAK,S,kBAC7B,IAAuE,EAAvEf,EAAAA,EAAAA,IAAuEgB,EAAA,C,WAApD/C,EAAaI,M,qCAAbJ,EAAaI,MAAK4C,GAAEC,YAAY,U,6EAYzDlB,EAAAA,EAAAA,IAqBYwB,EAAA,CApBVC,MAAM,O,WACGzC,EAAArC,M,uCAAAqC,EAA2BrC,MAAAsE,GACpCS,MAAM,QACNC,OAAA,GACA,uB,CAUWC,QAAMC,EAAAA,EAAAA,IACf,IAGO,EAHPnC,EAAAA,EAAAA,IAGO,OAHPsC,EAGO,EAFLhC,EAAAA,EAAAA,IAAsEqB,EAAA,CAA1DC,QAAKC,EAAA,MAAAA,EAAA,IAAAN,GAAEjC,EAAArC,OAA8B,I,kBAAO,IAAE4E,EAAA,MAAAA,EAAA,M,QAAF,S,eACxDvB,EAAAA,EAAAA,IAAuGqB,EAAA,CAA5FzC,KAAK,UAAWlD,QAASwD,EAAAvC,MAAwB2E,QAAOjC,G,kBAAsB,IAAEkC,EAAA,MAAAA,EAAA,M,QAAF,S,iDAX7F,IAOU,EAPVvB,EAAAA,EAAAA,IAOUW,EAAA,CAPAC,MAAOzB,EAAqB0B,MAAOzB,E,QAAyB,wBAAJ3D,IAAIwD,EAAwB,cAAY,Q,kBACxG,IAEe,EAFfe,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,MAAMhB,KAAK,Y,kBAC7B,IAAgF,EAAhFf,EAAAA,EAAAA,IAAgFgB,EAAA,C,WAA7D7B,EAAmBnD,S,uCAAnBmD,EAAmBnD,SAAQiF,GAAEC,YAAY,U,gCAE9DlB,EAAAA,EAAAA,IAEec,EAAA,CAFDiB,MAAM,KAAKhB,KAAK,S,kBAC5B,IAAgF,EAAhFf,EAAAA,EAAAA,IAAgFgB,EAAA,C,WAA7D7B,EAAmBf,M,uCAAnBe,EAAmBf,MAAK6C,GAAEC,YAAY,a,2FC5HnE,MAAMe,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://ms/./src/views/LoginView.vue", "webpack://ms/./src/views/LoginView.vue?5755"], "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card\">\r\n      <!-- Left side -->\r\n      <div class=\"login-info\">\r\n        <div class=\"logo-wrapper\">\r\n          <div class=\"logo-icon\">\r\n            <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo-img\" />\r\n            <i class=\"el-icon-monitor\"></i>\r\n          </div>\r\n          <div class=\"logo-text\">\r\n            实习生学籍管理系统\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"welcome-text\">\r\n          <h2>欢迎回来</h2>\r\n          <p>登录您的账户以继续访问系统</p>\r\n        </div>\r\n        \r\n        <div class=\"feature-list\">\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">现代化的管理界面</div>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">强大的功能模块</div>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">安全可靠的数据保护</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- Right side -->\r\n      <div class=\"login-form-wrapper\">\r\n        <div class=\"login-form-container\">\r\n          <h2 class=\"form-title\">用户登录</h2>\r\n          <p class=\"form-subtitle\">请输入您的账户信息</p>\r\n          \r\n          <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" class=\"login-form\">\r\n            <el-form-item prop=\"username\">\r\n              <el-input \r\n                v-model=\"loginForm.username\" \r\n                placeholder=\"输入账号\" \r\n                :prefix-icon=\"User\">\r\n              </el-input>\r\n            </el-form-item>\r\n            \r\n            <el-form-item prop=\"password\">\r\n              <el-input \r\n                v-model=\"loginForm.password\" \r\n                type=\"password\" \r\n                placeholder=\"输入密码\" \r\n                :prefix-icon=\"Lock\"\r\n                show-password>\r\n              </el-input>\r\n            </el-form-item>\r\n            \r\n           \r\n            \r\n            <el-form-item>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"handleLogin\" class=\"login-button\">\r\n                登录\r\n              </el-button>\r\n            </el-form-item>\r\n            \r\n           \r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 注册对话框 -->\r\n    <el-dialog\r\n      title=\"用户注册\"\r\n      v-model=\"registerDialogVisible\"\r\n      width=\"400px\"\r\n      center\r\n      destroy-on-close\r\n    >\r\n      <el-form :model=\"registerForm\" :rules=\"registerRules\" ref=\"registerFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"registerForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" :loading=\"registerLoading\" @click=\"handleRegister\">注册</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 忘记密码对话框 -->\r\n    <el-dialog\r\n      title=\"忘记密码\"\r\n      v-model=\"forgotPasswordDialogVisible\"\r\n      width=\"400px\"\r\n      center\r\n      destroy-on-close\r\n    >\r\n      <el-form :model=\"forgotPasswordForm\" :rules=\"forgotPasswordRules\" ref=\"forgotPasswordFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"forgotPasswordForm.username\" placeholder=\"请输入用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"forgotPasswordForm.email\" placeholder=\"请输入注册时的邮箱\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"forgotPasswordDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" :loading=\"forgotPasswordLoading\" @click=\"handleForgotPassword\">提交</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { User, Lock, Check } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\n// 配置全局API请求拦截器，自动添加token\r\naxios.interceptors.request.use(\r\n  config => {\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器，处理401错误\r\naxios.interceptors.response.use(\r\n  response => response,\r\n  error => {\r\n    if (error.response && error.response.status === 401) {\r\n      // 清除本地存储的token\r\n      localStorage.removeItem('token')\r\n      localStorage.removeItem('userInfo')\r\n      // 如果用户不在登录页，重定向到登录页\r\n      if (window.location.pathname !== '/login') {\r\n        ElMessage.error('登录已过期，请重新登录')\r\n        window.location.href = '/#/login'\r\n      }\r\n    }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nconst router = useRouter()\r\nconst loginFormRef = ref(null)\r\nconst loading = ref(false)\r\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\r\n\r\n// 登录相关\r\nconst loginForm = reactive({\r\n  username: '',\r\n  password: '',\r\n  remember: false\r\n})\r\n\r\nconst loginRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst handleLogin = async () => {\r\n  if (!loginFormRef.value) return\r\n  \r\n  try {\r\n    await loginFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        loading.value = true\r\n        \r\n        try {\r\n          const response = await axios.post(`${API_URL}/auth/login`, {\r\n            username: loginForm.username,\r\n            password: loginForm.password\r\n          })\r\n          \r\n          // 登录成功，保存token和用户信息\r\n          const { token, data } = response.data\r\n          localStorage.setItem('token', token)\r\n\r\n          // 如果选择记住我，保存用户名\r\n          if (loginForm.remember) {\r\n            localStorage.setItem('rememberedUsername', loginForm.username)\r\n          } else {\r\n            localStorage.removeItem('rememberedUsername')\r\n          }\r\n\r\n          // 存储用户信息\r\n          localStorage.setItem('userInfo', JSON.stringify(data))\r\n\r\n          // 单独保存关键信息，方便使用\r\n          localStorage.setItem('userId', data.id)\r\n          localStorage.setItem('userRole', data.role)\r\n          if (data.student_id) {\r\n            localStorage.setItem('studentId', data.student_id)\r\n            console.log('保存学生ID:', data.student_id)\r\n          } else if (data.role === 'student') {\r\n            // 如果是学生但没有student_id，尝试从其他地方获取\r\n            try {\r\n              const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`)\r\n              if (studentResponse.data.success && studentResponse.data.data) {\r\n                localStorage.setItem('studentId', studentResponse.data.data.id)\r\n                console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id)\r\n              } else {\r\n                console.error('无法获取学生ID，但用户角色为学生')\r\n                ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用')\r\n              }\r\n            } catch (err) {\r\n              console.error('获取学生信息失败:', err)\r\n            }\r\n          }\r\n\r\n          console.log('登录成功，用户信息:', data)\r\n          \r\n          ElMessage.success('登录成功')\r\n          \r\n          // 根据用户角色跳转到不同页面\r\n          if (data.role === 'student') {\r\n            router.push('/courses/list') // 学生跳转到课程管理\r\n          } else if (data.role === 'admin' || data.role === 'teacher') {\r\n            router.push('/students/list') // 管理员和教师跳转到实习生管理\r\n          } else {\r\n            router.push('/dashboard') // 其他角色跳转到首页\r\n          }\r\n        } catch (error) {\r\n          console.error('登录失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码')\r\n        } finally {\r\n          loading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    loading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 注册相关\r\nconst registerDialogVisible = ref(false)\r\nconst registerFormRef = ref(null)\r\nconst registerLoading = ref(false)\r\n\r\nconst registerForm = reactive({\r\n  username: '',\r\n  password: '',\r\n  confirmPassword: '',\r\n  name: '',\r\n  email: '',\r\n  phone: '',\r\n  student_id: '',\r\n  role: 'user'  // 默认注册为普通用户\r\n})\r\n\r\nconst validatePass = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入密码'))\r\n  } else if (value !== registerForm.password) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\nconst registerRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  confirmPassword: [\r\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\r\n    { validator: validatePass, trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showRegister = () => {\r\n  registerDialogVisible.value = true\r\n}\r\n\r\nconst handleRegister = async () => {\r\n  if (!registerFormRef.value) return\r\n  \r\n  try {\r\n    await registerFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        registerLoading.value = true\r\n        \r\n        try {\r\n          const { confirmPassword, ...registerData } = registerForm\r\n          \r\n          const response = await axios.post(`${API_URL}/auth/register`, registerData)\r\n          \r\n          ElMessage.success('注册成功，请登录')\r\n          registerDialogVisible.value = false\r\n          \r\n          // 可选：自动填充登录表单\r\n          loginForm.username = registerForm.username\r\n          loginForm.password = ''\r\n        } catch (error) {\r\n          console.error('注册失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')\r\n        } finally {\r\n          registerLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    registerLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 忘记密码相关\r\nconst forgotPasswordDialogVisible = ref(false)\r\nconst forgotPasswordFormRef = ref(null)\r\nconst forgotPasswordLoading = ref(false)\r\n\r\nconst forgotPasswordForm = reactive({\r\n  username: '',\r\n  email: ''\r\n})\r\n\r\nconst forgotPasswordRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showForgotPassword = () => {\r\n  forgotPasswordDialogVisible.value = true\r\n}\r\n\r\nconst handleForgotPassword = async () => {\r\n  if (!forgotPasswordFormRef.value) return\r\n  \r\n  try {\r\n    await forgotPasswordFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        forgotPasswordLoading.value = true\r\n        \r\n        try {\r\n          // 注意：需要在后端实现忘记密码API\r\n          const response = await axios.post(`${API_URL}/auth/forgot-password`, forgotPasswordForm)\r\n          \r\n          ElMessage.success('重置密码链接已发送到您的邮箱')\r\n          forgotPasswordDialogVisible.value = false\r\n        } catch (error) {\r\n          console.error('忘记密码请求失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '操作失败，请稍后重试')\r\n        } finally {\r\n          forgotPasswordLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    forgotPasswordLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 检查是否有记住的用户名\r\nconst checkRememberedUsername = () => {\r\n  const rememberedUsername = localStorage.getItem('rememberedUsername')\r\n  if (rememberedUsername) {\r\n    loginForm.username = rememberedUsername\r\n    loginForm.remember = true\r\n  }\r\n}\r\n\r\n// 组件挂载时检查记住的用户名\r\ncheckRememberedUsername()\r\n</script>\r\n\r\n<style scoped>\r\n.login-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color:rgb(124, 181, 239);\r\n}\r\n\r\n.login-card {\r\n  width: 1000px;\r\n  height: 600px;\r\n  display: flex;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Left side */\r\n.login-info {\r\n  width: 50%;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  color: white;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.logo-icon {\r\n  width: 120px;\r\n  height: 120px;\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 24px;\r\n  color: #409EFF;\r\n}\r\n\r\n.logo-img {\r\n  width: 96px;\r\n  height: 96px;\r\n  margin-right: 4px;\r\n  object-fit: contain;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-text {\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.welcome-text h2 {\r\n  font-size: 32px;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-text p {\r\n  font-size: 16px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.feature-list {\r\n  margin-top: auto;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.feature-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.feature-text {\r\n  font-size: 16px;\r\n}\r\n\r\n/* Right side */\r\n.login-form-wrapper {\r\n  width: 50%;\r\n  background-color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n}\r\n\r\n.login-form-container {\r\n  width: 100%;\r\n  max-width: 320px;\r\n}\r\n\r\n.form-title {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  text-align: center;\r\n}\r\n\r\n.form-subtitle {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.login-form :deep(.el-input__wrapper) {\r\n  padding: 0 15px;\r\n  height: 50px;\r\n  box-shadow: 0 0 0 1px #e4e7ed inset;\r\n}\r\n\r\n.login-form :deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n.form-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.forgot-link {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-button {\r\n  width: 100%;\r\n  height: 50px;\r\n  border-radius: 6px;\r\n  font-size: 16px;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.register-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.register-link a {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n\r\n/* Responsive */\r\n@media (max-width: 992px) {\r\n  .login-card {\r\n    width: 90%;\r\n    height: auto;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .login-info,\r\n  .login-form-wrapper {\r\n    width: 100%;\r\n    padding: 30px;\r\n  }\r\n  \r\n  .login-info {\r\n    padding-bottom: 40px;\r\n  }\r\n  \r\n  .welcome-text {\r\n    margin-bottom: 30px;\r\n  }\r\n  \r\n  .feature-list {\r\n    margin-top: 0;\r\n  }\r\n}\r\n</style> ", "import script from \"./LoginView.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./LoginView.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./LoginView.vue?vue&type=style&index=0&id=5209985f&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-5209985f\"]])\n\nexport default __exports__"], "names": ["axios", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "pathname", "ElMessage", "href", "router", "useRouter", "loginFormRef", "ref", "loading", "API_URL", "process", "VUE_APP_API_URL", "loginForm", "reactive", "username", "password", "remember", "loginRules", "required", "message", "trigger", "min", "max", "handleLogin", "async", "value", "validate", "valid", "post", "data", "setItem", "JSON", "stringify", "id", "role", "student_id", "console", "log", "studentResponse", "get", "success", "warning", "err", "push", "registerDialogVisible", "registerFormRef", "registerLoading", "registerForm", "confirmPassword", "name", "email", "phone", "validatePass", "rule", "callback", "Error", "registerRules", "validator", "type", "pattern", "handleRegister", "registerData", "forgotPasswordDialogVisible", "forgotPasswordFormRef", "forgotPasswordLoading", "forgotPasswordForm", "forgotPasswordRules", "handleForgotPassword", "checkRememberedUsername", "rememberedUsername", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_icon", "_unref", "Check", "class", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_component_el_form", "model", "rules", "_component_el_form_item", "prop", "_component_el_input", "$event", "placeholder", "User", "Lock", "_component_el_button", "onClick", "_cache", "_component_el_dialog", "title", "width", "center", "footer", "_withCtx", "_hoisted_13", "label", "_hoisted_14", "__exports__"], "sourceRoot": ""}
{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from 'axios';\nimport store from '../store';\nimport router from '../router';\nimport { ElMessage } from 'element-plus';\n\n// 配置axios默认值\nconst api = axios.create({\n  baseURL: 'http://localhost:3000',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加认证头\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理常见错误\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  const {\n    response\n  } = error;\n  if (response) {\n    // 处理不同的错误状态码\n    switch (response.status) {\n      case 401:\n        // 未授权\n        // 清除本地存储的用户信息和token\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('studentId');\n\n        // 清除Vuex中的状态\n        store.dispatch('logout');\n\n        // 判断当前是否在登录页\n        if (router.currentRoute.value.path !== '/login') {\n          router.push('/login');\n          // 显示错误提示\n          ElMessage({\n            message: '登录已过期，请重新登录',\n            type: 'error',\n            duration: 3000\n          });\n        }\n        break;\n      case 403:\n        // 禁止访问\n        ElMessage({\n          message: '您没有权限执行此操作',\n          type: 'error',\n          duration: 3000\n        });\n        break;\n      default:\n        // 处理其他错误\n        ElMessage({\n          message: response.data.message || '请求失败',\n          type: 'error',\n          duration: 3000\n        });\n    }\n  } else {\n    // 处理网络错误\n    ElMessage({\n      message: '网络错误，请检查您的网络连接',\n      type: 'error',\n      duration: 3000\n    });\n  }\n  return Promise.reject(error);\n});\n\n// 添加刷新token的功能（可选，如果后端支持）\nexport const refreshToken = async () => {\n  try {\n    const refreshToken = localStorage.getItem('refreshToken');\n    if (!refreshToken) {\n      return null;\n    }\n    const response = await axios.post('/api/auth/refresh-token', {\n      refreshToken\n    });\n    const {\n      token\n    } = response.data;\n    localStorage.setItem('token', token);\n    return token;\n  } catch (error) {\n    localStorage.removeItem('token');\n    localStorage.removeItem('refreshToken');\n    return null;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "store", "router", "ElMessage", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "error", "Promise", "reject", "response", "status", "removeItem", "dispatch", "currentRoute", "value", "path", "push", "message", "type", "duration", "data", "refreshToken", "post", "setItem"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/utils/api.js"], "sourcesContent": ["import axios from 'axios'\r\nimport store from '../store'\r\nimport router from '../router'\r\nimport { ElMessage } from 'element-plus'\r\n\r\n// 配置axios默认值\r\nconst api = axios.create({\r\n  baseURL: 'http://localhost:3000',\r\n  timeout: 10000,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n})\r\n\r\n// 请求拦截器 - 添加认证头\r\napi.interceptors.request.use(\r\n  config => {\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器 - 处理常见错误\r\napi.interceptors.response.use(\r\n  response => {\r\n    return response\r\n  },\r\n  error => {\r\n    const { response } = error\r\n    if (response) {\r\n      // 处理不同的错误状态码\r\n      switch (response.status) {\r\n        case 401: // 未授权\r\n          // 清除本地存储的用户信息和token\r\n          localStorage.removeItem('token')\r\n          localStorage.removeItem('userId')\r\n          localStorage.removeItem('userRole')\r\n          localStorage.removeItem('studentId')\r\n          \r\n          // 清除Vuex中的状态\r\n          store.dispatch('logout')\r\n          \r\n          // 判断当前是否在登录页\r\n          if (router.currentRoute.value.path !== '/login') {\r\n            router.push('/login')\r\n            // 显示错误提示\r\n            ElMessage({\r\n              message: '登录已过期，请重新登录',\r\n              type: 'error',\r\n              duration: 3000\r\n            })\r\n          }\r\n          break\r\n          \r\n        case 403: // 禁止访问\r\n          ElMessage({\r\n            message: '您没有权限执行此操作',\r\n            type: 'error',\r\n            duration: 3000\r\n          })\r\n          break\r\n          \r\n        default:\r\n          // 处理其他错误\r\n          ElMessage({\r\n            message: response.data.message || '请求失败',\r\n            type: 'error',\r\n            duration: 3000\r\n          })\r\n      }\r\n    } else {\r\n      // 处理网络错误\r\n      ElMessage({\r\n        message: '网络错误，请检查您的网络连接',\r\n        type: 'error',\r\n        duration: 3000\r\n      })\r\n    }\r\n    \r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 添加刷新token的功能（可选，如果后端支持）\r\nexport const refreshToken = async () => {\r\n  try {\r\n    const refreshToken = localStorage.getItem('refreshToken')\r\n    if (!refreshToken) {\r\n      return null\r\n    }\r\n    \r\n    const response = await axios.post('/api/auth/refresh-token', {\r\n      refreshToken\r\n    })\r\n    \r\n    const { token } = response.data\r\n    localStorage.setItem('token', token)\r\n    return token\r\n  } catch (error) {\r\n    localStorage.removeItem('token')\r\n    localStorage.removeItem('refreshToken')\r\n    return null\r\n  }\r\n}\r\n\r\nexport default api "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,SAAS,QAAQ,cAAc;;AAExC;AACA,MAAMC,GAAG,GAAGJ,KAAK,CAACK,MAAM,CAAC;EACvBC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAI;EACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACDI,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACK,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC3BQ,QAAQ,IAAI;EACV,OAAOA,QAAQ;AACjB,CAAC,EACDH,KAAK,IAAI;EACP,MAAM;IAAEG;EAAS,CAAC,GAAGH,KAAK;EAC1B,IAAIG,QAAQ,EAAE;IACZ;IACA,QAAQA,QAAQ,CAACC,MAAM;MACrB,KAAK,GAAG;QAAE;QACR;QACAN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,QAAQ,CAAC;QACjCP,YAAY,CAACO,UAAU,CAAC,UAAU,CAAC;QACnCP,YAAY,CAACO,UAAU,CAAC,WAAW,CAAC;;QAEpC;QACApB,KAAK,CAACqB,QAAQ,CAAC,QAAQ,CAAC;;QAExB;QACA,IAAIpB,MAAM,CAACqB,YAAY,CAACC,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC/CvB,MAAM,CAACwB,IAAI,CAAC,QAAQ,CAAC;UACrB;UACAvB,SAAS,CAAC;YACRwB,OAAO,EAAE,aAAa;YACtBC,IAAI,EAAE,OAAO;YACbC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QACA;MAEF,KAAK,GAAG;QAAE;QACR1B,SAAS,CAAC;UACRwB,OAAO,EAAE,YAAY;UACrBC,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MAEF;QACE;QACA1B,SAAS,CAAC;UACRwB,OAAO,EAAER,QAAQ,CAACW,IAAI,CAACH,OAAO,IAAI,MAAM;UACxCC,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC;IACN;EACF,CAAC,MAAM;IACL;IACA1B,SAAS,CAAC;MACRwB,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,OAAOZ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACF,MAAMA,YAAY,GAAGjB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,IAAI,CAACgB,YAAY,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,MAAMZ,QAAQ,GAAG,MAAMnB,KAAK,CAACgC,IAAI,CAAC,yBAAyB,EAAE;MAC3DD;IACF,CAAC,CAAC;IAEF,MAAM;MAAElB;IAAM,CAAC,GAAGM,QAAQ,CAACW,IAAI;IAC/BhB,YAAY,CAACmB,OAAO,CAAC,OAAO,EAAEpB,KAAK,CAAC;IACpC,OAAOA,KAAK;EACd,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdF,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;IACvC,OAAO,IAAI;EACb;AACF,CAAC;AAED,eAAejB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
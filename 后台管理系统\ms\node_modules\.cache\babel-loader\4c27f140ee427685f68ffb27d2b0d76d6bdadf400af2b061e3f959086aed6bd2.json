{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"course-list-container\"\n};\nconst _hoisted_2 = {\n  class: \"filter-container\"\n};\nconst _hoisted_3 = {\n  class: \"card-header\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"no-material\"\n};\nconst _hoisted_5 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_6 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"filter-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n      model: $setup.filterForm,\n      inline: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"课程标题\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.filterForm.title,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filterForm.title = $event),\n          placeholder: \"请输入课程标题\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSearch\n        }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"搜索\")])),\n          _: 1 /* STABLE */,\n          __: [5]\n        }), _createVNode(_component_el_button, {\n          onClick: $setup.resetFilter\n        }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [6]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])])]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_card, {\n    class: \"table-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"岗前培训课程\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleAddCourse\n    }, {\n      default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"新增课程\")])),\n      _: 1 /* STABLE */,\n      __: [7]\n    })])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.courseList,\n      stripe: \"\",\n      border: \"\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"index\",\n        width: \"50\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"title\",\n        label: \"课程标题\",\n        \"min-width\": \"200\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"description\",\n        label: \"课程描述\",\n        \"min-width\": \"300\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"课件\",\n        width: \"120\",\n        align: \"center\"\n      }, {\n        default: _withCtx(scope => [scope.row.material_path ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          type: \"success\",\n          link: \"\",\n          onClick: $event => $setup.handleDownload(scope.row)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode($setup[\"Download\"])]),\n            _: 1 /* STABLE */\n          }), _cache[9] || (_cache[9] = _createTextVNode(\" 下载课件 \"))]),\n          _: 2 /* DYNAMIC */,\n          __: [9]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : (_openBlock(), _createElementBlock(\"span\", _hoisted_4, \"无\"))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"created_at\",\n        label: \"创建时间\",\n        width: \"160\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"200\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: $event => $setup.handleEdit(scope.row)\n        }, {\n          default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [10]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.handleDelete(scope.row)\n        }, {\n          default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [11]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_pagination, {\n      background: \"\",\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      \"current-page\": $setup.currentPage,\n      \"page-sizes\": [10, 20, 50, 100],\n      \"page-size\": $setup.pageSize,\n      total: $setup.total,\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 新增/编辑课程对话框 \"), _createVNode(_component_el_dialog, {\n    title: $setup.dialogTitle,\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.dialogVisible = $event),\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n      onClick: _cache[3] || (_cache[3] = $event => $setup.dialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [14]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitForm,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [15]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.courseForm,\n      rules: $setup.courseRules,\n      ref: \"courseFormRef\",\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"课程标题\",\n        prop: \"title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.courseForm.title,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.courseForm.title = $event),\n          placeholder: \"请输入课程标题\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"课程描述\",\n        prop: \"description\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          type: \"textarea\",\n          modelValue: $setup.courseForm.description,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.courseForm.description = $event),\n          placeholder: \"请输入课程描述\",\n          rows: \"4\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"课件文件\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_upload, {\n          class: \"material-upload\",\n          action: $setup.uploadAction,\n          headers: $setup.uploadHeaders,\n          \"http-request\": $setup.customUploadRequest,\n          \"on-preview\": $setup.handlePreview,\n          \"on-remove\": $setup.handleRemove,\n          \"on-success\": $setup.handleUploadSuccess,\n          \"before-upload\": $setup.beforeUpload,\n          limit: 1,\n          \"file-list\": $setup.fileList\n        }, {\n          tip: _withCtx(() => _cache[13] || (_cache[13] = [_createElementVNode(\"div\", {\n            class: \"el-upload__tip\"\n          }, \" 支持上传PDF、Word、PPT等格式文件，大小不超过10MB \", -1 /* CACHED */)])),\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\"\n          }, {\n            default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"点击上传\")])),\n            _: 1 /* STABLE */,\n            __: [12]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"action\", \"headers\", \"file-list\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "_createElementVNode", "_hoisted_2", "_component_el_form", "model", "$setup", "filterForm", "inline", "_component_el_form_item", "label", "_component_el_input", "title", "$event", "placeholder", "clearable", "_component_el_button", "type", "onClick", "handleSearch", "_cache", "resetFilter", "header", "_withCtx", "_hoisted_3", "handleAddCourse", "_createBlock", "_component_el_table", "data", "courseList", "stripe", "border", "style", "_component_el_table_column", "width", "prop", "align", "default", "scope", "row", "material_path", "size", "link", "handleDownload", "_component_el_icon", "_hoisted_4", "formatDate", "created_at", "fixed", "handleEdit", "handleDelete", "loading", "_hoisted_5", "_component_el_pagination", "background", "layout", "currentPage", "pageSize", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_createCommentVNode", "_component_el_dialog", "dialogTitle", "dialogVisible", "footer", "_hoisted_6", "submitForm", "submitting", "courseForm", "rules", "courseRules", "ref", "description", "rows", "_component_el_upload", "action", "uploadAction", "headers", "uploadHeaders", "customUploadRequest", "handlePreview", "handleRemove", "handleUploadSuccess", "beforeUpload", "limit", "fileList", "tip"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\views\\courses\\CourseList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"course-list-container\">\r\n    <el-card class=\"filter-card\">\r\n      <div class=\"filter-container\">\r\n        <el-form :model=\"filterForm\" inline>\r\n          <el-form-item label=\"课程标题\">\r\n            <el-input v-model=\"filterForm.title\" placeholder=\"请输入课程标题\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button @click=\"resetFilter\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n    \r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>岗前培训课程</span>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddCourse\">新增课程</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table :data=\"courseList\" stripe border style=\"width: 100%\" v-loading=\"loading\">\r\n        <el-table-column type=\"index\" width=\"50\" />\r\n        <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"200\" />\r\n        <el-table-column prop=\"description\" label=\"课程描述\" min-width=\"300\" show-overflow-tooltip />\r\n        <el-table-column label=\"课件\" width=\"120\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button v-if=\"scope.row.material_path\" size=\"small\" type=\"success\" link @click=\"handleDownload(scope.row)\">\r\n              <el-icon><Download /></el-icon>\r\n              下载课件\r\n            </el-button>\r\n            <span v-else class=\"no-material\">无</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/编辑课程对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form :model=\"courseForm\" :rules=\"courseRules\" ref=\"courseFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"课程标题\" prop=\"title\">\r\n          <el-input v-model=\"courseForm.title\" placeholder=\"请输入课程标题\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程描述\" prop=\"description\">\r\n          <el-input type=\"textarea\" v-model=\"courseForm.description\" placeholder=\"请输入课程描述\" rows=\"4\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课件文件\">\r\n          <el-upload\r\n            class=\"material-upload\"\r\n            :action=\"uploadAction\"\r\n            :headers=\"uploadHeaders\"\r\n            :http-request=\"customUploadRequest\"\r\n            :on-preview=\"handlePreview\"\r\n            :on-remove=\"handleRemove\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :before-upload=\"beforeUpload\"\r\n            :limit=\"1\"\r\n            :file-list=\"fileList\"\r\n          >\r\n            <el-button type=\"primary\">点击上传</el-button>\r\n            <template #tip>\r\n              <div class=\"el-upload__tip\">\r\n                支持上传PDF、Word、PPT等格式文件，大小不超过10MB\r\n              </div>\r\n            </template>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Download } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\n// 直接定义API基础URL，而不是使用环境变量\r\nconst API_URL = 'http://localhost:3000'\r\n\r\n// 创建axios实例，配置默认headers\r\nconst http = axios.create({\r\n  baseURL: API_URL\r\n})\r\n\r\n// 添加请求拦截器，为每个请求添加token\r\nhttp.interceptors.request.use(config => {\r\n  // 从localStorage获取token\r\n  const token = localStorage.getItem('token')\r\n  if (token) {\r\n    config.headers.Authorization = `Bearer ${token}`\r\n  }\r\n  return config\r\n}, error => {\r\n  return Promise.reject(error)\r\n})\r\n\r\n// 日期格式化函数\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return '-'\r\n  const date = new Date(dateString)\r\n  if (isNaN(date.getTime())) return dateString\r\n  \r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  const seconds = String(date.getSeconds()).padStart(2, '0')\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n}\r\n\r\nconst loading = ref(true)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst dialogVisible = ref(false)\r\nconst dialogTitle = ref('新增课程')\r\nconst courseFormRef = ref(null)\r\nconst fileList = ref([])\r\nconst uploadAction = ref(`${API_URL}/api/courses`) // 实际的上传API地址\r\nconst submitting = ref(false)\r\nconst tempFile = ref(null) // 存储临时文件\r\n\r\n// 上传请求头，包含token\r\nconst uploadHeaders = computed(() => {\r\n  return {\r\n    Authorization: `Bearer ${localStorage.getItem('token') || ''}`\r\n  }\r\n})\r\n\r\n// 课程列表\r\nconst courseList = ref([])\r\n\r\n// 过滤条件\r\nconst filterForm = reactive({\r\n  title: ''\r\n})\r\n\r\n// 课程表单\r\nconst courseForm = reactive({\r\n  id: null,\r\n  title: '',\r\n  description: '',\r\n  material_path: ''\r\n})\r\n\r\n// 验证规则\r\nconst courseRules = {\r\n  title: [\r\n    { required: true, message: '请输入课程标题', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 获取课程列表数据\r\nconst fetchCourses = async () => {\r\n  loading.value = true\r\n  try {\r\n    // 处理分页\r\n    const response = await http.get('/api/courses')\r\n    if (response.data.success) {\r\n      courseList.value = response.data.data\r\n      total.value = response.data.count\r\n    } else {\r\n      ElMessage.error(response.data.message || '获取课程列表失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取课程列表失败:', error)\r\n    if (error.response && error.response.status === 401) {\r\n      ElMessage.error('登录已过期，请重新登录')\r\n      // 可以选择重定向到登录页面\r\n      // router.push('/login')\r\n    } else {\r\n      ElMessage.error('获取课程列表失败，请检查网络连接')\r\n    }\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  fetchCourses()\r\n})\r\n\r\n// 重置过滤条件\r\nconst resetFilter = () => {\r\n  filterForm.title = ''\r\n  fetchCourses()\r\n}\r\n\r\n// 搜索\r\nconst handleSearch = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await http.get('/api/courses/search', {\r\n      params: { keyword: filterForm.title }\r\n    })\r\n    if (response.data.success) {\r\n      courseList.value = response.data.data\r\n      total.value = response.data.count\r\n      ElMessage.success('搜索完成')\r\n    } else {\r\n      ElMessage.error(response.data.message || '搜索失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('搜索失败:', error)\r\n    ElMessage.error('搜索失败，请检查网络连接')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 新增课程\r\nconst handleAddCourse = () => {\r\n  dialogTitle.value = '新增课程'\r\n  dialogVisible.value = true\r\n  // 重置表单\r\n  courseForm.id = null\r\n  courseForm.title = ''\r\n  courseForm.description = ''\r\n  courseForm.material_path = ''\r\n  tempFile.value = null\r\n  fileList.value = []\r\n}\r\n\r\n// 编辑课程\r\nconst handleEdit = (row) => {\r\n  dialogTitle.value = '编辑课程'\r\n  dialogVisible.value = true\r\n  \r\n  // 填充表单数据\r\n  courseForm.id = row.id\r\n  courseForm.title = row.title\r\n  courseForm.description = row.description\r\n  courseForm.material_path = row.material_path\r\n  tempFile.value = null\r\n  \r\n  // 设置文件列表\r\n  fileList.value = []\r\n  if (row.material_path) {\r\n    fileList.value.push({\r\n      name: row.material_path.split('/').pop(),\r\n      url: `${API_URL}/${row.material_path}`\r\n    })\r\n  }\r\n}\r\n\r\n// 删除课程\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      const response = await http.delete(`/api/courses/${row.id}`)\r\n      if (response.data.success) {\r\n        ElMessage.success(`课程 ${row.title} 已删除`)\r\n        fetchCourses()\r\n      } else {\r\n        ElMessage.error(response.data.message || '删除课程失败')\r\n      }\r\n    } catch (error) {\r\n      console.error('删除课程失败:', error)\r\n      ElMessage.error('删除课程失败，请检查网络连接')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 下载课件\r\nconst handleDownload = async (row) => {\r\n  if (!row.material_path) {\r\n    ElMessage.warning('该课程没有上传课件')\r\n    return\r\n  }\r\n  \r\n  try {\r\n    ElMessage.success(`正在下载 ${row.title} 的课件`)\r\n    \r\n    // 使用axios发送请求并获取blob数据\r\n    const response = await http.get(`/api/courses/${row.id}/download`, {\r\n      responseType: 'blob' // 指定响应类型为blob\r\n    })\r\n    \r\n    // 获取文件名，从响应头获取\r\n    let filename = ''\r\n    const contentDisposition = response.headers['content-disposition']\r\n    if (contentDisposition) {\r\n      const filenameMatch = contentDisposition.match(/filename[^;=\\n]*=((['\"]).*?\\2|[^;\\n]*)/)\r\n      if (filenameMatch && filenameMatch[1]) {\r\n        filename = filenameMatch[1].replace(/['\"]/g, '')\r\n        try {\r\n          // 尝试解码文件名\r\n          filename = decodeURIComponent(filename)\r\n        } catch (e) {\r\n          console.error('解码文件名失败', e)\r\n          // 如果解码失败，使用原始文件名\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 如果没有从响应头获取到文件名，则使用路径中的文件名\r\n    if (!filename) {\r\n      filename = row.material_path.split('/').pop()\r\n    }\r\n    \r\n    // 创建blob链接\r\n    const blob = new Blob([response.data])\r\n    const url = window.URL.createObjectURL(blob)\r\n    \r\n    // 创建临时链接并模拟点击下载\r\n    const link = document.createElement('a')\r\n    link.href = url\r\n    link.setAttribute('download', filename)\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    \r\n    // 清理\r\n    document.body.removeChild(link)\r\n    window.URL.revokeObjectURL(url)\r\n    \r\n  } catch (error) {\r\n    console.error('下载失败:', error)\r\n    ElMessage.error('下载课件失败，请检查权限或网络连接')\r\n  }\r\n}\r\n\r\n// 自定义上传请求处理\r\nconst customUploadRequest = (options) => {\r\n  // 存储文件对象\r\n  tempFile.value = options.file\r\n  \r\n  // 修改文件名以解决中文编码问题\r\n  const originalFileName = options.file.name\r\n  // 显示成功消息，包含原始文件名\r\n  ElMessage.success(`文件 ${originalFileName} 已选择`)\r\n  options.onSuccess()\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!courseFormRef.value) return\r\n  \r\n  await courseFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      submitting.value = true\r\n      try {\r\n        const formData = new FormData()\r\n        formData.append('title', courseForm.title)\r\n        formData.append('description', courseForm.description)\r\n        \r\n        if (tempFile.value) {\r\n          // 添加原始文件名到formData以供后端使用\r\n          formData.append('originalFileName', tempFile.value.name)\r\n          formData.append('material', tempFile.value)\r\n        }\r\n        \r\n        let response\r\n        if (courseForm.id) {\r\n          // 编辑模式\r\n          response = await http.put(`/api/courses/${courseForm.id}`, formData)\r\n          if (response.data.success) {\r\n            ElMessage.success(`课程 ${courseForm.title} 信息已更新`)\r\n          } else {\r\n            ElMessage.error(response.data.message || '更新课程失败')\r\n          }\r\n        } else {\r\n          // 新增模式\r\n          response = await http.post('/api/courses', formData)\r\n          if (response.data.success) {\r\n            ElMessage.success(`课程 ${courseForm.title} 添加成功`)\r\n          } else {\r\n            ElMessage.error(response.data.message || '添加课程失败')\r\n          }\r\n        }\r\n        \r\n        dialogVisible.value = false\r\n        fetchCourses()\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error)\r\n        ElMessage.error('提交失败，请检查网络连接')\r\n      } finally {\r\n        submitting.value = false\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 文件上传相关方法\r\nconst handlePreview = (file) => {\r\n  if (file.url) {\r\n    window.open(file.url, '_blank')\r\n  }\r\n}\r\n\r\nconst handleRemove = () => {\r\n  fileList.value = []\r\n  tempFile.value = null\r\n}\r\n\r\nconst handleUploadSuccess = () => {\r\n  // 这个方法不再需要显示成功消息，因为在customUploadRequest中已处理\r\n}\r\n\r\nconst beforeUpload = (file) => {\r\n  // 文件大小限制：10MB\r\n  const maxSize = 10 * 1024 * 1024\r\n  if (file.size > maxSize) {\r\n    ElMessage.error('文件大小不能超过10MB')\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  fetchCourses()\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchCourses()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.course-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.no-material {\r\n  color: #909399;\r\n}\r\n\r\n.material-upload {\r\n  width: 100%;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n}\r\n\r\n:deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAkB;;EAetBA,KAAK,EAAC;AAAa;;;EAkBPA,KAAK,EAAC;;;EAgBpBA,KAAK,EAAC;AAAsB;;EAoD1BA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;uBAvGhCC,mBAAA,CA6GM,OA7GNC,UA6GM,GA5GJC,YAAA,CAYUC,kBAAA;IAZDJ,KAAK,EAAC;EAAa;sBAC1B,MAUM,CAVNK,mBAAA,CAUM,OAVNC,UAUM,GATJH,YAAA,CAQUI,kBAAA;MARAC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAEC,MAAM,EAAN;;wBAC3B,MAEe,CAFfR,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC;MAAM;0BACxB,MAAgF,CAAhFV,YAAA,CAAgFW,mBAAA;sBAA7DL,MAAA,CAAAC,UAAU,CAACK,KAAK;qEAAhBN,MAAA,CAAAC,UAAU,CAACK,KAAK,GAAAC,MAAA;UAAEC,WAAW,EAAC,SAAS;UAACC,SAAS,EAAT;;;UAE7Df,YAAA,CAGeS,uBAAA;0BAFb,MAA8D,CAA9DT,YAAA,CAA8DgB,oBAAA;UAAnDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEZ,MAAA,CAAAa;;4BAAc,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;YAClDpB,YAAA,CAA8CgB,oBAAA;UAAlCE,OAAK,EAAEZ,MAAA,CAAAe;QAAW;4BAAE,MAAED,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;;;MAM1CpB,YAAA,CAgDUC,kBAAA;IAhDDJ,KAAK,EAAC;EAAY;IACdyB,MAAM,EAAAC,QAAA,CACf,MAKM,CALNrB,mBAAA,CAKM,OALNsB,UAKM,G,0BAJJtB,mBAAA,CAAmB,cAAb,QAAM,qBACZA,mBAAA,CAEM,cADJF,YAAA,CAAmEgB,oBAAA;MAAxDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAmB;;wBAAiB,MAAIL,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;sBAK7D,MAwBW,C,+BAxBXM,YAAA,CAwBWC,mBAAA;MAxBAC,IAAI,EAAEtB,MAAA,CAAAuB,UAAU;MAAEC,MAAM,EAAN,EAAM;MAACC,MAAM,EAAN,EAAM;MAACC,KAAmB,EAAnB;QAAA;MAAA;;wBACzC,MAA2C,CAA3ChC,YAAA,CAA2CiC,0BAAA;QAA1BhB,IAAI,EAAC,OAAO;QAACiB,KAAK,EAAC;UACpClC,YAAA,CAA6DiC,0BAAA;QAA5CE,IAAI,EAAC,OAAO;QAACzB,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;UACrDV,YAAA,CAAyFiC,0BAAA;QAAxEE,IAAI,EAAC,aAAa;QAACzB,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;UACjEV,YAAA,CAQkBiC,0BAAA;QARDvB,KAAK,EAAC,IAAI;QAACwB,KAAK,EAAC,KAAK;QAACE,KAAK,EAAC;;QACjCC,OAAO,EAAAd,QAAA,CAcKe,KAdE,KACNA,KAAK,CAACC,GAAG,CAACC,aAAa,I,cAAxCd,YAAA,CAGYV,oBAAA;;UAH8ByB,IAAI,EAAC,OAAO;UAACxB,IAAI,EAAC,SAAS;UAACyB,IAAI,EAAJ,EAAI;UAAExB,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAqC,cAAc,CAACL,KAAK,CAACC,GAAG;;4BACzG,MAA+B,CAA/BvC,YAAA,CAA+B4C,kBAAA;8BAAtB,MAAY,CAAZ5C,YAAA,CAAYM,MAAA,c;;yDAAU,QAEjC,G;;;2EACAR,mBAAA,CAAyC,QAAzC+C,UAAyC,EAAR,GAAC,G;;UAGtC7C,YAAA,CAIkBiC,0BAAA;QAJDE,IAAI,EAAC,YAAY;QAACzB,KAAK,EAAC,MAAM;QAACwB,KAAK,EAAC;;QACzCG,OAAO,EAAAd,QAAA,CACsBe,KADf,K,kCACpBhC,MAAA,CAAAwC,UAAU,CAACR,KAAK,CAACC,GAAG,CAACQ,UAAU,kB;;UAGtC/C,YAAA,CAKkBiC,0BAAA;QALDvB,KAAK,EAAC,IAAI;QAACwB,KAAK,EAAC,KAAK;QAACc,KAAK,EAAC;;QACjCX,OAAO,EAAAd,QAAA,CACoEe,KAD7D,KACvBtC,YAAA,CAAoFgB,oBAAA;UAAzEC,IAAI,EAAC,SAAS;UAACwB,IAAI,EAAC,OAAO;UAAEvB,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAA2C,UAAU,CAACX,KAAK,CAACC,GAAG;;4BAAG,MAAEnB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DACxEpB,YAAA,CAAqFgB,oBAAA;UAA1EC,IAAI,EAAC,QAAQ;UAACwB,IAAI,EAAC,OAAO;UAAEvB,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAA4C,YAAY,CAACZ,KAAK,CAACC,GAAG;;4BAAG,MAAEnB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDArBLd,MAAA,CAAA6C,OAAO,E,GA0BjFjD,mBAAA,CAWM,OAXNkD,UAWM,GAVJpD,YAAA,CASEqD,wBAAA;MARAC,UAAU,EAAV,EAAU;MACVC,MAAM,EAAC,yCAAyC;MAC/C,cAAY,EAAEjD,MAAA,CAAAkD,WAAW;MACzB,YAAU,EAAE,iBAAiB;MAC7B,WAAS,EAAElD,MAAA,CAAAmD,QAAQ;MACnBC,KAAK,EAAEpD,MAAA,CAAAoD,KAAK;MACZC,YAAW,EAAErD,MAAA,CAAAsD,gBAAgB;MAC7BC,eAAc,EAAEvD,MAAA,CAAAwD;;;MAKvBC,mBAAA,gBAAmB,EACnB/D,YAAA,CA0CYgE,oBAAA;IAzCTpD,KAAK,EAAEN,MAAA,CAAA2D,WAAW;gBACV3D,MAAA,CAAA4D,aAAa;+DAAb5D,MAAA,CAAA4D,aAAa,GAAArD,MAAA;IACtBqB,KAAK,EAAC;;IAiCKiC,MAAM,EAAA5C,QAAA,CACf,MAGM,CAHNrB,mBAAA,CAGM,OAHNkE,UAGM,GAFJpE,YAAA,CAAwDgB,oBAAA;MAA5CE,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAAP,MAAA,IAAEP,MAAA,CAAA4D,aAAa;;wBAAU,MAAE9C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CpB,YAAA,CAAkFgB,oBAAA;MAAvEC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAA+D,UAAU;MAAGlB,OAAO,EAAE7C,MAAA,CAAAgE;;wBAAY,MAAElD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAlC1E,MA8BU,CA9BVpB,YAAA,CA8BUI,kBAAA;MA9BAC,KAAK,EAAEC,MAAA,CAAAiE,UAAU;MAAGC,KAAK,EAAElE,MAAA,CAAAmE,WAAW;MAAEC,GAAG,EAAC,eAAe;MAAC,aAAW,EAAC;;wBAChF,MAEe,CAFf1E,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAAsE,CAAtEnC,YAAA,CAAsEW,mBAAA;sBAAnDL,MAAA,CAAAiE,UAAU,CAAC3D,KAAK;qEAAhBN,MAAA,CAAAiE,UAAU,CAAC3D,KAAK,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAGnDd,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAAqG,CAArGnC,YAAA,CAAqGW,mBAAA;UAA3FM,IAAI,EAAC,UAAU;sBAAUX,MAAA,CAAAiE,UAAU,CAACI,WAAW;qEAAtBrE,MAAA,CAAAiE,UAAU,CAACI,WAAW,GAAA9D,MAAA;UAAEC,WAAW,EAAC,SAAS;UAAC8D,IAAI,EAAC;;;UAGxF5E,YAAA,CAoBeS,uBAAA;QApBDC,KAAK,EAAC;MAAM;0BACxB,MAkBY,CAlBZV,YAAA,CAkBY6E,oBAAA;UAjBVhF,KAAK,EAAC,iBAAiB;UACtBiF,MAAM,EAAExE,MAAA,CAAAyE,YAAY;UACpBC,OAAO,EAAE1E,MAAA,CAAA2E,aAAa;UACtB,cAAY,EAAE3E,MAAA,CAAA4E,mBAAmB;UACjC,YAAU,EAAE5E,MAAA,CAAA6E,aAAa;UACzB,WAAS,EAAE7E,MAAA,CAAA8E,YAAY;UACvB,YAAU,EAAE9E,MAAA,CAAA+E,mBAAmB;UAC/B,eAAa,EAAE/E,MAAA,CAAAgF,YAAY;UAC3BC,KAAK,EAAE,CAAC;UACR,WAAS,EAAEjF,MAAA,CAAAkF;;UAGDC,GAAG,EAAAlE,QAAA,CACZ,MAEMH,MAAA,SAAAA,MAAA,QAFNlB,mBAAA,CAEM;YAFDL,KAAK,EAAC;UAAgB,GAAC,mCAE5B,mB;4BAJF,MAA0C,CAA1CG,YAAA,CAA0CgB,oBAAA;YAA/BC,IAAI,EAAC;UAAS;8BAAC,MAAIG,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
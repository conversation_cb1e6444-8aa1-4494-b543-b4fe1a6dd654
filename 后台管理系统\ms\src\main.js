import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import api from './utils/api'
import axios from 'axios'

// Import Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// Import icons
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 配置全局axios默认值
axios.defaults.baseURL = 'http://localhost:3000'

// 初始化检查token
const token = localStorage.getItem('token')
if (token) {
  // 检查token是否有效
  try {
    // JWT格式: header.payload.signature
    const payloadBase64 = token.split('.')[1]
    if (!payloadBase64) {
      // 格式不正确，清除token
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('userRole')
      localStorage.removeItem('studentId')
    } else {
      const decodedPayload = JSON.parse(atob(payloadBase64))
      const expiration = decodedPayload.exp * 1000 // 转换为毫秒

      // 检查是否过期
      if (Date.now() >= expiration) {
        // token已过期，清除相关信息
        localStorage.removeItem('token')
        localStorage.removeItem('userId')
        localStorage.removeItem('userRole')
        localStorage.removeItem('studentId')
        
        // 如果后端支持刷新token，可以尝试刷新
        // api.refreshToken()
      }
    }
  } catch (error) {
    console.error('解析token失败:', error)
    // 解析错误，清除token
    localStorage.removeItem('token')
    localStorage.removeItem('userId')
    localStorage.removeItem('userRole')
    localStorage.removeItem('studentId')
  }
}

const app = createApp(App)

// 全局API实例
app.config.globalProperties.$api = api
app.config.globalProperties.$axios = axios

// Register all icons globally
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(store).use(router).use(ElementPlus).mount('#app')

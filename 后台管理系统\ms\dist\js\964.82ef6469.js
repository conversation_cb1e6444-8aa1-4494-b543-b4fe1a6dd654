"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[964],{9964:function(e,a,l){l.r(a),l.d(a,{default:function(){return R}});l(4114);var r=l(6768),o=l(144),s=l(3153),t=l(1387),d=l(1219),u=l(7477),n=l(4373);const i={class:"login-container"},m={class:"login-card"},c={class:"login-info"},p={class:"feature-list"},g={class:"feature-item"},f={class:"feature-icon"},b={class:"feature-item"},k={class:"feature-icon"},v={class:"feature-item"},w={class:"feature-icon"},h={class:"login-form-wrapper"},_={class:"login-form-container"},F={class:"dialog-footer"},V={class:"dialog-footer"};var y={__name:"LoginView",setup(e){n.A.interceptors.request.use(e=>{const a=localStorage.getItem("token");return a&&(e.headers.Authorization=`Bearer ${a}`),e},e=>Promise.reject(e)),n.A.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("token"),localStorage.removeItem("userInfo"),"/login"!==window.location.pathname&&(d.nk.error("登录已过期，请重新登录"),window.location.href="/#/login")),Promise.reject(e)));const a=(0,t.rd)(),l=(0,o.KR)(null),y=(0,o.KR)(!1),I={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL||"http://localhost:3000/api",L=(0,o.Kh)({username:"",password:"",remember:!1}),R={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}]},U=async()=>{if(l.value)try{await l.value.validate(async e=>{if(e){y.value=!0;try{const e=await n.A.post(`${I}/auth/login`,{username:L.username,password:L.password}),{token:r,data:o}=e.data;if(localStorage.setItem("token",r),L.remember?localStorage.setItem("rememberedUsername",L.username):localStorage.removeItem("rememberedUsername"),localStorage.setItem("userInfo",JSON.stringify(o)),localStorage.setItem("userId",o.id),localStorage.setItem("userRole",o.role),o.student_id)localStorage.setItem("studentId",o.student_id),console.log("保存学生ID:",o.student_id);else if("student"===o.role)try{const e=await n.A.get(`${I}/students/by-user/${o.id}`);e.data.success&&e.data.data?(localStorage.setItem("studentId",e.data.data.id),console.log("通过用户ID获取并保存学生ID:",e.data.data.id)):(console.error("无法获取学生ID，但用户角色为学生"),d.nk.warning("无法获取您的学生信息，部分功能可能无法使用"))}catch(l){console.error("获取学生信息失败:",l)}console.log("登录成功，用户信息:",o),d.nk.success("登录成功"),"student"===o.role?a.push("/courses/list"):"admin"===o.role||"teacher"===o.role?a.push("/students/list"):a.push("/dashboard")}catch(r){console.error("登录失败:",r),d.nk.error(r.response?.data?.message||"登录失败，请检查用户名和密码")}finally{y.value=!1}}})}catch(e){y.value=!1,d.nk.error("表单验证失败")}},x=(0,o.KR)(!1),S=(0,o.KR)(null),P=(0,o.KR)(!1),A=(0,o.Kh)({username:"",password:"",confirmPassword:"",name:"",email:"",phone:"",student_id:"",role:"user"}),C=(e,a,l)=>{""===a?l(new Error("请再次输入密码")):a!==A.password?l(new Error("两次输入密码不一致!")):l()},K={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:C,trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},q=async()=>{if(S.value)try{await S.value.validate(async e=>{if(e){P.value=!0;try{const{confirmPassword:e,...a}=A;await n.A.post(`${I}/auth/register`,a);d.nk.success("注册成功，请登录"),x.value=!1,L.username=A.username,L.password=""}catch(a){console.error("注册失败:",a),d.nk.error(a.response?.data?.message||"注册失败，请稍后重试")}finally{P.value=!1}}})}catch(e){P.value=!1,d.nk.error("表单验证失败")}},E=(0,o.KR)(!1),$=(0,o.KR)(null),D=(0,o.KR)(!1),W=(0,o.Kh)({username:"",email:""}),N={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},j=async()=>{if($.value)try{await $.value.validate(async e=>{if(e){D.value=!0;try{await n.A.post(`${I}/auth/forgot-password`,W);d.nk.success("重置密码链接已发送到您的邮箱"),E.value=!1}catch(a){console.error("忘记密码请求失败:",a),d.nk.error(a.response?.data?.message||"操作失败，请稍后重试")}finally{D.value=!1}}})}catch(e){D.value=!1,d.nk.error("表单验证失败")}},B=()=>{const e=localStorage.getItem("rememberedUsername");e&&(L.username=e,L.remember=!0)};return B(),(e,a)=>{const t=(0,r.g2)("el-icon"),d=(0,r.g2)("el-input"),n=(0,r.g2)("el-form-item"),I=(0,r.g2)("el-button"),C=(0,r.g2)("el-form"),B=(0,r.g2)("el-dialog");return(0,r.uX)(),(0,r.CE)("div",i,[(0,r.Lk)("div",m,[(0,r.Lk)("div",c,[a[17]||(a[17]=(0,r.Fv)('<div class="logo-wrapper" data-v-5209985f><div class="logo-icon" data-v-5209985f><img src="'+s+'" alt="Logo" class="logo-img" data-v-5209985f><i class="el-icon-monitor" data-v-5209985f></i></div><div class="logo-text" data-v-5209985f> 实习生学籍管理系统 </div></div><div class="welcome-text" data-v-5209985f><h2 data-v-5209985f>欢迎回来</h2><p data-v-5209985f>登录您的账户以继续访问系统</p></div>',2)),(0,r.Lk)("div",p,[(0,r.Lk)("div",g,[(0,r.Lk)("div",f,[(0,r.bF)(t,null,{default:(0,r.k6)(()=>[(0,r.bF)((0,o.R1)(u.Check))]),_:1})]),a[14]||(a[14]=(0,r.Lk)("div",{class:"feature-text"},"现代化的管理界面",-1))]),(0,r.Lk)("div",b,[(0,r.Lk)("div",k,[(0,r.bF)(t,null,{default:(0,r.k6)(()=>[(0,r.bF)((0,o.R1)(u.Check))]),_:1})]),a[15]||(a[15]=(0,r.Lk)("div",{class:"feature-text"},"强大的功能模块",-1))]),(0,r.Lk)("div",v,[(0,r.Lk)("div",w,[(0,r.bF)(t,null,{default:(0,r.k6)(()=>[(0,r.bF)((0,o.R1)(u.Check))]),_:1})]),a[16]||(a[16]=(0,r.Lk)("div",{class:"feature-text"},"安全可靠的数据保护",-1))])])]),(0,r.Lk)("div",h,[(0,r.Lk)("div",_,[a[19]||(a[19]=(0,r.Lk)("h2",{class:"form-title"},"用户登录",-1)),a[20]||(a[20]=(0,r.Lk)("p",{class:"form-subtitle"},"请输入您的账户信息",-1)),(0,r.bF)(C,{model:L,rules:R,ref_key:"loginFormRef",ref:l,class:"login-form"},{default:(0,r.k6)(()=>[(0,r.bF)(n,{prop:"username"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:L.username,"onUpdate:modelValue":a[0]||(a[0]=e=>L.username=e),placeholder:"输入账号","prefix-icon":(0,o.R1)(u.User)},null,8,["modelValue","prefix-icon"])]),_:1}),(0,r.bF)(n,{prop:"password"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:L.password,"onUpdate:modelValue":a[1]||(a[1]=e=>L.password=e),type:"password",placeholder:"输入密码","prefix-icon":(0,o.R1)(u.Lock),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),(0,r.bF)(n,null,{default:(0,r.k6)(()=>[(0,r.bF)(I,{type:"primary",loading:y.value,onClick:U,class:"login-button"},{default:(0,r.k6)(()=>a[18]||(a[18]=[(0,r.eW)(" 登录 ")])),_:1,__:[18]},8,["loading"])]),_:1})]),_:1},8,["model"])])])]),(0,r.bF)(B,{title:"用户注册",modelValue:x.value,"onUpdate:modelValue":a[9]||(a[9]=e=>x.value=e),width:"400px",center:"","destroy-on-close":""},{footer:(0,r.k6)(()=>[(0,r.Lk)("span",F,[(0,r.bF)(I,{onClick:a[8]||(a[8]=e=>x.value=!1)},{default:(0,r.k6)(()=>a[21]||(a[21]=[(0,r.eW)("取消")])),_:1,__:[21]}),(0,r.bF)(I,{type:"primary",loading:P.value,onClick:q},{default:(0,r.k6)(()=>a[22]||(a[22]=[(0,r.eW)("注册")])),_:1,__:[22]},8,["loading"])])]),default:(0,r.k6)(()=>[(0,r.bF)(C,{model:A,rules:K,ref_key:"registerFormRef",ref:S,"label-width":"80px"},{default:(0,r.k6)(()=>[(0,r.bF)(n,{label:"用户名",prop:"username"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:A.username,"onUpdate:modelValue":a[2]||(a[2]=e=>A.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),(0,r.bF)(n,{label:"密码",prop:"password"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:A.password,"onUpdate:modelValue":a[3]||(a[3]=e=>A.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}),(0,r.bF)(n,{label:"确认密码",prop:"confirmPassword"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:A.confirmPassword,"onUpdate:modelValue":a[4]||(a[4]=e=>A.confirmPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1}),(0,r.bF)(n,{label:"姓名",prop:"name"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:A.name,"onUpdate:modelValue":a[5]||(a[5]=e=>A.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),(0,r.bF)(n,{label:"邮箱",prop:"email"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:A.email,"onUpdate:modelValue":a[6]||(a[6]=e=>A.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),(0,r.bF)(n,{label:"手机号",prop:"phone"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:A.phone,"onUpdate:modelValue":a[7]||(a[7]=e=>A.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),(0,r.bF)(B,{title:"忘记密码",modelValue:E.value,"onUpdate:modelValue":a[13]||(a[13]=e=>E.value=e),width:"400px",center:"","destroy-on-close":""},{footer:(0,r.k6)(()=>[(0,r.Lk)("span",V,[(0,r.bF)(I,{onClick:a[12]||(a[12]=e=>E.value=!1)},{default:(0,r.k6)(()=>a[23]||(a[23]=[(0,r.eW)("取消")])),_:1,__:[23]}),(0,r.bF)(I,{type:"primary",loading:D.value,onClick:j},{default:(0,r.k6)(()=>a[24]||(a[24]=[(0,r.eW)("提交")])),_:1,__:[24]},8,["loading"])])]),default:(0,r.k6)(()=>[(0,r.bF)(C,{model:W,rules:N,ref_key:"forgotPasswordFormRef",ref:$,"label-width":"80px"},{default:(0,r.k6)(()=>[(0,r.bF)(n,{label:"用户名",prop:"username"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:W.username,"onUpdate:modelValue":a[10]||(a[10]=e=>W.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),(0,r.bF)(n,{label:"邮箱",prop:"email"},{default:(0,r.k6)(()=>[(0,r.bF)(d,{modelValue:W.email,"onUpdate:modelValue":a[11]||(a[11]=e=>W.email=e),placeholder:"请输入注册时的邮箱"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},I=l(1241);const L=(0,I.A)(y,[["__scopeId","data-v-5209985f"]]);var R=L}}]);
//# sourceMappingURL=964.82ef6469.js.map
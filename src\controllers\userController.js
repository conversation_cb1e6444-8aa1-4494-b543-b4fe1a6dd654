const User = require('../models/userModel');
const bcrypt = require('bcryptjs');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const util = require('util');
const unlinkFile = util.promisify(fs.unlink);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/users/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

// 文件过滤，只允许 Excel 文件
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    const ext = path.extname(file.originalname).toLowerCase();
    if (ext === '.xls' || ext === '.xlsx') {
      cb(null, true);
    } else {
      cb(new Error('文件扩展名必须是 .xls 或 .xlsx'), false);
    }
  } else {
    cb(new Error('仅支持 Excel 格式的文件'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

// 获取所有用户
exports.getAllUsers = async (req, res) => {
  try {
    // 从请求中获取过滤条件
    const filters = {
      username: req.query.username,
      phone: req.query.phone,
      status: req.query.status !== undefined ? parseInt(req.query.status) : undefined,
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10
    };
    
    // 获取总记录数（不分页）
    const countFilters = { ...filters };
    delete countFilters.page;
    delete countFilters.limit;
    const allUsers = await User.findAll(countFilters);
    const total = allUsers.length;
    
    // 获取分页后的数据
    const users = await User.findAll(filters);
    
    res.status(200).json({
      success: true,
      count: total,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
};

// 获取单个用户
exports.getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    const { username, password, role, name, email, phone, student_id } = req.body;
    
    // 验证必要字段
    if (!username || !password || !name) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名、密码和姓名'
      });
    }
    
    // 验证角色是否有效
    const validRoles = ['admin', 'teacher', 'student'];
    if (role && !validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: '无效的角色，角色必须是 admin、teacher 或 student'
      });
    }
    
    // 检查用户名是否已存在
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      });
    }
    
    // 创建用户
    const userId = await User.create({
      username,
      password,
      role: role || 'student',
      name,
      email,
      phone,
      student_id
    });
    
    // 获取创建的用户信息
    const user = await User.findById(userId);
    
    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
};

// 更新用户
exports.updateUser = async (req, res) => {
  try {
    const { role, name, email, phone, student_id, password } = req.body;
    
    // 获取当前用户信息，确保我们有完整的数据
    const currentUser = await User.findById(req.params.id);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    // 验证角色是否有效
    const validRoles = ['admin', 'teacher', 'student'];
    // 使用当前角色作为默认值，防止role为null
    const updatedRole = role || currentUser.role;
    
    if (updatedRole && !validRoles.includes(updatedRole)) {
      return res.status(400).json({
        success: false,
        message: '无效的角色，角色必须是 admin、teacher 或 student'
      });
    }
    
    // 更新用户
    const success = await User.update(req.params.id, {
      role: updatedRole,
      name: name || currentUser.name,
      email: email || currentUser.email,
      phone: phone || currentUser.phone,
      student_id: student_id || currentUser.student_id,
      password: password // 密码可以为空，表示不更新密码
    });
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户或更新失败'
      });
    }
    
    // 获取更新后的用户信息
    const user = await User.findById(req.params.id);
    
    res.status(200).json({
      success: true,
      message: '用户信息更新成功',
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新用户失败',
      error: error.message
    });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const success = await User.delete(req.params.id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户或删除失败'
      });
    }
    
    res.status(200).json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
};

// 批量删除用户
exports.batchDeleteUsers = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的用户ID数组'
      });
    }
    
    let successCount = 0;
    let failCount = 0;
    
    // 逐个删除用户
    for (const id of ids) {
      try {
        const success = await User.delete(id);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        failCount++;
      }
    }
    
    res.status(200).json({
      success: true,
      message: `成功删除 ${successCount} 个用户，失败 ${failCount} 个`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '批量删除用户失败',
      error: error.message
    });
  }
}; 

// 更新用户状态
exports.updateUserStatus = async (req, res) => {
  try {
    const userId = req.params.id;
    const { status } = req.body;
    
    // 确保状态是数字类型
    const statusNum = parseInt(status, 10);
    
    // 验证状态值
    if (statusNum !== 0 && statusNum !== 1) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值，状态必须是 0（禁用）或 1（启用）'
      });
    }
    
    // 获取当前用户信息
    const currentUser = await User.findById(userId);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    // 只更新状态字段
    const success = await User.update(userId, {
      ...currentUser,
      status: statusNum
    });
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户或更新失败'
      });
    }
    
    res.status(200).json({
      success: true,
      message: statusNum === 1 ? '用户已启用' : '用户已禁用',
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新用户状态失败',
      error: error.message
    });
  }
};

// 批量导入用户信息
exports.importUsers = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      });
    }

    // 动态导入xlsx库
    let XLSX;
    try {
      XLSX = require('xlsx');
    } catch (error) {
      await unlinkFile(req.file.path);
      return res.status(500).json({
        success: false,
        message: '服务器缺少Excel处理库，请联系管理员安装xlsx依赖'
      });
    }

    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(req.file.path);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表转换为JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // 删除上传的文件
      await unlinkFile(req.file.path);

      if (jsonData.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Excel文件中没有数据'
        });
      }

      // 验证和转换数据
      const validUsers = [];
      const errors = [];

      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];
        const rowNum = i + 2; // Excel行号从2开始（第1行是标题）

        try {
          // 验证必填字段
          const requiredFields = {
            '用户名': 'username',
            '密码': 'password',
            '姓名': 'name'
          };

          const userData = {};

          for (const [chineseField, englishField] of Object.entries(requiredFields)) {
            if (!row[chineseField] || row[chineseField].toString().trim() === '') {
              throw new Error(`第${rowNum}行缺少必填字段：${chineseField}`);
            }
            userData[englishField] = row[chineseField].toString().trim();
          }

          // 验证角色
          const roleMap = {
            '管理员': 'admin',
            '教师': 'teacher',
            '学生': 'student'
          };

          const roleValue = row['角色'] ? row['角色'].toString().trim() : '学生';
          if (!roleMap[roleValue]) {
            throw new Error(`第${rowNum}行角色必须是"管理员"、"教师"或"学生"`);
          }
          userData.role = roleMap[roleValue];

          // 处理可选字段
          userData.email = row['邮箱'] ? row['邮箱'].toString().trim() : '';
          userData.phone = row['电话'] ? row['电话'].toString().trim() : '';

          // 注意：用户导入时不关联实习生，所以不处理student_id字段
          userData.student_id = null;

          validUsers.push(userData);

        } catch (error) {
          errors.push(error.message);
        }
      }

      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors
        });
      }

      // 批量插入数据库
      const insertedUsers = [];
      const insertErrors = [];

      for (let i = 0; i < validUsers.length; i++) {
        try {
          // 检查用户名是否已存在
          const existingUser = await User.findByUsername(validUsers[i].username);
          if (existingUser) {
            insertErrors.push(`第${i + 2}行用户名"${validUsers[i].username}"已存在`);
            continue;
          }

          const userId = await User.create(validUsers[i]);
          const user = await User.findById(userId);
          insertedUsers.push(user);
        } catch (error) {
          insertErrors.push(`第${i + 2}行插入失败：${error.message}`);
        }
      }

      res.status(201).json({
        success: true,
        message: `成功导入 ${insertedUsers.length} 条用户记录`,
        data: {
          imported: insertedUsers.length,
          total: jsonData.length,
          errors: insertErrors
        }
      });

    } catch (error) {
      // 确保删除临时文件
      try {
        await unlinkFile(req.file.path);
      } catch (unlinkError) {
        console.error('删除临时文件失败:', unlinkError);
      }

      throw error;
    }

  } catch (error) {
    console.error('导入用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '导入用户信息失败',
      error: error.message
    });
  }
};

module.exports = {
  upload
};
{"ast": null, "code": "import { defineComponent, getCurrentInstance, ref, computed, watch, onBeforeUnmount, provide, toRef, unref, resolveComponent, openBlock, createElementBlock, normalizeClass, createVNode, createSlots, withCtx, renderSlot, createBlock, mergeProps, createCommentVNode } from 'vue';\nimport { ElButton } from '../../button/index.mjs';\nimport { ElTooltip } from '../../tooltip/index.mjs';\nimport { ElScrollbar } from '../../scrollbar/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport ElRovingFocusGroup from '../../roving-focus-group/src/roving-focus-group2.mjs';\nimport { ArrowDown } from '@element-plus/icons-vue';\nimport { ElCollection, dropdownProps } from './dropdown.mjs';\nimport { DROPDOWN_INJECTION_KEY, DROPDOWN_INSTANCE_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { OnlyChild } from '../../slot/src/only-child.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { addUnit } from '../../../utils/dom/style.mjs';\nimport { castArray } from 'lodash-unified';\nimport { useId } from '../../../hooks/use-id/index.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nconst {\n  ButtonGroup: ElButtonGroup\n} = ElButton;\nconst _sfc_main = defineComponent({\n  name: \"ElDropdown\",\n  components: {\n    ElButton,\n    ElButtonGroup,\n    ElScrollbar,\n    ElDropdownCollection: ElCollection,\n    ElTooltip,\n    ElRovingFocusGroup,\n    ElOnlyChild: OnlyChild,\n    ElIcon,\n    ArrowDown\n  },\n  props: dropdownProps,\n  emits: [\"visible-change\", \"click\", \"command\"],\n  setup(props, {\n    emit\n  }) {\n    const _instance = getCurrentInstance();\n    const ns = useNamespace(\"dropdown\");\n    const {\n      t\n    } = useLocale();\n    const triggeringElementRef = ref();\n    const referenceElementRef = ref();\n    const popperRef = ref();\n    const contentRef = ref();\n    const scrollbar = ref(null);\n    const currentTabId = ref(null);\n    const isUsingKeyboard = ref(false);\n    const wrapStyle = computed(() => ({\n      maxHeight: addUnit(props.maxHeight)\n    }));\n    const dropdownTriggerKls = computed(() => [ns.m(dropdownSize.value)]);\n    const trigger = computed(() => castArray(props.trigger));\n    const defaultTriggerId = useId().value;\n    const triggerId = computed(() => props.id || defaultTriggerId);\n    watch([triggeringElementRef, trigger], ([triggeringElement, trigger2], [prevTriggeringElement]) => {\n      var _a, _b, _c;\n      if ((_a = prevTriggeringElement == null ? void 0 : prevTriggeringElement.$el) == null ? void 0 : _a.removeEventListener) {\n        prevTriggeringElement.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n      if ((_b = triggeringElement == null ? void 0 : triggeringElement.$el) == null ? void 0 : _b.removeEventListener) {\n        triggeringElement.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n      if (((_c = triggeringElement == null ? void 0 : triggeringElement.$el) == null ? void 0 : _c.addEventListener) && trigger2.includes(\"hover\")) {\n        triggeringElement.$el.addEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n    }, {\n      immediate: true\n    });\n    onBeforeUnmount(() => {\n      var _a, _b;\n      if ((_b = (_a = triggeringElementRef.value) == null ? void 0 : _a.$el) == null ? void 0 : _b.removeEventListener) {\n        triggeringElementRef.value.$el.removeEventListener(\"pointerenter\", onAutofocusTriggerEnter);\n      }\n    });\n    function handleClick() {\n      handleClose();\n    }\n    function handleClose() {\n      var _a;\n      (_a = popperRef.value) == null ? void 0 : _a.onClose();\n    }\n    function handleOpen() {\n      var _a;\n      (_a = popperRef.value) == null ? void 0 : _a.onOpen();\n    }\n    const dropdownSize = useFormSize();\n    function commandHandler(...args) {\n      emit(\"command\", ...args);\n    }\n    function onAutofocusTriggerEnter() {\n      var _a, _b;\n      (_b = (_a = triggeringElementRef.value) == null ? void 0 : _a.$el) == null ? void 0 : _b.focus();\n    }\n    function onItemEnter() {}\n    function onItemLeave() {\n      const contentEl = unref(contentRef);\n      trigger.value.includes(\"hover\") && (contentEl == null ? void 0 : contentEl.focus());\n      currentTabId.value = null;\n    }\n    function handleCurrentTabIdChange(id) {\n      currentTabId.value = id;\n    }\n    function handleEntryFocus(e) {\n      if (!isUsingKeyboard.value) {\n        e.preventDefault();\n        e.stopImmediatePropagation();\n      }\n    }\n    function handleBeforeShowTooltip() {\n      emit(\"visible-change\", true);\n    }\n    function handleShowTooltip(event) {\n      var _a;\n      if ((event == null ? void 0 : event.type) === \"keydown\") {\n        (_a = contentRef.value) == null ? void 0 : _a.focus();\n      }\n    }\n    function handleBeforeHideTooltip() {\n      emit(\"visible-change\", false);\n    }\n    provide(DROPDOWN_INJECTION_KEY, {\n      contentRef,\n      role: computed(() => props.role),\n      triggerId,\n      isUsingKeyboard,\n      onItemEnter,\n      onItemLeave\n    });\n    provide(DROPDOWN_INSTANCE_INJECTION_KEY, {\n      instance: _instance,\n      dropdownSize,\n      handleClick,\n      commandHandler,\n      trigger: toRef(props, \"trigger\"),\n      hideOnClick: toRef(props, \"hideOnClick\")\n    });\n    const onFocusAfterTrapped = e => {\n      var _a, _b;\n      e.preventDefault();\n      (_b = (_a = contentRef.value) == null ? void 0 : _a.focus) == null ? void 0 : _b.call(_a, {\n        preventScroll: true\n      });\n    };\n    const handlerMainButtonClick = event => {\n      emit(\"click\", event);\n    };\n    return {\n      t,\n      ns,\n      scrollbar,\n      wrapStyle,\n      dropdownTriggerKls,\n      dropdownSize,\n      triggerId,\n      currentTabId,\n      handleCurrentTabIdChange,\n      handlerMainButtonClick,\n      handleEntryFocus,\n      handleClose,\n      handleOpen,\n      handleBeforeShowTooltip,\n      handleShowTooltip,\n      handleBeforeHideTooltip,\n      onFocusAfterTrapped,\n      popperRef,\n      contentRef,\n      triggeringElementRef,\n      referenceElementRef\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _a;\n  const _component_el_dropdown_collection = resolveComponent(\"el-dropdown-collection\");\n  const _component_el_roving_focus_group = resolveComponent(\"el-roving-focus-group\");\n  const _component_el_scrollbar = resolveComponent(\"el-scrollbar\");\n  const _component_el_only_child = resolveComponent(\"el-only-child\");\n  const _component_el_tooltip = resolveComponent(\"el-tooltip\");\n  const _component_el_button = resolveComponent(\"el-button\");\n  const _component_arrow_down = resolveComponent(\"arrow-down\");\n  const _component_el_icon = resolveComponent(\"el-icon\");\n  const _component_el_button_group = resolveComponent(\"el-button-group\");\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([_ctx.ns.b(), _ctx.ns.is(\"disabled\", _ctx.disabled)])\n  }, [createVNode(_component_el_tooltip, {\n    ref: \"popperRef\",\n    role: _ctx.role,\n    effect: _ctx.effect,\n    \"fallback-placements\": [\"bottom\", \"top\"],\n    \"popper-options\": _ctx.popperOptions,\n    \"gpu-acceleration\": false,\n    \"hide-after\": _ctx.trigger === \"hover\" ? _ctx.hideTimeout : 0,\n    \"manual-mode\": true,\n    placement: _ctx.placement,\n    \"popper-class\": [_ctx.ns.e(\"popper\"), _ctx.popperClass],\n    \"reference-element\": (_a = _ctx.referenceElementRef) == null ? void 0 : _a.$el,\n    trigger: _ctx.trigger,\n    \"trigger-keys\": _ctx.triggerKeys,\n    \"trigger-target-el\": _ctx.contentRef,\n    \"show-after\": _ctx.trigger === \"hover\" ? _ctx.showTimeout : 0,\n    \"stop-popper-mouse-event\": false,\n    \"virtual-ref\": _ctx.triggeringElementRef,\n    \"virtual-triggering\": _ctx.splitButton,\n    disabled: _ctx.disabled,\n    transition: `${_ctx.ns.namespace.value}-zoom-in-top`,\n    teleported: _ctx.teleported,\n    pure: \"\",\n    persistent: _ctx.persistent,\n    onBeforeShow: _ctx.handleBeforeShowTooltip,\n    onShow: _ctx.handleShowTooltip,\n    onBeforeHide: _ctx.handleBeforeHideTooltip\n  }, createSlots({\n    content: withCtx(() => [createVNode(_component_el_scrollbar, {\n      ref: \"scrollbar\",\n      \"wrap-style\": _ctx.wrapStyle,\n      tag: \"div\",\n      \"view-class\": _ctx.ns.e(\"list\")\n    }, {\n      default: withCtx(() => [createVNode(_component_el_roving_focus_group, {\n        loop: _ctx.loop,\n        \"current-tab-id\": _ctx.currentTabId,\n        orientation: \"horizontal\",\n        onCurrentTabIdChange: _ctx.handleCurrentTabIdChange,\n        onEntryFocus: _ctx.handleEntryFocus\n      }, {\n        default: withCtx(() => [createVNode(_component_el_dropdown_collection, null, {\n          default: withCtx(() => [renderSlot(_ctx.$slots, \"dropdown\")]),\n          _: 3\n        })]),\n        _: 3\n      }, 8, [\"loop\", \"current-tab-id\", \"onCurrentTabIdChange\", \"onEntryFocus\"])]),\n      _: 3\n    }, 8, [\"wrap-style\", \"view-class\"])]),\n    _: 2\n  }, [!_ctx.splitButton ? {\n    name: \"default\",\n    fn: withCtx(() => [createVNode(_component_el_only_child, {\n      id: _ctx.triggerId,\n      ref: \"triggeringElementRef\",\n      role: \"button\",\n      tabindex: _ctx.tabindex\n    }, {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n      _: 3\n    }, 8, [\"id\", \"tabindex\"])])\n  } : void 0]), 1032, [\"role\", \"effect\", \"popper-options\", \"hide-after\", \"placement\", \"popper-class\", \"reference-element\", \"trigger\", \"trigger-keys\", \"trigger-target-el\", \"show-after\", \"virtual-ref\", \"virtual-triggering\", \"disabled\", \"transition\", \"teleported\", \"persistent\", \"onBeforeShow\", \"onShow\", \"onBeforeHide\"]), _ctx.splitButton ? (openBlock(), createBlock(_component_el_button_group, {\n    key: 0\n  }, {\n    default: withCtx(() => [createVNode(_component_el_button, mergeProps({\n      ref: \"referenceElementRef\"\n    }, _ctx.buttonProps, {\n      size: _ctx.dropdownSize,\n      type: _ctx.type,\n      disabled: _ctx.disabled,\n      tabindex: _ctx.tabindex,\n      onClick: _ctx.handlerMainButtonClick\n    }), {\n      default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n      _: 3\n    }, 16, [\"size\", \"type\", \"disabled\", \"tabindex\", \"onClick\"]), createVNode(_component_el_button, mergeProps({\n      id: _ctx.triggerId,\n      ref: \"triggeringElementRef\"\n    }, _ctx.buttonProps, {\n      role: \"button\",\n      size: _ctx.dropdownSize,\n      type: _ctx.type,\n      class: _ctx.ns.e(\"caret-button\"),\n      disabled: _ctx.disabled,\n      tabindex: _ctx.tabindex,\n      \"aria-label\": _ctx.t(\"el.dropdown.toggleDropdown\")\n    }), {\n      default: withCtx(() => [createVNode(_component_el_icon, {\n        class: normalizeClass(_ctx.ns.e(\"icon\"))\n      }, {\n        default: withCtx(() => [createVNode(_component_arrow_down)]),\n        _: 1\n      }, 8, [\"class\"])]),\n      _: 1\n    }, 16, [\"id\", \"size\", \"type\", \"class\", \"disabled\", \"tabindex\", \"aria-label\"])]),\n    _: 3\n  })) : createCommentVNode(\"v-if\", true)], 2);\n}\nvar Dropdown = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"dropdown.vue\"]]);\nexport { Dropdown as default };", "map": {"version": 3, "names": ["ButtonGroup", "ElButtonGroup", "ElButton", "_sfc_main", "defineComponent", "name", "components", "ElScrollbar", "ElDropdownCollection", "ElCollection", "ElTooltip", "ElRovingFocusGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ElIcon", "ArrowDown", "props", "dropdownProps", "emits", "setup", "emit", "_instance", "getCurrentInstance", "ns", "useNamespace", "t", "useLocale", "triggeringElementRef", "ref", "referenceElementRef", "popperRef", "contentRef", "scrollbar", "currentTabId", "isUsingKeyboard", "wrapStyle", "computed", "maxHeight", "addUnit", "dropdownTriggerKls", "m", "dropdownSize", "value", "trigger", "<PERSON><PERSON><PERSON><PERSON>", "defaultTriggerId", "useId", "triggerId", "id", "watch", "triggeringElement", "trigger2", "prevTriggeringElement", "_a", "_b", "_c", "$el", "removeEventListener", "onAutofocusTriggerEnter", "addEventListener", "includes", "immediate", "onBeforeUnmount", "handleClick", "handleClose", "onClose", "handleOpen", "onOpen", "useFormSize", "command<PERSON><PERSON>ler", "args", "focus", "onItemEnter", "onItemLeave", "contentEl", "unref", "handleCurrentTabIdChange", "handleEntryFocus", "e", "preventDefault", "stopImmediatePropagation", "handleBeforeShowTooltip", "handleShowTooltip", "event", "type", "handleBeforeHideTooltip", "provide", "DROPDOWN_INJECTION_KEY", "role", "DROPDOWN_INSTANCE_INJECTION_KEY", "instance", "toRef", "hideOnClick", "onFocusAfterTrapped", "call", "preventScroll", "handlerMainButtonClick", "_sfc_render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_dropdown_collection", "resolveComponent", "_component_el_roving_focus_group", "_component_el_scrollbar", "_component_el_only_child", "_component_el_tooltip", "_component_el_button", "popperOptions", "hideTimeout", "placement", "triggerKeys", "showTimeout", "splitButton", "disabled", "transition", "namespace", "teleported", "pure", "persistent", "onBeforeShow", "onShow", "onBeforeHide", "createSlots", "content", "withCtx", "createVNode", "tag", "default", "loop", "orientation", "onCurrentTabIdChange", "onEntryFocus", "$slots", "_", "openBlock", "createBlock", "_component_el_button_group", "key", "mergeProps", "buttonProps", "size", "tabindex", "class", "_component_el_icon", "_component_arrow_down", "createCommentVNode", "Dropdown", "_export_sfc"], "sources": ["../../../../../../packages/components/dropdown/src/dropdown.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('disabled', disabled)]\">\n    <el-tooltip\n      ref=\"popperRef\"\n      :role=\"role\"\n      :effect=\"effect\"\n      :fallback-placements=\"['bottom', 'top']\"\n      :popper-options=\"popperOptions\"\n      :gpu-acceleration=\"false\"\n      :hide-after=\"trigger === 'hover' ? hideTimeout : 0\"\n      :manual-mode=\"true\"\n      :placement=\"placement\"\n      :popper-class=\"[ns.e('popper'), popperClass]\"\n      :reference-element=\"referenceElementRef?.$el\"\n      :trigger=\"trigger\"\n      :trigger-keys=\"triggerKeys\"\n      :trigger-target-el=\"contentRef\"\n      :show-after=\"trigger === 'hover' ? showTimeout : 0\"\n      :stop-popper-mouse-event=\"false\"\n      :virtual-ref=\"triggeringElementRef\"\n      :virtual-triggering=\"splitButton\"\n      :disabled=\"disabled\"\n      :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n      :teleported=\"teleported\"\n      pure\n      :persistent=\"persistent\"\n      @before-show=\"handleBeforeShowTooltip\"\n      @show=\"handleShowTooltip\"\n      @before-hide=\"handleBeforeHideTooltip\"\n    >\n      <template #content>\n        <el-scrollbar\n          ref=\"scrollbar\"\n          :wrap-style=\"wrapStyle\"\n          tag=\"div\"\n          :view-class=\"ns.e('list')\"\n        >\n          <el-roving-focus-group\n            :loop=\"loop\"\n            :current-tab-id=\"currentTabId\"\n            orientation=\"horizontal\"\n            @current-tab-id-change=\"handleCurrentTabIdChange\"\n            @entry-focus=\"handleEntryFocus\"\n          >\n            <el-dropdown-collection>\n              <slot name=\"dropdown\" />\n            </el-dropdown-collection>\n          </el-roving-focus-group>\n        </el-scrollbar>\n      </template>\n      <template v-if=\"!splitButton\" #default>\n        <el-only-child\n          :id=\"triggerId\"\n          ref=\"triggeringElementRef\"\n          role=\"button\"\n          :tabindex=\"tabindex\"\n        >\n          <slot name=\"default\" />\n        </el-only-child>\n      </template>\n    </el-tooltip>\n    <template v-if=\"splitButton\">\n      <el-button-group>\n        <el-button\n          ref=\"referenceElementRef\"\n          v-bind=\"buttonProps\"\n          :size=\"dropdownSize\"\n          :type=\"type\"\n          :disabled=\"disabled\"\n          :tabindex=\"tabindex\"\n          @click=\"handlerMainButtonClick\"\n        >\n          <slot name=\"default\" />\n        </el-button>\n        <el-button\n          :id=\"triggerId\"\n          ref=\"triggeringElementRef\"\n          v-bind=\"buttonProps\"\n          role=\"button\"\n          :size=\"dropdownSize\"\n          :type=\"type\"\n          :class=\"ns.e('caret-button')\"\n          :disabled=\"disabled\"\n          :tabindex=\"tabindex\"\n          :aria-label=\"t('el.dropdown.toggleDropdown')\"\n        >\n          <el-icon :class=\"ns.e('icon')\"><arrow-down /></el-icon>\n        </el-button>\n      </el-button-group>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onBeforeUnmount,\n  provide,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport ElButton from '@element-plus/components/button'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElIcon from '@element-plus/components/icon'\nimport ElRovingFocusGroup from '@element-plus/components/roving-focus-group'\nimport { ElOnlyChild } from '@element-plus/components/slot'\nimport { useFormSize } from '@element-plus/components/form'\nimport { addUnit, ensureArray } from '@element-plus/utils'\nimport { ArrowDown } from '@element-plus/icons-vue'\nimport { useId, useLocale, useNamespace } from '@element-plus/hooks'\nimport { ElCollection as ElDropdownCollection, dropdownProps } from './dropdown'\nimport {\n  DROPDOWN_INJECTION_KEY,\n  DROPDOWN_INSTANCE_INJECTION_KEY,\n} from './tokens'\n\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { CSSProperties } from 'vue'\n\nconst { ButtonGroup: ElButtonGroup } = ElButton\n\nexport default defineComponent({\n  name: 'ElDropdown',\n  components: {\n    ElButton,\n    ElButtonGroup,\n    ElScrollbar,\n    ElDropdownCollection,\n    ElTooltip,\n    ElRovingFocusGroup,\n    ElOnlyChild,\n    ElIcon,\n    ArrowDown,\n  },\n  props: dropdownProps,\n  emits: ['visible-change', 'click', 'command'],\n  setup(props, { emit }) {\n    const _instance = getCurrentInstance()\n    const ns = useNamespace('dropdown')\n    const { t } = useLocale()\n\n    const triggeringElementRef = ref()\n    const referenceElementRef = ref()\n    const popperRef = ref<TooltipInstance>()\n    const contentRef = ref<HTMLElement>()\n    const scrollbar = ref(null)\n    const currentTabId = ref<string | null>(null)\n    const isUsingKeyboard = ref(false)\n\n    const wrapStyle = computed<CSSProperties>(() => ({\n      maxHeight: addUnit(props.maxHeight),\n    }))\n    const dropdownTriggerKls = computed(() => [ns.m(dropdownSize.value)])\n    const trigger = computed(() => ensureArray(props.trigger))\n\n    const defaultTriggerId = useId().value\n    const triggerId = computed<string>(() => props.id || defaultTriggerId)\n\n    // The goal of this code is to focus on the tooltip triggering element when it is hovered.\n    // This is a temporary fix for where closing the dropdown through pointerleave event focuses on a\n    // completely different element. For a permanent solution, remove all calls to any \"element.focus()\"\n    // that are triggered through pointer enter/leave events.\n    watch(\n      [triggeringElementRef, trigger],\n      ([triggeringElement, trigger], [prevTriggeringElement]) => {\n        if (prevTriggeringElement?.$el?.removeEventListener) {\n          prevTriggeringElement.$el.removeEventListener(\n            'pointerenter',\n            onAutofocusTriggerEnter\n          )\n        }\n        if (triggeringElement?.$el?.removeEventListener) {\n          triggeringElement.$el.removeEventListener(\n            'pointerenter',\n            onAutofocusTriggerEnter\n          )\n        }\n        if (\n          triggeringElement?.$el?.addEventListener &&\n          trigger.includes('hover')\n        ) {\n          triggeringElement.$el.addEventListener(\n            'pointerenter',\n            onAutofocusTriggerEnter\n          )\n        }\n      },\n      { immediate: true }\n    )\n\n    onBeforeUnmount(() => {\n      if (triggeringElementRef.value?.$el?.removeEventListener) {\n        triggeringElementRef.value.$el.removeEventListener(\n          'pointerenter',\n          onAutofocusTriggerEnter\n        )\n      }\n    })\n\n    function handleClick() {\n      handleClose()\n    }\n\n    function handleClose() {\n      popperRef.value?.onClose()\n    }\n\n    function handleOpen() {\n      popperRef.value?.onOpen()\n    }\n\n    const dropdownSize = useFormSize()\n\n    function commandHandler(...args: any[]) {\n      emit('command', ...args)\n    }\n\n    function onAutofocusTriggerEnter() {\n      triggeringElementRef.value?.$el?.focus()\n    }\n\n    function onItemEnter() {\n      // NOOP for now\n    }\n\n    function onItemLeave() {\n      const contentEl = unref(contentRef)\n\n      trigger.value.includes('hover') && contentEl?.focus()\n      currentTabId.value = null\n    }\n\n    function handleCurrentTabIdChange(id: string) {\n      currentTabId.value = id\n    }\n\n    function handleEntryFocus(e: Event) {\n      if (!isUsingKeyboard.value) {\n        e.preventDefault()\n        e.stopImmediatePropagation()\n      }\n    }\n\n    function handleBeforeShowTooltip() {\n      emit('visible-change', true)\n    }\n\n    function handleShowTooltip(event?: Event) {\n      if (event?.type === 'keydown') {\n        contentRef.value?.focus()\n      }\n    }\n\n    function handleBeforeHideTooltip() {\n      emit('visible-change', false)\n    }\n\n    provide(DROPDOWN_INJECTION_KEY, {\n      contentRef,\n      role: computed(() => props.role),\n      triggerId,\n      isUsingKeyboard,\n      onItemEnter,\n      onItemLeave,\n    })\n\n    provide(DROPDOWN_INSTANCE_INJECTION_KEY, {\n      instance: _instance,\n      dropdownSize,\n      handleClick,\n      commandHandler,\n      trigger: toRef(props, 'trigger'),\n      hideOnClick: toRef(props, 'hideOnClick'),\n    })\n\n    const onFocusAfterTrapped = (e: Event) => {\n      e.preventDefault()\n      contentRef.value?.focus?.({\n        preventScroll: true,\n      })\n    }\n\n    const handlerMainButtonClick = (event: MouseEvent) => {\n      emit('click', event)\n    }\n\n    return {\n      t,\n      ns,\n      scrollbar,\n      wrapStyle,\n      dropdownTriggerKls,\n      dropdownSize,\n      triggerId,\n      currentTabId,\n      handleCurrentTabIdChange,\n      handlerMainButtonClick,\n      handleEntryFocus,\n      handleClose,\n      handleOpen,\n      handleBeforeShowTooltip,\n      handleShowTooltip,\n      handleBeforeHideTooltip,\n      onFocusAfterTrapped,\n      popperRef,\n      contentRef,\n      triggeringElementRef,\n      referenceElementRef,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;AA4HA,MAAM;EAAEA,WAAa,EAAAC;AAAA,CAAkB,GAAAC,QAAA;AAEvC,MAAKC,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,UAAY;IACVJ,QAAA;IACAD,aAAA;IACAM,WAAA;IAAAC,oBAAA,EACAC,YAAA;IACAC,SAAA;IACAC,kBAAA;IAAAC,WAAA,EACAC,SAAA;IACAC,MAAA;IACAC;EAAA,CACF;EACAC,KAAO,EAAAC,aAAA;EACPC,KAAO,GAAC,gBAAkB,WAAS,SAAS;EAC5CC,KAAMA,CAAAH,KAAA,EAAO;IAAEI;EAAA,CAAQ;IACrB,MAAMC,SAAA,GAAYC,kBAAmB;IAC/B,MAAAC,EAAA,GAAKC,YAAA,CAAa,UAAU;IAC5B;MAAEC;IAAE,IAAIC,SAAU;IAExB,MAAMC,oBAAA,GAAuBC,GAAI;IACjC,MAAMC,mBAAA,GAAsBD,GAAI;IAChC,MAAME,SAAA,GAAYF,GAAqB;IACvC,MAAMG,UAAA,GAAaH,GAAiB;IAC9B,MAAAI,SAAA,GAAYJ,GAAA,CAAI,IAAI;IACpB,MAAAK,YAAA,GAAeL,GAAA,CAAmB,IAAI;IACtC,MAAAM,eAAA,GAAkBN,GAAA,CAAI,KAAK;IAE3B,MAAAO,SAAA,GAAYC,QAAA,CAAwB,OAAO;MAC/CC,SAAA,EAAWC,OAAQ,CAAAtB,KAAA,CAAMqB,SAAS;IAAA,CAClC;IACI,MAAAE,kBAAA,GAAqBH,QAAA,CAAS,MAAM,CAACb,EAAA,CAAGiB,CAAE,CAAAC,YAAA,CAAaC,KAAK,CAAC,CAAC;IACpE,MAAMC,OAAA,GAAUP,QAAS,OAAMQ,SAAY,CAAA5B,KAAA,CAAM2B,OAAO,CAAC;IAEnD,MAAAE,gBAAA,GAAmBC,KAAA,EAAQ,CAAAJ,KAAA;IACjC,MAAMK,SAAY,GAAAX,QAAA,CAAiB,MAAMpB,KAAA,CAAMgC,EAAA,IAAMH,gBAAgB;IAMrEI,KAAA,EAAAtB,oBAAA,EAAAgB,OAAA,KAAAO,iBAAA,EAAAC,QAAA,IAAAC,qBAAA;MACE,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;MACA,IAAE,CAAAF,EAAA,GAAAD,qBAA6B,oBAAAA,qBAA4B,CAAAI,GAAA,qBAAAH,EAAA,CAAAI,mBAAA;QACrDL,qBAAA,CAAAI,GAAA,CAAAC,mBAAiD,iBAAAC,uBAAA;MACnD;MACE,KAAAJ,EAAA,GAAAJ,iBAAA,oBAAAA,iBAAA,CAAAM,GAAA,qBAAAF,EAAA,CAAAG,mBAAA;QACAP,iBAAA,CAAAM,GAAA,CAAAC,mBAAA,iBAAAC,uBAAA;MAAA;MAEJ,MAAAH,EAAA,GAAAL,iBAAA,oBAAAA,iBAAA,CAAAM,GAAA,qBAAAD,EAAA,CAAAI,gBAAA,KAAAR,QAAA,CAAAS,QAAA;QACIV,iBAAA,CAAAM,GAAA,CAAAG,gBAA6C,iBAAAD,uBAAA;MAC/C;IAAsB,CACpB;MAAAG,SAAA;IAAA;IACAC,eAAA;MACF,IAAAT,EAAA,EAAAC,EAAA;MACF,KAAAA,EAAA,IAAAD,EAAA,GAAA1B,oBAAA,CAAAe,KAAA,qBAAAW,EAAA,CAAAG,GAAA,qBAAAF,EAAA,CAAAG,mBAAA;QACA9B,oBAAA,CAAAe,KAC0B,CAAAc,GAAA,CAAAC,mBAChB,iBAAAC,uBACR;MACA;IAAsB,CACpB;IACA,SAAAK,YAAA;MACFC,WAAA;IAAA;IAEJ,SAAAA,YAAA;MACA,IAAAX,EAAA;MACF,CAAAA,EAAA,GAAAvB,SAAA,CAAAY,KAAA,qBAAAW,EAAA,CAAAY,OAAA;IAEA;IACM,SAAAC,WAAA;MACF,IAAAb,EAAA;MACE,CAAAA,EAAA,GAAAvB,SAAA,CAAAY,KAAA,qBAAAW,EAAA,CAAAc,MAAA;IAAA;IAEF,MAAA1B,YAAA,GAAA2B,WAAA;IACF,SAAAC,eAAA,GAAAC,IAAA;MACDlD,IAAA,eAAAkD,IAAA;IAED;IACc,SAAAZ,wBAAA;MACd,IAAAL,EAAA,EAAAC,EAAA;MAEA,CAAAA,EAAA,IAASD,EAAc,GAAA1B,oBAAA,CAAAe,KAAA,qBAAAW,EAAA,CAAAG,GAAA,qBAAAF,EAAA,CAAAiB,KAAA;IACrB;IACF,SAAAC,YAAA,GAEA;IACE,SAAAC,WAAwBA,CAAA;MAC1B,MAAAC,SAAA,GAAAC,KAAA,CAAA5C,UAAA;MAEAY,OAAA,CAAAD,KAAA,CAAAkB,QAAiC,cAAAc,SAAA,oBAAAA,SAAA,CAAAH,KAAA;MAEjCtC,YAAA,CAAAS,KAAA,OAA2B;IACzB;IACF,SAAAkC,yBAAA5B,EAAA;MAEAf,YAAmC,CAAAS,KAAA,GAAAM,EAAA;IACjC;IACF,SAAA6B,iBAAAC,CAAA;MAEA,KAAA5C,eAAuB,CAAAQ,KAAA;QAEvBoC,CAAA,CAAAC,cAAA;QAEAD,CAAA,CAAAE,wBAAuB;MACrB;IAEA;IACA,SAAAC,uBAAqBA,CAAA;MACvB7D,IAAA;IAEA;IACE,SAAA8D,iBAAqBA,CAAAC,KAAA;MACvB,IAAA9B,EAAA;MAEA,KAAA8B,KAAA,gBAAoC,IAAAA,KAAA,CAAAC,IAAA;QAC9B,CAAA/B,EAAC,GAAAtB,UAAA,CAAAW,KAAuB,qBAAAW,EAAA,CAAAkB,KAAA;MAC1B;IACA;IACF,SAAAc,wBAAA;MACFjE,IAAA;IAEA;IACEkE,OAAK,CAAAC,sBAAsB;MAC7BxD,UAAA;MAEAyD,IAAA,EAAApD,QAAA,OAAApB,KAA0C,CAAAwE,IAAA;MACpCzC,SAAA;MACFb,eAAA;MACFsC,WAAA;MACFC;IAEA;IACEa,OAAK,CAAAG,+BAAuB;MAC9BC,QAAA,EAAArE,SAAA;MAEAoB,YAAgC;MAC9BsB,WAAA;MACAM,cAAM;MACN1B,OAAA,EAAAgD,KAAA,CAAA3E,KAAA;MACA4E,WAAA,EAAAD,KAAA,CAAA3E,KAAA;IAAA,CACA;IACA,MAAA6E,mBAAA,GAAAf,CAAA;MACD,IAAAzB,EAAA,EAAAC,EAAA;MAEDwB,CAAA,CAAAC,cAAyC;MACvC,CAAUzB,EAAA,IAAAD,EAAA,GAAAtB,UAAA,CAAAW,KAAA,qBAAAW,EAAA,CAAAkB,KAAA,qBAAAjB,EAAA,CAAAwC,IAAA,CAAAzC,EAAA;QACV0C,aAAA;MAAA,CACA;IAAA,CACA;IACA,MAAAC,sBAA+B,GAAAb,KAAA;MAC/B/D,IAAA,UAAmB+D,KAAA;IAAoB,CACxC;IAEK;MACJ1D,CAAA;MACAF,EAAA;MAA0BS,SACT;MACjBG,SAAC;MACHI,kBAAA;MAEME,YAAA;MACJM,SAAA;MACFd,YAAA;MAEO2C,wBAAA;MACLoB,sBAAA;MACAnB,gBAAA;MACAb,WAAA;MACAE,UAAA;MACAe,uBAAA;MACAC,iBAAA;MACAG,uBAAA;MACAQ,mBAAA;MACA/D,SAAA;MACAC,UAAA;MACAJ,oBAAA;MACAE;IAAA,CACA;EAAA;AACA,CACA;AACA,SACAoE,YAAAC,IAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,QAAA;EACA,IAAAlD,EAAA;EACA,MAAAmD,iCAAA,GAAAC,gBAAA;EACA,MAAAC,gCAAA,GAAAD,gBAAA;EACA,MAAAE,uBAAA,GAAAF,gBAAA;EACF,MAAAG,wBAAA,GAAAH,gBAAA;EACF,MAAAI,qBAAA,GAAAJ,gBAAA;EACD,MAAAK,oBAAA,GAAAL,gBAAA;;;;;;;;;;;IA1TC,kBAAAP,IAAA,CAAAa,aAAA;IAyFM;IAAA,cAAAb,IAAA,CAAAvD,OAAA,eAAAuD,IAAA,CAAAc,WAAA;IAzFA;IAA0CC,SAAA,EAAAf,IAAA,CAAAe,SAAA;;IAC9C,mBA0Da,GAAA5D,EAAA,GAAA6C,IAAA,CAAArE,mBAAA,qBAAAwB,EAAA,CAAAG,GAAA;IAAAb,OAzDP,EAAAuD,IAAA,CAAAvD,OAAA;IAAA,cACG,EAAAuD,IAAA,CAAAgB,WAAA;IAAA,mBACE,EAAAhB,IAAA,CAAAnE,UAAA;IAAA,YACa,EAAAmE,IAAA,CAAAvD,OAAA,eAAAuD,IAAA,CAAAiB,WAAA;IAAA,yBACL;IAAA,aACE,EAAAjB,IAAA,CAAAvE,oBAAA;IAClB,oBAAmB,EAAAuE,IAAA,CAAAkB,WAAA;IAA0BC,QAChC,EAAAnB,IAAA,CAAAmB,QAAA;IAAAC,UACF,KAAApB,IAAA,CAAA3E,EAAA,CAAAgG,SAAA,CAAA7E,KAAA;IAAA8E,UACC,EAAAtB,IAAA,CAAAsB,UAAM;IAAwBC,IAAA;IACFC,UAC/B,EAAAxB,IAAA,CAAAwB,UAAA;IAAAC,YACK,EAAAzB,IAAA,CAAAjB,uBAAA;IAAA2C,MACK,EAAA1B,IAAA,CAAAhB,iBAAA;IACnB2C,YAAA,EAAA3B,IAAmB,CAAAb;EAA0B,GAAAyC,WACpB;IAAAC,OACZ,EAAAC,OAAA,QACbC,WAAoB,CAAAtB,uBAAA;MACV/E,GAAA;MACA,cAAQsE,IAAA,CAAA/D,SAAA;MACN+F,GAAA;MACb,cAAAhC,IAAA,CAAA3E,EAAA,CAAAuD,CAAA;IAAA,CACa;MACCqD,OAAA,EAAAH,OAAA,QACPC,WAAA,CAAAvB,gCAAA;QACO0B,IAAA,EAAAlC,IAAA,CAAAkC,IAAA;QAAA,kBAAAlC,IAAA,CAAAjE,YAAA;QAEHoG,WAAA,EAkBM;QAAAC,oBAAA,EAAApC,IAAA,CAAAtB,wBAAA;QAhBT2D,YAAA,EAAArC,IAAA,CAAArB;MAAA,CACS;QACTsD,OAAA,EAAAH,OAAA,QACHC,WAAA,CAAAzB,iCAAgB;UAAA2B,OAAA,EAAAH,OAAA,Q,UAYO,CAAA9B,IAAA,CAAAsC,MAAA;UATfC,CAAA;QAAA,CACU,GACjB;QAAYA,CAAA,EACY;MAAA,IACvB,EAAa;;IAIW;IADCA,CAAA;EAAA,I;;;;;;;;;;;EAKf,WAAc,mTAAAvC,IAAA,CAAAkB,WAAA,IAQbsB,SAAA,IAAAC,WAAA,CAAAC,0BAAA;IAAAC,GAAA;EAAA;IAAAV,OAAA,EAAAH,OAAA,QAAAC,WANT,CAAAnB,oBAAA,EAAAgC,UAAA;MAAAlH,GAAA;IAAA,GAAAsE,IAAA,CAAA6C,WAAA;MAAAC,IACL,EAAI9C,IAAA,CAAAzD,YAAA;MAAA2C,IACJ,EAAKc,IAAA,CAAAd,IAAA;MAAAiC,QACM,EAAAnB,IAAA,CAAAmB,QAAA;MAAA4B,QAAA,EAAA/C,IAAA,CAAA+C,QAAA;;IAEY;MAAAd,OAAA,EAAAH,OAAA,Q;;;;MAIbpG,GAAA;IA2BI,GAAAsE,IAAA,CAAA6C,WAfJ;MAVZvD,IAAA;MAEqBwD,IACZ,EAAA9C,IAAA,CAAAzD,YAAA;MAAA2C,IACA,EAAAc,IAAA,CAAAd,IAAA;MAAA8D,KACI,EAAAhD,IAAA,CAAA3E,EAAA,CAAAuD,CAAA;MAAAuC,QACA,EAAAnB,IAAA,CAAAmB,QAAA;MAAA4B,QACH,EAAA/C,IAAA,CAAA+C,QAAA;MAAA,cAAA/C,IAAA,CAAAzE,CAAA;;MAEe0G,OAAA,EAAAH,OAAA,QAAAC,WAAA,CAAAkB,kBAAA;;;gCAEzBlB,WAAA,CAAAmB,qBAaY,EAZL;QACDX,CAAA;MAAA,CACI,EAAW,eACnB;MAAKA,CAAA,EACE;IAAA,KACA,yEACN;IAAWA,CAAA;EACD,MAAAY,kBACA;AACG;AAEyC,IAAAC,QAAA,kBAAAC,WAAA,CAAApJ,SAAA,cAAA8F,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
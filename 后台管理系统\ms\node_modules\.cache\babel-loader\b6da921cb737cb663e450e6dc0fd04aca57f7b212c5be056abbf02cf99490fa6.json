{"ast": null, "code": "import { defineComponent, markRaw, ref, effectScope, shallowRef, computed, watch, nextTick, onMounted, openBlock, createBlock, unref, withCtx, createVNode, Transition, createElementVNode, normalizeClass, normalizeStyle, withModifiers, createCommentVNode, createElementBlock, Fragment, renderSlot, createTextVNode, toDisplayString, resolveDynamicComponent, renderList } from 'vue';\nimport { useEventListener } from '@vueuse/core';\nimport { throttle } from 'lodash-unified';\nimport ElFocusTrap from '../../focus-trap/src/focus-trap.mjs';\nimport { ElTeleport } from '../../teleport/index.mjs';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { FullScreen, ScaleToOriginal, Close, ArrowLeft, ArrowRight, ZoomOut, ZoomIn, RefreshLeft, RefreshRight } from '@element-plus/icons-vue';\nimport { imageViewerProps, imageViewerEmits } from './image-viewer.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLocale } from '../../../hooks/use-locale/index.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useZIndex } from '../../../hooks/use-z-index/index.mjs';\nimport { EVENT_CODE } from '../../../constants/aria.mjs';\nimport { keysOf } from '../../../utils/objects.mjs';\nconst __default__ = defineComponent({\n  name: \"ElImageViewer\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: imageViewerProps,\n  emits: imageViewerEmits,\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    var _a;\n    const props = __props;\n    const modes = {\n      CONTAIN: {\n        name: \"contain\",\n        icon: markRaw(FullScreen)\n      },\n      ORIGINAL: {\n        name: \"original\",\n        icon: markRaw(ScaleToOriginal)\n      }\n    };\n    let stopWheelListener;\n    let prevOverflow = \"\";\n    const {\n      t\n    } = useLocale();\n    const ns = useNamespace(\"image-viewer\");\n    const {\n      nextZIndex\n    } = useZIndex();\n    const wrapper = ref();\n    const imgRefs = ref([]);\n    const scopeEventListener = effectScope();\n    const loading = ref(true);\n    const activeIndex = ref(props.initialIndex);\n    const mode = shallowRef(modes.CONTAIN);\n    const transform = ref({\n      scale: 1,\n      deg: 0,\n      offsetX: 0,\n      offsetY: 0,\n      enableTransition: false\n    });\n    const zIndex = ref((_a = props.zIndex) != null ? _a : nextZIndex());\n    const isSingle = computed(() => {\n      const {\n        urlList\n      } = props;\n      return urlList.length <= 1;\n    });\n    const isFirst = computed(() => activeIndex.value === 0);\n    const isLast = computed(() => activeIndex.value === props.urlList.length - 1);\n    const currentImg = computed(() => props.urlList[activeIndex.value]);\n    const arrowPrevKls = computed(() => [ns.e(\"btn\"), ns.e(\"prev\"), ns.is(\"disabled\", !props.infinite && isFirst.value)]);\n    const arrowNextKls = computed(() => [ns.e(\"btn\"), ns.e(\"next\"), ns.is(\"disabled\", !props.infinite && isLast.value)]);\n    const imgStyle = computed(() => {\n      const {\n        scale,\n        deg,\n        offsetX,\n        offsetY,\n        enableTransition\n      } = transform.value;\n      let translateX = offsetX / scale;\n      let translateY = offsetY / scale;\n      const radian = deg * Math.PI / 180;\n      const cosRadian = Math.cos(radian);\n      const sinRadian = Math.sin(radian);\n      translateX = translateX * cosRadian + translateY * sinRadian;\n      translateY = translateY * cosRadian - offsetX / scale * sinRadian;\n      const style = {\n        transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,\n        transition: enableTransition ? \"transform .3s\" : \"\"\n      };\n      if (mode.value.name === modes.CONTAIN.name) {\n        style.maxWidth = style.maxHeight = \"100%\";\n      }\n      return style;\n    });\n    const progress = computed(() => `${activeIndex.value + 1} / ${props.urlList.length}`);\n    function hide() {\n      unregisterEventListener();\n      stopWheelListener == null ? void 0 : stopWheelListener();\n      document.body.style.overflow = prevOverflow;\n      emit(\"close\");\n    }\n    function registerEventListener() {\n      const keydownHandler = throttle(e => {\n        switch (e.code) {\n          case EVENT_CODE.esc:\n            props.closeOnPressEscape && hide();\n            break;\n          case EVENT_CODE.space:\n            toggleMode();\n            break;\n          case EVENT_CODE.left:\n            prev();\n            break;\n          case EVENT_CODE.up:\n            handleActions(\"zoomIn\");\n            break;\n          case EVENT_CODE.right:\n            next();\n            break;\n          case EVENT_CODE.down:\n            handleActions(\"zoomOut\");\n            break;\n        }\n      });\n      const mousewheelHandler = throttle(e => {\n        const delta = e.deltaY || e.deltaX;\n        handleActions(delta < 0 ? \"zoomIn\" : \"zoomOut\", {\n          zoomRate: props.zoomRate,\n          enableTransition: false\n        });\n      });\n      scopeEventListener.run(() => {\n        useEventListener(document, \"keydown\", keydownHandler);\n        useEventListener(document, \"wheel\", mousewheelHandler);\n      });\n    }\n    function unregisterEventListener() {\n      scopeEventListener.stop();\n    }\n    function handleImgLoad() {\n      loading.value = false;\n    }\n    function handleImgError(e) {\n      loading.value = false;\n      e.target.alt = t(\"el.image.error\");\n    }\n    function handleMouseDown(e) {\n      if (loading.value || e.button !== 0 || !wrapper.value) return;\n      transform.value.enableTransition = false;\n      const {\n        offsetX,\n        offsetY\n      } = transform.value;\n      const startX = e.pageX;\n      const startY = e.pageY;\n      const dragHandler = throttle(ev => {\n        transform.value = {\n          ...transform.value,\n          offsetX: offsetX + ev.pageX - startX,\n          offsetY: offsetY + ev.pageY - startY\n        };\n      });\n      const removeMousemove = useEventListener(document, \"mousemove\", dragHandler);\n      useEventListener(document, \"mouseup\", () => {\n        removeMousemove();\n      });\n      e.preventDefault();\n    }\n    function reset() {\n      transform.value = {\n        scale: 1,\n        deg: 0,\n        offsetX: 0,\n        offsetY: 0,\n        enableTransition: false\n      };\n    }\n    function toggleMode() {\n      if (loading.value) return;\n      const modeNames = keysOf(modes);\n      const modeValues = Object.values(modes);\n      const currentMode = mode.value.name;\n      const index = modeValues.findIndex(i => i.name === currentMode);\n      const nextIndex = (index + 1) % modeNames.length;\n      mode.value = modes[modeNames[nextIndex]];\n      reset();\n    }\n    function setActiveItem(index) {\n      const len = props.urlList.length;\n      activeIndex.value = (index + len) % len;\n    }\n    function prev() {\n      if (isFirst.value && !props.infinite) return;\n      setActiveItem(activeIndex.value - 1);\n    }\n    function next() {\n      if (isLast.value && !props.infinite) return;\n      setActiveItem(activeIndex.value + 1);\n    }\n    function handleActions(action, options = {}) {\n      if (loading.value) return;\n      const {\n        minScale,\n        maxScale\n      } = props;\n      const {\n        zoomRate,\n        rotateDeg,\n        enableTransition\n      } = {\n        zoomRate: props.zoomRate,\n        rotateDeg: 90,\n        enableTransition: true,\n        ...options\n      };\n      switch (action) {\n        case \"zoomOut\":\n          if (transform.value.scale > minScale) {\n            transform.value.scale = Number.parseFloat((transform.value.scale / zoomRate).toFixed(3));\n          }\n          break;\n        case \"zoomIn\":\n          if (transform.value.scale < maxScale) {\n            transform.value.scale = Number.parseFloat((transform.value.scale * zoomRate).toFixed(3));\n          }\n          break;\n        case \"clockwise\":\n          transform.value.deg += rotateDeg;\n          emit(\"rotate\", transform.value.deg);\n          break;\n        case \"anticlockwise\":\n          transform.value.deg -= rotateDeg;\n          emit(\"rotate\", transform.value.deg);\n          break;\n      }\n      transform.value.enableTransition = enableTransition;\n    }\n    function onFocusoutPrevented(event) {\n      var _a2;\n      if (((_a2 = event.detail) == null ? void 0 : _a2.focusReason) === \"pointer\") {\n        event.preventDefault();\n      }\n    }\n    function onCloseRequested() {\n      if (props.closeOnPressEscape) {\n        hide();\n      }\n    }\n    function wheelHandler(e) {\n      if (!e.ctrlKey) return;\n      if (e.deltaY < 0) {\n        e.preventDefault();\n        return false;\n      } else if (e.deltaY > 0) {\n        e.preventDefault();\n        return false;\n      }\n    }\n    watch(currentImg, () => {\n      nextTick(() => {\n        const $img = imgRefs.value[0];\n        if (!($img == null ? void 0 : $img.complete)) {\n          loading.value = true;\n        }\n      });\n    });\n    watch(activeIndex, val => {\n      reset();\n      emit(\"switch\", val);\n    });\n    onMounted(() => {\n      registerEventListener();\n      stopWheelListener = useEventListener(\"wheel\", wheelHandler, {\n        passive: false\n      });\n      prevOverflow = document.body.style.overflow;\n      document.body.style.overflow = \"hidden\";\n    });\n    expose({\n      setActiveItem\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(ElTeleport), {\n        to: \"body\",\n        disabled: !_ctx.teleported\n      }, {\n        default: withCtx(() => [createVNode(Transition, {\n          name: \"viewer-fade\",\n          appear: \"\"\n        }, {\n          default: withCtx(() => [createElementVNode(\"div\", {\n            ref_key: \"wrapper\",\n            ref: wrapper,\n            tabindex: -1,\n            class: normalizeClass(unref(ns).e(\"wrapper\")),\n            style: normalizeStyle({\n              zIndex: zIndex.value\n            })\n          }, [createVNode(unref(ElFocusTrap), {\n            loop: \"\",\n            trapped: \"\",\n            \"focus-trap-el\": wrapper.value,\n            \"focus-start-el\": \"container\",\n            onFocusoutPrevented,\n            onReleaseRequested: onCloseRequested\n          }, {\n            default: withCtx(() => [createElementVNode(\"div\", {\n              class: normalizeClass(unref(ns).e(\"mask\")),\n              onClick: withModifiers($event => _ctx.hideOnClickModal && hide(), [\"self\"])\n            }, null, 10, [\"onClick\"]), createCommentVNode(\" CLOSE \"), createElementVNode(\"span\", {\n              class: normalizeClass([unref(ns).e(\"btn\"), unref(ns).e(\"close\")]),\n              onClick: hide\n            }, [createVNode(unref(ElIcon), null, {\n              default: withCtx(() => [createVNode(unref(Close))]),\n              _: 1\n            })], 2), createCommentVNode(\" ARROW \"), !unref(isSingle) ? (openBlock(), createElementBlock(Fragment, {\n              key: 0\n            }, [createElementVNode(\"span\", {\n              class: normalizeClass(unref(arrowPrevKls)),\n              onClick: prev\n            }, [createVNode(unref(ElIcon), null, {\n              default: withCtx(() => [createVNode(unref(ArrowLeft))]),\n              _: 1\n            })], 2), createElementVNode(\"span\", {\n              class: normalizeClass(unref(arrowNextKls)),\n              onClick: next\n            }, [createVNode(unref(ElIcon), null, {\n              default: withCtx(() => [createVNode(unref(ArrowRight))]),\n              _: 1\n            })], 2)], 64)) : createCommentVNode(\"v-if\", true), _ctx.$slots.progress || _ctx.showProgress ? (openBlock(), createElementBlock(\"div\", {\n              key: 1,\n              class: normalizeClass([unref(ns).e(\"btn\"), unref(ns).e(\"progress\")])\n            }, [renderSlot(_ctx.$slots, \"progress\", {\n              activeIndex: activeIndex.value,\n              total: _ctx.urlList.length\n            }, () => [createTextVNode(toDisplayString(unref(progress)), 1)])], 2)) : createCommentVNode(\"v-if\", true), createCommentVNode(\" ACTIONS \"), createElementVNode(\"div\", {\n              class: normalizeClass([unref(ns).e(\"btn\"), unref(ns).e(\"actions\")])\n            }, [createElementVNode(\"div\", {\n              class: normalizeClass(unref(ns).e(\"actions__inner\"))\n            }, [renderSlot(_ctx.$slots, \"toolbar\", {\n              actions: handleActions,\n              prev,\n              next,\n              reset: toggleMode,\n              activeIndex: activeIndex.value,\n              setActiveItem\n            }, () => [createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"zoomOut\")\n            }, {\n              default: withCtx(() => [createVNode(unref(ZoomOut))]),\n              _: 1\n            }, 8, [\"onClick\"]), createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"zoomIn\")\n            }, {\n              default: withCtx(() => [createVNode(unref(ZoomIn))]),\n              _: 1\n            }, 8, [\"onClick\"]), createElementVNode(\"i\", {\n              class: normalizeClass(unref(ns).e(\"actions__divider\"))\n            }, null, 2), createVNode(unref(ElIcon), {\n              onClick: toggleMode\n            }, {\n              default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(mode).icon)))]),\n              _: 1\n            }), createElementVNode(\"i\", {\n              class: normalizeClass(unref(ns).e(\"actions__divider\"))\n            }, null, 2), createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"anticlockwise\")\n            }, {\n              default: withCtx(() => [createVNode(unref(RefreshLeft))]),\n              _: 1\n            }, 8, [\"onClick\"]), createVNode(unref(ElIcon), {\n              onClick: $event => handleActions(\"clockwise\")\n            }, {\n              default: withCtx(() => [createVNode(unref(RefreshRight))]),\n              _: 1\n            }, 8, [\"onClick\"])])], 2)], 2), createCommentVNode(\" CANVAS \"), createElementVNode(\"div\", {\n              class: normalizeClass(unref(ns).e(\"canvas\"))\n            }, [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.urlList, (url, i) => {\n              return openBlock(), createElementBlock(Fragment, {\n                key: i\n              }, [i === activeIndex.value ? (openBlock(), createElementBlock(\"img\", {\n                key: 0,\n                ref_for: true,\n                ref: el => imgRefs.value[i] = el,\n                src: url,\n                style: normalizeStyle(unref(imgStyle)),\n                class: normalizeClass(unref(ns).e(\"img\")),\n                crossorigin: _ctx.crossorigin,\n                onLoad: handleImgLoad,\n                onError: handleImgError,\n                onMousedown: handleMouseDown\n              }, null, 46, [\"src\", \"crossorigin\"])) : createCommentVNode(\"v-if\", true)], 64);\n            }), 128))], 2), renderSlot(_ctx.$slots, \"default\")]),\n            _: 3\n          }, 8, [\"focus-trap-el\"])], 6)]),\n          _: 3\n        })]),\n        _: 3\n      }, 8, [\"disabled\"]);\n    };\n  }\n});\nvar ImageViewer = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"image-viewer.vue\"]]);\nexport { ImageViewer as default };", "map": {"version": 3, "names": ["name", "props", "__props", "modes", "CONTAIN", "icon", "mark<PERSON>aw", "FullScreen", "ORIGINAL", "ScaleToOriginal", "stopWheelListener", "prevOverflow", "t", "useLocale", "ns", "useNamespace", "nextZIndex", "useZIndex", "wrapper", "ref", "imgRefs", "scopeEventListener", "effectScope", "loading", "activeIndex", "initialIndex", "mode", "shallowRef", "transform", "scale", "deg", "offsetX", "offsetY", "enableTransition", "zIndex", "_a", "isSingle", "computed", "urlList", "length", "<PERSON><PERSON><PERSON><PERSON>", "value", "isLast", "currentImg", "arrowPrevKls", "e", "is", "infinite", "arrowNextKls", "imgStyle", "translateX", "translateY", "radian", "Math", "PI", "cosRadian", "cos", "sinRadian", "sin", "style", "transition", "max<PERSON><PERSON><PERSON>", "maxHeight", "progress", "hide", "unregisterEventListener", "document", "body", "overflow", "emit", "registerEventListener", "keydownHandler", "throttle", "code", "EVENT_CODE", "esc", "closeOnPressEscape", "space", "toggleMode", "left", "prev", "up", "handleActions", "right", "next", "down", "mousewheelHandler", "delta", "deltaY", "deltaX", "zoomRate", "run", "useEventListener", "stop", "handleImgLoad", "handleImgError", "target", "alt", "handleMouseDown", "button", "startX", "pageX", "startY", "pageY", "<PERSON><PERSON><PERSON><PERSON>", "ev", "removeMousemove", "preventDefault", "reset", "modeNames", "keysOf", "modeValues", "Object", "values", "currentMode", "index", "findIndex", "i", "nextIndex", "setActiveItem", "len", "action", "options", "minScale", "maxScale", "rotateDeg", "Number", "parseFloat", "toFixed", "onFocusoutPrevented", "event", "_a2", "detail", "focusReason", "onCloseRequested", "wheelHandler", "ctrl<PERSON>ey", "watch", "nextTick", "$img", "complete", "val", "onMounted", "passive", "expose", "_ctx", "_cache"], "sources": ["../../../../../../packages/components/image-viewer/src/image-viewer.vue"], "sourcesContent": ["<template>\n  <el-teleport to=\"body\" :disabled=\"!teleported\">\n    <transition name=\"viewer-fade\" appear>\n      <div\n        ref=\"wrapper\"\n        :tabindex=\"-1\"\n        :class=\"ns.e('wrapper')\"\n        :style=\"{ zIndex }\"\n      >\n        <el-focus-trap\n          loop\n          trapped\n          :focus-trap-el=\"wrapper\"\n          focus-start-el=\"container\"\n          @focusout-prevented=\"onFocusoutPrevented\"\n          @release-requested=\"onCloseRequested\"\n        >\n          <div :class=\"ns.e('mask')\" @click.self=\"hideOnClickModal && hide()\" />\n\n          <!-- CLOSE -->\n          <span :class=\"[ns.e('btn'), ns.e('close')]\" @click=\"hide\">\n            <el-icon>\n              <Close />\n            </el-icon>\n          </span>\n\n          <!-- ARROW -->\n          <template v-if=\"!isSingle\">\n            <span :class=\"arrowPrevKls\" @click=\"prev\">\n              <el-icon>\n                <ArrowLeft />\n              </el-icon>\n            </span>\n            <span :class=\"arrowNextKls\" @click=\"next\">\n              <el-icon>\n                <ArrowRight />\n              </el-icon>\n            </span>\n          </template>\n          <div\n            v-if=\"$slots.progress || showProgress\"\n            :class=\"[ns.e('btn'), ns.e('progress')]\"\n          >\n            <slot\n              name=\"progress\"\n              :active-index=\"activeIndex\"\n              :total=\"urlList.length\"\n            >\n              {{ progress }}\n            </slot>\n          </div>\n          <!-- ACTIONS -->\n          <div :class=\"[ns.e('btn'), ns.e('actions')]\">\n            <div :class=\"ns.e('actions__inner')\">\n              <slot\n                name=\"toolbar\"\n                :actions=\"handleActions\"\n                :prev=\"prev\"\n                :next=\"next\"\n                :reset=\"toggleMode\"\n                :active-index=\"activeIndex\"\n                :set-active-item=\"setActiveItem\"\n              >\n                <el-icon @click=\"handleActions('zoomOut')\">\n                  <ZoomOut />\n                </el-icon>\n                <el-icon @click=\"handleActions('zoomIn')\">\n                  <ZoomIn />\n                </el-icon>\n                <i :class=\"ns.e('actions__divider')\" />\n                <el-icon @click=\"toggleMode\">\n                  <component :is=\"mode.icon\" />\n                </el-icon>\n                <i :class=\"ns.e('actions__divider')\" />\n                <el-icon @click=\"handleActions('anticlockwise')\">\n                  <RefreshLeft />\n                </el-icon>\n                <el-icon @click=\"handleActions('clockwise')\">\n                  <RefreshRight />\n                </el-icon>\n              </slot>\n            </div>\n          </div>\n          <!-- CANVAS -->\n          <div :class=\"ns.e('canvas')\">\n            <template v-for=\"(url, i) in urlList\" :key=\"i\">\n              <img\n                v-if=\"i === activeIndex\"\n                :ref=\"(el) => (imgRefs[i] = el as HTMLImageElement)\"\n                :src=\"url\"\n                :style=\"imgStyle\"\n                :class=\"ns.e('img')\"\n                :crossorigin=\"crossorigin\"\n                @load=\"handleImgLoad\"\n                @error=\"handleImgError\"\n                @mousedown=\"handleMouseDown\"\n              />\n            </template>\n          </div>\n          <slot />\n        </el-focus-trap>\n      </div>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  effectScope,\n  markRaw,\n  nextTick,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { throttle } from 'lodash-unified'\nimport { useLocale, useNamespace, useZIndex } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { keysOf } from '@element-plus/utils'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport ElTeleport from '@element-plus/components/teleport'\nimport ElIcon from '@element-plus/components/icon'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  Close,\n  FullScreen,\n  RefreshLeft,\n  RefreshRight,\n  ScaleToOriginal,\n  ZoomIn,\n  ZoomOut,\n} from '@element-plus/icons-vue'\nimport { imageViewerEmits, imageViewerProps } from './image-viewer'\n\nimport type { CSSProperties } from 'vue'\nimport type { ImageViewerAction, ImageViewerMode } from './image-viewer'\n\nconst modes: Record<'CONTAIN' | 'ORIGINAL', ImageViewerMode> = {\n  CONTAIN: {\n    name: 'contain',\n    icon: markRaw(FullScreen),\n  },\n  ORIGINAL: {\n    name: 'original',\n    icon: markRaw(ScaleToOriginal),\n  },\n}\n\ndefineOptions({\n  name: 'ElImageViewer',\n})\n\nconst props = defineProps(imageViewerProps)\nconst emit = defineEmits(imageViewerEmits)\n\nlet stopWheelListener: (() => void) | undefined\nlet prevOverflow = ''\n\nconst { t } = useLocale()\nconst ns = useNamespace('image-viewer')\nconst { nextZIndex } = useZIndex()\nconst wrapper = ref<HTMLDivElement>()\nconst imgRefs = ref<HTMLImageElement[]>([])\n\nconst scopeEventListener = effectScope()\n\nconst loading = ref(true)\nconst activeIndex = ref(props.initialIndex)\nconst mode = shallowRef<ImageViewerMode>(modes.CONTAIN)\nconst transform = ref({\n  scale: 1,\n  deg: 0,\n  offsetX: 0,\n  offsetY: 0,\n  enableTransition: false,\n})\nconst zIndex = ref(props.zIndex ?? nextZIndex())\n\nconst isSingle = computed(() => {\n  const { urlList } = props\n  return urlList.length <= 1\n})\n\nconst isFirst = computed(() => activeIndex.value === 0)\n\nconst isLast = computed(() => activeIndex.value === props.urlList.length - 1)\n\nconst currentImg = computed(() => props.urlList[activeIndex.value])\n\nconst arrowPrevKls = computed(() => [\n  ns.e('btn'),\n  ns.e('prev'),\n  ns.is('disabled', !props.infinite && isFirst.value),\n])\n\nconst arrowNextKls = computed(() => [\n  ns.e('btn'),\n  ns.e('next'),\n  ns.is('disabled', !props.infinite && isLast.value),\n])\n\nconst imgStyle = computed(() => {\n  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value\n  let translateX = offsetX / scale\n  let translateY = offsetY / scale\n\n  const radian = (deg * Math.PI) / 180\n  const cosRadian = Math.cos(radian)\n  const sinRadian = Math.sin(radian)\n  translateX = translateX * cosRadian + translateY * sinRadian\n  translateY = translateY * cosRadian - (offsetX / scale) * sinRadian\n\n  const style: CSSProperties = {\n    transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,\n    transition: enableTransition ? 'transform .3s' : '',\n  }\n  if (mode.value.name === modes.CONTAIN.name) {\n    style.maxWidth = style.maxHeight = '100%'\n  }\n  return style\n})\n\nconst progress = computed(\n  () => `${activeIndex.value + 1} / ${props.urlList.length}`\n)\n\nfunction hide() {\n  unregisterEventListener()\n  stopWheelListener?.()\n  document.body.style.overflow = prevOverflow\n  emit('close')\n}\n\nfunction registerEventListener() {\n  const keydownHandler = throttle((e: KeyboardEvent) => {\n    switch (e.code) {\n      // ESC\n      case EVENT_CODE.esc:\n        props.closeOnPressEscape && hide()\n        break\n      // SPACE\n      case EVENT_CODE.space:\n        toggleMode()\n        break\n      // LEFT_ARROW\n      case EVENT_CODE.left:\n        prev()\n        break\n      // UP_ARROW\n      case EVENT_CODE.up:\n        handleActions('zoomIn')\n        break\n      // RIGHT_ARROW\n      case EVENT_CODE.right:\n        next()\n        break\n      // DOWN_ARROW\n      case EVENT_CODE.down:\n        handleActions('zoomOut')\n        break\n    }\n  })\n  const mousewheelHandler = throttle((e: WheelEvent) => {\n    const delta = e.deltaY || e.deltaX\n    handleActions(delta < 0 ? 'zoomIn' : 'zoomOut', {\n      zoomRate: props.zoomRate,\n      enableTransition: false,\n    })\n  })\n\n  scopeEventListener.run(() => {\n    useEventListener(document, 'keydown', keydownHandler)\n    useEventListener(document, 'wheel', mousewheelHandler)\n  })\n}\n\nfunction unregisterEventListener() {\n  scopeEventListener.stop()\n}\n\nfunction handleImgLoad() {\n  loading.value = false\n}\n\nfunction handleImgError(e: Event) {\n  loading.value = false\n  ;(e.target as HTMLImageElement).alt = t('el.image.error')\n}\n\nfunction handleMouseDown(e: MouseEvent) {\n  if (loading.value || e.button !== 0 || !wrapper.value) return\n  transform.value.enableTransition = false\n\n  const { offsetX, offsetY } = transform.value\n  const startX = e.pageX\n  const startY = e.pageY\n\n  const dragHandler = throttle((ev: MouseEvent) => {\n    transform.value = {\n      ...transform.value,\n      offsetX: offsetX + ev.pageX - startX,\n      offsetY: offsetY + ev.pageY - startY,\n    }\n  })\n  const removeMousemove = useEventListener(document, 'mousemove', dragHandler)\n  useEventListener(document, 'mouseup', () => {\n    removeMousemove()\n  })\n\n  e.preventDefault()\n}\n\nfunction reset() {\n  transform.value = {\n    scale: 1,\n    deg: 0,\n    offsetX: 0,\n    offsetY: 0,\n    enableTransition: false,\n  }\n}\n\nfunction toggleMode() {\n  if (loading.value) return\n\n  const modeNames = keysOf(modes)\n  const modeValues = Object.values(modes)\n  const currentMode = mode.value.name\n  const index = modeValues.findIndex((i) => i.name === currentMode)\n  const nextIndex = (index + 1) % modeNames.length\n  mode.value = modes[modeNames[nextIndex]]\n  reset()\n}\n\nfunction setActiveItem(index: number) {\n  const len = props.urlList.length\n  activeIndex.value = (index + len) % len\n}\n\nfunction prev() {\n  if (isFirst.value && !props.infinite) return\n  setActiveItem(activeIndex.value - 1)\n}\n\nfunction next() {\n  if (isLast.value && !props.infinite) return\n  setActiveItem(activeIndex.value + 1)\n}\n\nfunction handleActions(action: ImageViewerAction, options = {}) {\n  if (loading.value) return\n  const { minScale, maxScale } = props\n  const { zoomRate, rotateDeg, enableTransition } = {\n    zoomRate: props.zoomRate,\n    rotateDeg: 90,\n    enableTransition: true,\n    ...options,\n  }\n  switch (action) {\n    case 'zoomOut':\n      if (transform.value.scale > minScale) {\n        transform.value.scale = Number.parseFloat(\n          (transform.value.scale / zoomRate).toFixed(3)\n        )\n      }\n      break\n    case 'zoomIn':\n      if (transform.value.scale < maxScale) {\n        transform.value.scale = Number.parseFloat(\n          (transform.value.scale * zoomRate).toFixed(3)\n        )\n      }\n      break\n    case 'clockwise':\n      transform.value.deg += rotateDeg\n      emit('rotate', transform.value.deg)\n      break\n    case 'anticlockwise':\n      transform.value.deg -= rotateDeg\n      emit('rotate', transform.value.deg)\n      break\n  }\n  transform.value.enableTransition = enableTransition\n}\n\nfunction onFocusoutPrevented(event: CustomEvent) {\n  if (event.detail?.focusReason === 'pointer') {\n    event.preventDefault()\n  }\n}\n\nfunction onCloseRequested() {\n  if (props.closeOnPressEscape) {\n    hide()\n  }\n}\n\nfunction wheelHandler(e: WheelEvent) {\n  if (!e.ctrlKey) return\n\n  if (e.deltaY < 0) {\n    e.preventDefault()\n    return false\n  } else if (e.deltaY > 0) {\n    e.preventDefault()\n    return false\n  }\n}\n\nwatch(currentImg, () => {\n  nextTick(() => {\n    const $img = imgRefs.value[0]\n    if (!$img?.complete) {\n      loading.value = true\n    }\n  })\n})\n\nwatch(activeIndex, (val) => {\n  reset()\n  emit('switch', val)\n})\n\nonMounted(() => {\n  registerEventListener()\n\n  stopWheelListener = useEventListener('wheel', wheelHandler, {\n    passive: false,\n  })\n\n  // prevent body scroll\n  prevOverflow = document.body.style.overflow\n  document.body.style.overflow = 'hidden'\n})\n\ndefineExpose({\n  /**\n   * @description manually switch image\n   */\n  setActiveItem,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;mCAwJc;EACZA,IAAM;AACR;;;;;;;;;;IAbA,MAAMC,KAAyD,GAAAC,OAAA;IAAA,MACpDC,KAAA;MAAAC,OACD;QACNJ,IAAA,EAAM,SAAkB;QAC1BK,IAAA,EAAAC,OAAA,CAAAC,UAAA;MAAA,CACU;MAAAC,QACF;QACNR,IAAA,EAAM,UAAuB;QAC/BK,IAAA,EAAAC,OAAA,CAAAG,eAAA;MAAA;IAUF,CAAI;IACJ,IAAIC,iBAAe;IAEb,IAAAC,YAAkB;IAClB;MAAAC;IAAA,CAAK,GAAAC,SAAA,EAA2B;IAChC,MAAAC,EAAE,GAAWC,YAAI,CAAU;IACjC,MAAM;MAAAC;IAA8B,IAAAC,SAAA;IAC9B,MAAAC,OAAA,GAAUC,GAAwB,EAAC;IAEzC,MAAMC,OAAA,GAAAD,GAAA;IAEA,MAAAE,kBAAkB,GAAAC,WAAA;IAClB,MAAAC,OAAA,GAAAJ,GAAA,CAAc,IAAI;IAClB,MAAAK,WAAmC,GAAAL,GAAA,CAAAlB,KAAA,CAAMwB,YAAO;IACtD,MAAMC,IAAA,GAAAC,UAAgB,CAAAxB,KAAA,CAAAC,OAAA;IAAA,MACbwB,SAAA,GAAAT,GAAA;MACPU,KAAK;MACLC,GAAS;MACTC,OAAS;MACTC,OAAkB;MACnBC,gBAAA;IACD;IAEM,MAAAC,MAAA,GAAAf,GAAA,EAAAgB,EAAA,GAAAlC,KAA0B,CAAAiC,MAAA,YAAAC,EAAA,GAAAnB,UAAA;IACxB,MAAAoB,QAAA,GAAAC,QAAc;MACpB;QAAAC;MAAe,CAAU,GAAArC,KAAA;MAC1B,OAAAqC,OAAA,CAAAC,MAAA;IAED;IAEM,MAAAC,OAAA,GAAAH,QAAkB,CAAM,MAAAb,WAAY,CAAAiB,KAAA,KAAgB;IAE1D,MAAMC,MAAA,GAAAL,QAAsB,OAAAb,WAAoB,CAAAiB,KAAA,KAAAxC,KAAA,CAAAqC,OAAiB,CAACC,MAAA;IAE5D,MAAAI,UAAA,GAAAN,QAAA,OAA8BpC,KAAA,CAAAqC,OAAA,CAAAd,WAAA,CAAAiB,KAAA;IAClC,MAAAG,YAAU,GAAAP,QAAA,QACVvB,EAAA,CAAG+B,CAAA,CAAE,KAAM,GACX/B,EAAA,CAAG+B,CAAG,UACP/B,EAAA,CAAAgC,EAAA,cAAA7C,KAAA,CAAA8C,QAAA,IAAAP,OAAA,CAAAC,KAAA,EAEK;IACJ,MAAAO,YAAU,GAAAX,QAAA,QACVvB,EAAA,CAAG+B,CAAA,CAAE,KAAM,GACX/B,EAAA,CAAG+B,CAAG,UACP/B,EAAA,CAAAgC,EAAA,cAAA7C,KAAA,CAAA8C,QAAA,IAAAL,MAAA,CAAAD,KAAA,EAEK;IACJ,MAAAQ,QAAe,GAAAZ,QAAA,OAAuB;MACtC;QAAAR,KAAA;QAAAC,GAA2B;QAAAC,OAAA;QAAAC,OAAA;QAAAC;MAAA,IAAAL,SAAA,CAAAa,KAAA;MAC3B,IAAIS,UAAA,GAAanB,OAAU,GAAAF,KAAA;MAErB,IAAAsB,UAAA,GAAgBnB,OAAA,GAAWH,KAAA;MAC3B,MAAAuB,MAAA,GAAAtB,GAAY,GAAKuB,IAAA,CAAAC,EAAU;MAC3B,MAAAC,SAAA,GAAYF,IAAK,CAAAG,GAAA,CAAIJ,MAAM;MACpB,MAAAK,SAAA,GAAAJ,IAAA,CAAAK,GAAa,CAAAN,MAAA;MACbF,UAAA,GAAAA,UAAA,GAAaK,SAAa,GAAAJ,UAAU,GAASM,SAAA;MAE1DN,UAA6B,GAAAA,UAAA,GAAAI,SAAA,GAAAxB,OAAA,GAAAF,KAAA,GAAA4B,SAAA;MAC3B,MAAAE,KAAA;QACA/B,SAAA,WAAYC,KAAA,YAAqCC,GAAA,kBAAAoB,UAAA,OAAAC,UAAA;QACnDS,UAAA,EAAA3B,gBAAA;MACA;MACQ,IAAAP,IAAA,CAAAe,KAAA,CAAAzC,IAAW,KAAAG,KAAkB,CAAAC,OAAA,CAAAJ,IAAA;QACrC2D,KAAA,CAAAE,QAAA,GAAAF,KAAA,CAAAG,SAAA;MACA;MACD,OAAAH,KAAA;IAED;IACE,MAAAI,QAAqB,GAAA1B,QAAA,OAAS,GAAMb,WAAA,CAAAiB,KAAc,GAAM,OAAAxC,KAAA,CAAAqC,OAAA,CAAAC,MAAA;IAC1D,SAAAyB,KAAA;MAEAC,uBAAgB;MACUvD,iBAAA,oBAAAA,iBAAA;MACJwD,QAAA,CAAAC,IAAA,CAAAR,KAAA,CAAAS,QAAA,GAAAzD,YAAA;MACX0D,IAAA;IACT;IACF,SAAAC,sBAAA;MAEA,MAAAC,cAAiC,GAAAC,QAAA,CAAA3B,CAAA;QACzB,QAAAA,CAAA,CAAA4B,IAAA;UACJ,KAAAC,UAAgB,CAAAC,GAAA;YAAA1E,KAEE,CAAA2E,kBAAA,IAAAZ,IAAA;YACd;UACA,KAAAU,UAAA,CAAAG,KAAA;YAAAC,UAEc;YACH;UACX,KAAAJ,UAAA,CAAAK,IAAA;YAAAC,IAEc;YACT;UACL,KAAAN,UAAA,CAAAO,EAAA;YAAAC,aAEc;YACd;UACA,KAAAR,UAAA,CAAAS,KAAA;YAAAC,IAEc;YACT;UACL,KAAAV,UAAA,CAAAW,IAAA;YAAAH,aAEc;YACd;QACA;MAAA,CACJ;MACF,MAACI,iBAAA,GAAAd,QAAA,CAAA3B,CAAA;QACK,MAAA0C,KAAA,GAAA1C,CAAA,CAAA2C,MAAA,IAA6B3C,CAAA,CAAA4C,MAAC;QAC5BP,aAAA,CAAQK,KAAE,OAAU,QAAE;UACdG,QAAA,EAAAzF,KAAA,CAAAyF,QAAY;UACxBzD,gBAAgB;QAAA,EAChB;MAAkB,EACpB;MACFZ,kBAAC,CAAAsE,GAAA;QAEDC,gBAAA,CAAmB1B,QAAU,aAAAK,cAAA;QACVqB,gBAAA,CAAA1B,QAAA,EAAU,SAAAoB,iBAAyB;MACpD,CAAiB;IAAoC;IAEzD,SAAArB,wBAAA;MAEA5C,kBAAmC,CAAAwE,IAAA;IACjC;IACF,SAAAC,cAAA;MAEAvE,OAAS,CAAgBkB,KAAA;IACvB;IACF,SAAAsD,eAAAlD,CAAA;MAEAtB,OAAS,CAAAkB,KAAA;MACPI,CAAA,CAAAmD,MAAQ,CAAQC,GAAA,GAAArF,CAAA;IACf;IACH,SAAAsF,gBAAArD,CAAA;MAEA,IAAAtB,OAAA,CAAAkB,KAAA,IAAAI,CAAA,CAAyBsD,MAAe,WAAAjF,OAAA,CAAAuB,KAAA,EACtC;MACAb,SAAA,CAAUa,KAAA,CAAMR,gBAAmB;MAEnC,MAAM;QAAEF,OAAA;QAASC;MAAQ,IAAIJ,SAAU,CAAAa,KAAA;MACvC,MAAM2D,MAAA,GAASvD,CAAE,CAAAwD,KAAA;MACjB,MAAMC,MAAA,GAASzD,CAAE,CAAA0D,KAAA;MAEX,MAAAC,WAAA,GAAchC,QAAS,CAACiC,EAAmB;QAC/C7E,SAAA,CAAUa,KAAQ;UAChB,GAAGb,SAAU,CAAAa,KAAA;UACbV,OAAA,EAASA,OAAU,GAAA0E,EAAA,CAAGJ,KAAQ,GAAAD,MAAA;UAC9BpE,OAAA,EAASA,OAAU,GAAAyE,EAAA,CAAGF,KAAQ,GAAAD;QAAA,CAChC;MAAA,CACD;MACD,MAAMI,eAAkB,GAAAd,gBAAA,CAAiB1B,QAAU,eAAasC,WAAW;MAC1DZ,gBAAA,CAAA1B,QAAA,EAAU,WAAW,MAAM;QAC1BwC,eAAA;MAAA,CACjB;MAED7D,CAAA,CAAE8D,cAAe;IAAA;IAGnB,SAASC,KAAQA,CAAA;MACfhF,SAAA,CAAUa,KAAQ;QAChBZ,KAAO;QACPC,GAAK;QACLC,OAAS;QACTC,OAAS;QACTC,gBAAkB;MAAA,CACpB;IAAA;IAGF,SAAS6C,UAAaA,CAAA;MACpB,IAAIvD,OAAA,CAAQkB,KAAO,EAEb;MACA,MAAAoE,SAAA,GAAAC,MAAoB,CAAA3G,KAAA;MACpB,MAAA4G,UAAA,GAAAC,MAAmB,CAAMC,MAAA,CAAA9G,KAAA;MAC/B,MAAM+G,WAAmB,GAAAxF,IAAA,CAAAe,KAAA,CAAAzC,IAAU;MAC7B,MAAAmH,KAAA,GAAAJ,UAAqB,CAAAK,SAAe,CAAAC,CAAA,IAAAA,CAAA,CAAArH,IAAA,KAAAkH,WAAA;MAC1C,MAAaI,SAAA,IAAMH,KAAU,QAAAN,SAAU,CAAAtE,MAAA;MACjCb,IAAA,CAAAe,KAAA,GAAAtC,KAAA,CAAA0G,SAAA,CAAAS,SAAA;MACRV,KAAA;IAEA;IACQ,SAAAW,aAAoBA,CAAAJ,KAAA;MACd,MAAAK,GAAA,GAAAvH,KAAA,CAAAqC,OAAA,CAAAC,MAAwB;MACtCf,WAAA,CAAAiB,KAAA,IAAA0E,KAAA,GAAAK,GAAA,IAAAA,GAAA;IAEA;IACE,SAAYxC,KAAA;MACE,IAAAxC,OAAA,CAAAC,KAAA,KAAAxC,KAAA,CAAA8C,QAAqB,EACrC;MAEAwE,aAAgB,CAAA/F,WAAA,CAAAiB,KAAA;IACd;IACc,SAAA2C,KAAA;MAChB,IAAA1C,MAAA,CAAAD,KAAA,KAAAxC,KAAA,CAAA8C,QAAA,EAEA;MACEwE,aAAmB,CAAA/F,WAAA,CAAAiB,KAAA;IACnB;IACA,SAAQyC,aAAqBA,CAAAuC,MAAA,EAAAC,OAAA;MAAqB,IAAAnG,OAAA,CACtCkB,KAAM,EAChB;MAAW,MACO;QAAAkF,QAAA;QAAAC;MAAA,IAAA3H,KAAA;MAAA,MACf;QAAAyF,QAAA;QAAAmC,SAAA;QAAA5F;MAAA;QACLyD,QAAA,EAAAzF,KAAA,CAAAyF,QAAA;QACAmC,SAAgB;QACd5F,gBAAK;QACC,GAAAyF;MACF,CAAU;MAAqB,QAAAD,MAClB;QACb;UACF,IAAA7F,SAAA,CAAAa,KAAA,CAAAZ,KAAA,GAAA8F,QAAA;YACA/F,SAAA,CAAAa,KAAA,CAAAZ,KAAA,GAAAiG,MAAA,CAAAC,UAAA,EAAAnG,SAAA,CAAAa,KAAA,CAAAZ,KAAA,GAAA6D,QAAA,EAAAsC,OAAA;UAAA;UAEI;QACQ;UAAqB,IAAApG,SAClB,CAAAa,KAAA,CAAAZ,KAAc,GAAA+F,QAAA;YAC3BhG,SAAA,CAAAa,KAAA,CAAAZ,KAAA,GAAAiG,MAAA,CAAAC,UAAA,EAAAnG,SAAA,CAAAa,KAAA,CAAAZ,KAAA,GAAA6D,QAAA,EAAAsC,OAAA;UAAA;UAEF;QACF,KAAK;UACHpG,SAAA,CAAUa,KAAA,CAAMX,GAAO,IAAA+F,SAAA;UAClBxD,IAAA,WAAUzC,SAAU,CAAAa,KAAA,CAAMX,GAAG;UAClC;QACF,KAAK;UACHF,SAAA,CAAUa,KAAA,CAAMX,GAAO,IAAA+F,SAAA;UAClBxD,IAAA,WAAUzC,SAAU,CAAAa,KAAA,CAAMX,GAAG;UAClC;MAAA;MAEJF,SAAA,CAAUa,KAAA,CAAMR,gBAAmB,GAAAA,gBAAA;IAAA;IAGrC,SAASgG,oBAAoBC,KAAoB;MAC3C,IAAAC,GAAA;MACF,MAAAA,GAAqB,GAAAD,KAAA,CAAAE,MAAA,qBAAAD,GAAA,CAAAE,WAAA;QACvBH,KAAA,CAAAvB,cAAA;MAAA;IAGF;IACE,SAAA2B,gBAA8BA,CAAA;MACvB,IAAArI,KAAA,CAAA2E,kBAAA;QACPZ,IAAA;MAAA;IAGF;IACM,SAAGuE,YAASA,CAAA1F,CAAA;MAEZ,KAAAA,CAAE,CAAA2F,OAAA,EACJ;MACO,IAAA3F,CAAA,CAAA2C,MAAA;QACT3C,CAAA,CAAA8D,cAAa;QACX,OAAiB;MACjB,CAAO,UAAA9D,CAAA,CAAA2C,MAAA;QACT3C,CAAA,CAAA8D,cAAA;QACF;MAEA;IACE;IACQ8B,KAAA,CAAA9F,UAAA,EAAe;MACjB+F,QAAA,OAAiB;QACnB,MAAAC,IAAQ,GAAQvH,OAAA,CAAAqB,KAAA;QAClB,MAAAkG,IAAA,oBAAAA,IAAA,CAAAC,QAAA;UACDrH,OAAA,CAAAkB,KAAA;QAAA;MAGH,CAAM;IACJ,CAAM;IACNgG,KAAA,CAAAjH,WAAe,EAAGqH,GAAA;MACnBjC,KAAA;MAEDvC,IAAA,SAAgB,EAAAwE,GAAA;IACd,CAAsB;IAEFC,SAAA;MAAwCxE,qBACjD;MACX5D,iBAAC,GAAAkF,gBAAA,UAAA2C,YAAA;QAGcQ,OAAA;MACf,CAAS;MACVpI,YAAA,GAAAuD,QAAA,CAAAC,IAAA,CAAAR,KAAA,CAAAS,QAAA;MAEYF,QAAA,CAAAC,IAAA,CAAAR,KAAA,CAAAS,QAAA;IAAA;IAAA4E,MAAA;MAAAzB;IAAA,CAIX;IACF,OAAC,CAAA0B,IAAA,EAAAC,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
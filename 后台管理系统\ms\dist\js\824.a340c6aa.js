"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[824],{2824:function(e,a,t){t.r(a),t.d(a,{default:function(){return h}});t(4114),t(4603),t(7566),t(8721);var l=t(6768),r=t(144),o=t(4232),n=t(1219),i=t(2933),s=t(7477),u=t(4373);const c={class:"course-list-container"},d={class:"filter-container"},p={class:"card-header"},k={key:1,class:"no-material"},m={class:"pagination-container"},v={class:"dialog-footer"},_="http://localhost:3000";var b={__name:"CourseList",setup(e){const a=u.A.create({baseURL:_});a.interceptors.request.use(e=>{const a=localStorage.getItem("token");return a&&(e.headers.Authorization=`Bearer ${a}`),e},e=>Promise.reject(e));const t=e=>{if(!e)return"-";const a=new Date(e);if(isNaN(a.getTime()))return e;const t=a.getFullYear(),l=String(a.getMonth()+1).padStart(2,"0"),r=String(a.getDate()).padStart(2,"0"),o=String(a.getHours()).padStart(2,"0"),n=String(a.getMinutes()).padStart(2,"0"),i=String(a.getSeconds()).padStart(2,"0");return`${t}-${l}-${r} ${o}:${n}:${i}`},b=(0,r.KR)(!0),g=(0,r.KR)(1),f=(0,r.KR)(10),h=(0,r.KR)(0),w=(0,r.KR)(!1),y=(0,r.KR)("新增课程"),F=(0,r.KR)(null),C=(0,r.KR)([]),R=(0,r.KR)(`${_}/api/courses`),$=(0,r.KR)(!1),S=(0,r.KR)(null),K=(0,l.EW)(()=>({Authorization:`Bearer ${localStorage.getItem("token")||""}`})),W=(0,r.KR)([]),L=(0,r.Kh)({title:""}),V=(0,r.Kh)({id:null,title:"",description:"",material_path:""}),z={title:[{required:!0,message:"请输入课程标题",trigger:"blur"}]},U=async()=>{b.value=!0;try{const e=await a.get("/api/courses");e.data.success?(W.value=e.data.data,h.value=e.data.count):n.nk.error(e.data.message||"获取课程列表失败")}catch(e){console.error("获取课程列表失败:",e),e.response&&401===e.response.status?n.nk.error("登录已过期，请重新登录"):n.nk.error("获取课程列表失败，请检查网络连接")}finally{b.value=!1}};(0,l.sV)(()=>{U()});const x=()=>{L.title="",U()},B=async()=>{b.value=!0;try{const e=await a.get("/api/courses/search",{params:{keyword:L.title}});e.data.success?(W.value=e.data.data,h.value=e.data.count,n.nk.success("搜索完成")):n.nk.error(e.data.message||"搜索失败")}catch(e){console.error("搜索失败:",e),n.nk.error("搜索失败，请检查网络连接")}finally{b.value=!1}},A=()=>{y.value="新增课程",w.value=!0,V.id=null,V.title="",V.description="",V.material_path="",S.value=null,C.value=[]},D=e=>{y.value="编辑课程",w.value=!0,V.id=e.id,V.title=e.title,V.description=e.description,V.material_path=e.material_path,S.value=null,C.value=[],e.material_path&&C.value.push({name:e.material_path.split("/").pop(),url:`${_}/${e.material_path}`})},T=e=>{i.s.confirm(`确定要删除课程 ${e.title} 吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=await a.delete(`/api/courses/${e.id}`);t.data.success?(n.nk.success(`课程 ${e.title} 已删除`),U()):n.nk.error(t.data.message||"删除课程失败")}catch(t){console.error("删除课程失败:",t),n.nk.error("删除课程失败，请检查网络连接")}}).catch(()=>{})},j=async e=>{if(e.material_path)try{n.nk.success(`正在下载 ${e.title} 的课件`);const l=await a.get(`/api/courses/${e.id}/download`,{responseType:"blob"});let r="";const o=l.headers["content-disposition"];if(o){const e=o.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);if(e&&e[1]){r=e[1].replace(/['"]/g,"");try{r=decodeURIComponent(r)}catch(t){console.error("解码文件名失败",t)}}}r||(r=e.material_path.split("/").pop());const i=new Blob([l.data]),s=window.URL.createObjectURL(i),u=document.createElement("a");u.href=s,u.setAttribute("download",r),document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s)}catch(l){console.error("下载失败:",l),n.nk.error("下载课件失败，请检查权限或网络连接")}else n.nk.warning("该课程没有上传课件")},E=e=>{S.value=e.file;const a=e.file.name;n.nk.success(`文件 ${a} 已选择`),e.onSuccess()},I=async()=>{F.value&&await F.value.validate(async e=>{if(!e)return!1;$.value=!0;try{const e=new FormData;let t;e.append("title",V.title),e.append("description",V.description),S.value&&(e.append("originalFileName",S.value.name),e.append("material",S.value)),V.id?(t=await a.put(`/api/courses/${V.id}`,e),t.data.success?n.nk.success(`课程 ${V.title} 信息已更新`):n.nk.error(t.data.message||"更新课程失败")):(t=await a.post("/api/courses",e),t.data.success?n.nk.success(`课程 ${V.title} 添加成功`):n.nk.error(t.data.message||"添加课程失败")),w.value=!1,U()}catch(t){console.error("提交表单失败:",t),n.nk.error("提交失败，请检查网络连接")}finally{$.value=!1}})},M=e=>{e.url&&window.open(e.url,"_blank")},N=()=>{C.value=[],S.value=null},P=()=>{},X=e=>{const a=10485760;return!(e.size>a)||(n.nk.error("文件大小不能超过10MB"),!1)},q=e=>{f.value=e,g.value=1,U()},O=e=>{g.value=e,U()};return(e,a)=>{const n=(0,l.g2)("el-input"),i=(0,l.g2)("el-form-item"),u=(0,l.g2)("el-button"),_=(0,l.g2)("el-form"),S=(0,l.g2)("el-card"),U=(0,l.g2)("el-table-column"),H=(0,l.g2)("el-icon"),Y=(0,l.g2)("el-table"),G=(0,l.g2)("el-pagination"),J=(0,l.g2)("el-upload"),Q=(0,l.g2)("el-dialog"),Z=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",c,[(0,l.bF)(S,{class:"filter-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",d,[(0,l.bF)(_,{model:L,inline:""},{default:(0,l.k6)(()=>[(0,l.bF)(i,{label:"课程标题"},{default:(0,l.k6)(()=>[(0,l.bF)(n,{modelValue:L.title,"onUpdate:modelValue":a[0]||(a[0]=e=>L.title=e),placeholder:"请输入课程标题",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(i,null,{default:(0,l.k6)(()=>[(0,l.bF)(u,{type:"primary",onClick:B},{default:(0,l.k6)(()=>a[5]||(a[5]=[(0,l.eW)("搜索")])),_:1,__:[5]}),(0,l.bF)(u,{onClick:x},{default:(0,l.k6)(()=>a[6]||(a[6]=[(0,l.eW)("重置")])),_:1,__:[6]})]),_:1})]),_:1},8,["model"])])]),_:1}),(0,l.bF)(S,{class:"table-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",p,[a[8]||(a[8]=(0,l.Lk)("span",null,"岗前培训课程",-1)),(0,l.Lk)("div",null,[(0,l.bF)(u,{type:"primary",onClick:A},{default:(0,l.k6)(()=>a[7]||(a[7]=[(0,l.eW)("新增课程")])),_:1,__:[7]})])])]),default:(0,l.k6)(()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(Y,{data:W.value,stripe:"",border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(U,{type:"index",width:"50"}),(0,l.bF)(U,{prop:"title",label:"课程标题","min-width":"200"}),(0,l.bF)(U,{prop:"description",label:"课程描述","min-width":"300","show-overflow-tooltip":""}),(0,l.bF)(U,{label:"课件",width:"120",align:"center"},{default:(0,l.k6)(e=>[e.row.material_path?((0,l.uX)(),(0,l.Wv)(u,{key:0,size:"small",type:"success",link:"",onClick:a=>j(e.row)},{default:(0,l.k6)(()=>[(0,l.bF)(H,null,{default:(0,l.k6)(()=>[(0,l.bF)((0,r.R1)(s.Download))]),_:1}),a[9]||(a[9]=(0,l.eW)(" 下载课件 "))]),_:2,__:[9]},1032,["onClick"])):((0,l.uX)(),(0,l.CE)("span",k,"无"))]),_:1}),(0,l.bF)(U,{prop:"created_at",label:"创建时间",width:"160"},{default:(0,l.k6)(e=>[(0,l.eW)((0,o.v_)(t(e.row.created_at)),1)]),_:1}),(0,l.bF)(U,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(u,{type:"primary",size:"small",onClick:a=>D(e.row)},{default:(0,l.k6)(()=>a[10]||(a[10]=[(0,l.eW)("编辑")])),_:2,__:[10]},1032,["onClick"]),(0,l.bF)(u,{type:"danger",size:"small",onClick:a=>T(e.row)},{default:(0,l.k6)(()=>a[11]||(a[11]=[(0,l.eW)("删除")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Z,b.value]]),(0,l.Lk)("div",m,[(0,l.bF)(G,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":g.value,"page-sizes":[10,20,50,100],"page-size":f.value,total:h.value,onSizeChange:q,onCurrentChange:O},null,8,["current-page","page-size","total"])])]),_:1}),(0,l.bF)(Q,{title:y.value,modelValue:w.value,"onUpdate:modelValue":a[4]||(a[4]=e=>w.value=e),width:"600px"},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",v,[(0,l.bF)(u,{onClick:a[3]||(a[3]=e=>w.value=!1)},{default:(0,l.k6)(()=>a[14]||(a[14]=[(0,l.eW)("取消")])),_:1,__:[14]}),(0,l.bF)(u,{type:"primary",onClick:I,loading:$.value},{default:(0,l.k6)(()=>a[15]||(a[15]=[(0,l.eW)("确定")])),_:1,__:[15]},8,["loading"])])]),default:(0,l.k6)(()=>[(0,l.bF)(_,{model:V,rules:z,ref_key:"courseFormRef",ref:F,"label-width":"80px"},{default:(0,l.k6)(()=>[(0,l.bF)(i,{label:"课程标题",prop:"title"},{default:(0,l.k6)(()=>[(0,l.bF)(n,{modelValue:V.title,"onUpdate:modelValue":a[1]||(a[1]=e=>V.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,l.bF)(i,{label:"课程描述",prop:"description"},{default:(0,l.k6)(()=>[(0,l.bF)(n,{type:"textarea",modelValue:V.description,"onUpdate:modelValue":a[2]||(a[2]=e=>V.description=e),placeholder:"请输入课程描述",rows:"4"},null,8,["modelValue"])]),_:1}),(0,l.bF)(i,{label:"课件文件"},{default:(0,l.k6)(()=>[(0,l.bF)(J,{class:"material-upload",action:R.value,headers:K.value,"http-request":E,"on-preview":M,"on-remove":N,"on-success":P,"before-upload":X,limit:1,"file-list":C.value},{tip:(0,l.k6)(()=>a[13]||(a[13]=[(0,l.Lk)("div",{class:"el-upload__tip"}," 支持上传PDF、Word、PPT等格式文件，大小不超过10MB ",-1)])),default:(0,l.k6)(()=>[(0,l.bF)(u,{type:"primary"},{default:(0,l.k6)(()=>a[12]||(a[12]=[(0,l.eW)("点击上传")])),_:1,__:[12]})]),_:1},8,["action","headers","file-list"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},g=t(1241);const f=(0,g.A)(b,[["__scopeId","data-v-981232ac"]]);var h=f}}]);
//# sourceMappingURL=824.a340c6aa.js.map
(function(){"use strict";var e={653:function(e,t,o){o(4114);var r=o(4373),a=o(5129),n=o(2151),l=o(1219);const s=r.A.create({baseURL:"http://localhost:3000",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers["Authorization"]=`Bearer ${t}`),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{const{response:t}=e;if(t)switch(t.status){case 401:localStorage.removeItem("token"),localStorage.removeItem("userId"),localStorage.removeItem("userRole"),localStorage.removeItem("studentId"),a.A.dispatch("logout"),"/login"!==n.A.currentRoute.value.path&&(n.A.push("/login"),(0,l.nk)({message:"登录已过期，请重新登录",type:"error",duration:3e3}));break;case 403:(0,l.nk)({message:"您没有权限执行此操作",type:"error",duration:3e3});break;default:(0,l.nk)({message:t.data.message||"请求失败",type:"error",duration:3e3})}else(0,l.nk)({message:"网络错误，请检查您的网络连接",type:"error",duration:3e3});return Promise.reject(e)});t.A=s},1927:function(e,t,o){o(4979);var r=o(5130),a=o(6768);const n={id:"app"};function l(e,t,o,r,l,s){const i=(0,a.g2)("router-view");return(0,a.uX)(),(0,a.CE)("div",n,[(0,a.bF)(i)])}var s=o(4373),i={name:"App",setup(){(0,a.sV)(()=>{u()})}};function u(){const e=localStorage.getItem("userId"),t=localStorage.getItem("userRole");if("student"===t&&!localStorage.getItem("studentId")&&e){console.log("尝试获取学生ID...");const t={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL||"http://localhost:3000/api",o=localStorage.getItem("token");if(!o)return void console.error("未找到登录令牌，无法获取学生ID");const r={Authorization:`Bearer ${o}`};s.A.get(`${t}/students/by-user/${e}`,{headers:r}).then(e=>{e.data.success&&e.data.data?(localStorage.setItem("studentId",e.data.data.id),console.log("成功获取并保存学生ID:",e.data.data.id)):console.error("无法获取学生ID")}).catch(e=>{console.error("获取学生ID失败:",e)})}}var c=o(1241);const d=(0,c.A)(i,[["render",l]]);var m=d,p=o(2151),f=o(5129),g=o(653),h=o(3049),k=(o(4188),o(7477));s.A.defaults.baseURL="http://localhost:3000";const v=localStorage.getItem("token");if(v)try{const e=v.split(".")[1];if(e){const t=JSON.parse(atob(e)),o=1e3*t.exp;Date.now()>=o&&(localStorage.removeItem("token"),localStorage.removeItem("userId"),localStorage.removeItem("userRole"),localStorage.removeItem("studentId"))}else localStorage.removeItem("token"),localStorage.removeItem("userId"),localStorage.removeItem("userRole"),localStorage.removeItem("studentId")}catch(_){console.error("解析token失败:",_),localStorage.removeItem("token"),localStorage.removeItem("userId"),localStorage.removeItem("userRole"),localStorage.removeItem("studentId")}const b=(0,r.Ef)(m);b.config.globalProperties.$api=g.A,b.config.globalProperties.$axios=s.A;for(const[w,y]of Object.entries(k))b.component(w,y);b.use(f.A).use(p.A).use(h.A).mount("#app")},2151:function(e,t,o){o.d(t,{A:function(){return B}});o(4979);var r=o(1387),a=(o(4114),o(6768)),n=o(5130),l=o(4232),s=o(144),i=o(3153),u=o(1219),c=o(2933),d=o(7377),m=o(47),p=o(2006),f=o(9623),g=o(4373);const h={class:"app-container"},k={class:"logo"},v={class:"header-left"},b={class:"header-right"},_={class:"user-info"},w={class:"dialog-footer"},y="http://localhost:3000/api";var S={__name:"AppLayout",setup(e){const t=(0,s.KR)(!1),o=(0,s.KR)(null),S=(0,s.KR)(!1),I=(0,s.Kh)({phone:"",newPassword:"",confirmPassword:""}),F=(e,t,o)=>{""===t?o(new Error("请再次输入密码")):t!==I.newPassword?o(new Error("两次输入密码不一致!")):o()},E={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:F,trigger:"blur"}]},x=()=>{t.value=!0,Object.assign(I,{phone:"",newPassword:"",confirmPassword:""})},R=async()=>{if(o.value)try{await o.value.validate(async e=>{if(e){S.value=!0;try{const e=await g.A.get(`${y}/auth/check-phone/${I.phone}`);if(!e.data.success)return void u.nk.error("该手机号未注册或不存在");await g.A.post(`${y}/auth/change-password-by-phone`,{phone:I.phone,newPassword:I.newPassword});u.nk.success("密码修改成功"),t.value=!1}catch(o){console.error("修改密码失败:",o),u.nk.error(o.response?.data?.message||"修改密码失败，请稍后重试")}finally{S.value=!1}}})}catch(e){S.value=!1,u.nk.error("表单验证失败")}},L=(0,r.rd)(),A=(0,r.lq)(),P=(0,s.KR)(!1),O=(0,s.KR)(localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")).name:"用户"),C=(0,a.EW)(()=>A.path),U=(0,a.EW)(()=>A.meta.title||"实习生列表"),T=(0,a.EW)(()=>{const e=localStorage.getItem("userRole");return"student"===e}),W=()=>{P.value=!P.value},V=()=>{c.s.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.clear(),L.push("/login"),u.nk.success("已退出登录")}).catch(()=>{})};return(e,r)=>{const u=(0,a.g2)("User"),c=(0,a.g2)("el-icon"),g=(0,a.g2)("el-menu-item"),y=(0,a.g2)("el-sub-menu"),F=(0,a.g2)("Reading"),L=(0,a.g2)("DocumentChecked"),A=(0,a.g2)("Refresh"),N=(0,a.g2)("UserFilled"),j=(0,a.g2)("el-menu"),K=(0,a.g2)("el-scrollbar"),$=(0,a.g2)("el-aside"),B=(0,a.g2)("el-breadcrumb-item"),D=(0,a.g2)("el-breadcrumb"),q=(0,a.g2)("el-avatar"),M=(0,a.g2)("CaretBottom"),X=(0,a.g2)("el-dropdown-item"),z=(0,a.g2)("el-dropdown-menu"),G=(0,a.g2)("el-dropdown"),J=(0,a.g2)("el-header"),Q=(0,a.g2)("router-view"),H=(0,a.g2)("el-main"),Z=(0,a.g2)("el-container");return(0,a.uX)(),(0,a.CE)("div",h,[(0,a.bF)(Z,{class:"layout-container"},{default:(0,a.k6)(()=>[(0,a.bF)($,{width:P.value?"64px":"220px",class:"aside"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",k,[r[5]||(r[5]=(0,a.Lk)("img",{src:i,alt:"logo"},null,-1)),(0,a.bo)((0,a.Lk)("h1",null,"实习生学籍管理系统",512),[[n.aG,!P.value]])]),(0,a.bF)(K,null,{default:(0,a.k6)(()=>[(0,a.bF)(j,{"default-active":C.value,class:"el-menu-vertical",collapse:P.value,"background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",router:"","collapse-transition":!1},{default:(0,a.k6)(()=>[T.value?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(y,{key:0,index:"/students"},{title:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(u)]),_:1}),r[6]||(r[6]=(0,a.Lk)("span",null,"实习生管理",-1))]),default:(0,a.k6)(()=>[(0,a.bF)(g,{index:"/students/list"},{default:(0,a.k6)(()=>r[7]||(r[7]=[(0,a.eW)("实习生列表")])),_:1,__:[7]})]),_:1})),(0,a.bF)(y,{index:"/courses"},{title:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(F)]),_:1}),r[8]||(r[8]=(0,a.Lk)("span",null,"岗前培训",-1))]),default:(0,a.k6)(()=>[(0,a.bF)(g,{index:"/courses/list"},{default:(0,a.k6)(()=>r[9]||(r[9]=[(0,a.eW)("课程管理")])),_:1,__:[9]})]),_:1}),(0,a.bF)(y,{index:"/exams"},{title:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(L)]),_:1}),r[10]||(r[10]=(0,a.Lk)("span",null,"考核管理",-1))]),default:(0,a.k6)(()=>[(0,a.bF)(g,{index:"/exams/list"},{default:(0,a.k6)(()=>r[11]||(r[11]=[(0,a.eW)("考试列表")])),_:1,__:[11]}),T.value?((0,a.uX)(),(0,a.Wv)(g,{key:0,index:"/exams/my-results"},{default:(0,a.k6)(()=>r[12]||(r[12]=[(0,a.eW)("我的考试成绩")])),_:1,__:[12]})):(0,a.Q3)("",!0)]),_:1}),T.value?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(y,{key:1,index:"/rotations"},{title:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(A)]),_:1}),r[13]||(r[13]=(0,a.Lk)("span",null,"轮转管理",-1))]),default:(0,a.k6)(()=>[(0,a.bF)(g,{index:"/rotations/list"},{default:(0,a.k6)(()=>r[14]||(r[14]=[(0,a.eW)("轮转记录")])),_:1,__:[14]}),(0,a.bF)(g,{index:"/rotations/graduation"},{default:(0,a.k6)(()=>r[15]||(r[15]=[(0,a.eW)("结业考核")])),_:1,__:[15]})]),_:1})),T.value?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(y,{key:2,index:"/users"},{title:(0,a.k6)(()=>[(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(N)]),_:1}),r[16]||(r[16]=(0,a.Lk)("span",null,"用户管理",-1))]),default:(0,a.k6)(()=>[(0,a.bF)(g,{index:"/users/list"},{default:(0,a.k6)(()=>r[17]||(r[17]=[(0,a.eW)("用户列表")])),_:1,__:[17]})]),_:1}))]),_:1},8,["default-active","collapse"])]),_:1})]),_:1},8,["width"]),(0,a.bF)(Z,{class:"main-container"},{default:(0,a.k6)(()=>[(0,a.bF)(J,{class:"header"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",v,[(0,a.bF)(c,{class:"fold-icon",onClick:W},{default:(0,a.k6)(()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(P.value?"Expand":"Fold")))]),_:1}),(0,a.bF)(D,{separator:"/"},{default:(0,a.k6)(()=>[(0,a.bF)(B,{to:{path:"/"}},{default:(0,a.k6)(()=>r[18]||(r[18]=[(0,a.eW)("首页")])),_:1,__:[18]}),(0,a.bF)(B,null,{default:(0,a.k6)(()=>[(0,a.eW)((0,l.v_)(U.value),1)]),_:1})]),_:1})]),(0,a.Lk)("div",b,[(0,a.bF)(G,{trigger:"click"},{dropdown:(0,a.k6)(()=>[(0,a.bF)(z,null,{default:(0,a.k6)(()=>[(0,a.bF)(X,{onClick:x},{default:(0,a.k6)(()=>r[19]||(r[19]=[(0,a.eW)("修改密码")])),_:1,__:[19]}),(0,a.bF)(X,{onClick:V},{default:(0,a.k6)(()=>r[20]||(r[20]=[(0,a.eW)("退出登录")])),_:1,__:[20]})]),_:1})]),default:(0,a.k6)(()=>[(0,a.Lk)("div",_,[(0,a.bF)(q,{size:30,src:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}),(0,a.Lk)("span",null,(0,l.v_)(O.value),1),(0,a.bF)(c,null,{default:(0,a.k6)(()=>[(0,a.bF)(M)]),_:1})])]),_:1})])]),_:1}),(0,a.bF)(H,{class:"main"},{default:(0,a.k6)(()=>[(0,a.bF)(Q)]),_:1})]),_:1})]),_:1}),(0,a.bF)((0,s.R1)(d.kZ),{title:"修改密码",modelValue:t.value,"onUpdate:modelValue":r[4]||(r[4]=e=>t.value=e),width:"400px",center:"","destroy-on-close":""},{footer:(0,a.k6)(()=>[(0,a.Lk)("span",w,[(0,a.bF)((0,s.R1)(m.S2),{onClick:r[3]||(r[3]=e=>t.value=!1)},{default:(0,a.k6)(()=>r[21]||(r[21]=[(0,a.eW)("取消")])),_:1,__:[21]}),(0,a.bF)((0,s.R1)(m.S2),{type:"primary",loading:S.value,onClick:R},{default:(0,a.k6)(()=>r[22]||(r[22]=[(0,a.eW)("确定")])),_:1,__:[22]},8,["loading"])])]),default:(0,a.k6)(()=>[(0,a.bF)((0,s.R1)(p.US),{model:I,rules:E,ref_key:"changePasswordFormRef",ref:o,"label-width":"80px"},{default:(0,a.k6)(()=>[(0,a.bF)((0,s.R1)(p.xE),{label:"手机号",prop:"phone"},{default:(0,a.k6)(()=>[(0,a.bF)((0,s.R1)(f.WK),{modelValue:I.phone,"onUpdate:modelValue":r[0]||(r[0]=e=>I.phone=e),placeholder:"请输入注册时的手机号"},null,8,["modelValue"])]),_:1}),(0,a.bF)((0,s.R1)(p.xE),{label:"新密码",prop:"newPassword"},{default:(0,a.k6)(()=>[(0,a.bF)((0,s.R1)(f.WK),{modelValue:I.newPassword,"onUpdate:modelValue":r[1]||(r[1]=e=>I.newPassword=e),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),(0,a.bF)((0,s.R1)(p.xE),{label:"确认密码",prop:"confirmPassword"},{default:(0,a.k6)(()=>[(0,a.bF)((0,s.R1)(f.WK),{modelValue:I.confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=e=>I.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},I=o(1241);const F=(0,I.A)(S,[["__scopeId","data-v-38885764"]]);var E=F,x=o(7477);const R={class:"reset-password-container"},L={class:"reset-password-card"},A={class:"login-link"};var P={__name:"ResetPasswordView",setup(e){const t=(0,r.lq)(),o=(0,r.rd)(),n=(0,s.KR)(null),l=(0,s.KR)(!1),i=(0,s.KR)(""),c={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL||"http://localhost:3000/api",d=(0,s.Kh)({newPassword:"",confirmPassword:""}),m=(e,t,o)=>{""===t?o(new Error("请再次输入密码")):t!==d.newPassword?o(new Error("两次输入密码不一致!")):o()},p={newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:m,trigger:"blur"}]},f=async()=>{if(n.value)try{await n.value.validate(async e=>{if(e){l.value=!0;try{await g.A.post(`${c}/auth/reset-password`,{token:i.value,newPassword:d.newPassword});u.nk.success("密码重置成功，请使用新密码登录"),o.push("/login")}catch(t){console.error("重置密码失败:",t),u.nk.error(t.response?.data?.message||"重置密码失败，请检查链接是否有效")}finally{l.value=!1}}})}catch(e){l.value=!1,u.nk.error("表单验证失败")}};return(0,a.sV)(()=>{i.value=t.query.token,i.value||(u.nk.error("无效的重置链接，请重新获取"),o.push("/login"))}),(e,t)=>{const o=(0,a.g2)("el-input"),r=(0,a.g2)("el-form-item"),i=(0,a.g2)("el-button"),u=(0,a.g2)("router-link"),c=(0,a.g2)("el-form");return(0,a.uX)(),(0,a.CE)("div",R,[(0,a.Lk)("div",L,[t[6]||(t[6]=(0,a.Lk)("div",{class:"logo-wrapper"},[(0,a.Lk)("div",{class:"logo-icon"},[(0,a.Lk)("i",{class:"el-icon-key"})]),(0,a.Lk)("div",{class:"logo-text"}," 重置密码 ")],-1)),(0,a.bF)(c,{model:d,rules:p,ref_key:"resetFormRef",ref:n,class:"reset-form"},{default:(0,a.k6)(()=>[t[5]||(t[5]=(0,a.Lk)("p",{class:"form-subtitle"},"请输入新密码",-1)),(0,a.bF)(r,{prop:"newPassword"},{default:(0,a.k6)(()=>[(0,a.bF)(o,{modelValue:d.newPassword,"onUpdate:modelValue":t[0]||(t[0]=e=>d.newPassword=e),type:"password",placeholder:"新密码","prefix-icon":(0,s.R1)(x.Lock),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),(0,a.bF)(r,{prop:"confirmPassword"},{default:(0,a.k6)(()=>[(0,a.bF)(o,{modelValue:d.confirmPassword,"onUpdate:modelValue":t[1]||(t[1]=e=>d.confirmPassword=e),type:"password",placeholder:"确认新密码","prefix-icon":(0,s.R1)(x.Lock),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),(0,a.bF)(r,null,{default:(0,a.k6)(()=>[(0,a.bF)(i,{type:"primary",loading:l.value,onClick:f,class:"reset-button"},{default:(0,a.k6)(()=>t[2]||(t[2]=[(0,a.eW)(" 重置密码 ")])),_:1,__:[2]},8,["loading"])]),_:1}),(0,a.Lk)("div",A,[t[4]||(t[4]=(0,a.Lk)("span",null,"记住密码了？",-1)),(0,a.bF)(u,{to:"/login"},{default:(0,a.k6)(()=>t[3]||(t[3]=[(0,a.eW)("返回登录")])),_:1,__:[3]})])]),_:1,__:[5]},8,["model"])])])}}};const O=(0,I.A)(P,[["__scopeId","data-v-61343e2e"]]);var C=O;const U={class:"not-found"};function T(e,t,o,r,n,l){const s=(0,a.g2)("el-button");return(0,a.uX)(),(0,a.CE)("div",U,[t[1]||(t[1]=(0,a.Lk)("h1",null,"404",-1)),t[2]||(t[2]=(0,a.Lk)("p",null,"页面不存在",-1)),(0,a.bF)(s,{type:"primary",onClick:l.goHome},{default:(0,a.k6)(()=>t[0]||(t[0]=[(0,a.eW)("返回首页")])),_:1,__:[0]},8,["onClick"])])}var W={name:"NotFound",methods:{goHome(){this.$router.push("/")}}};const V=(0,I.A)(W,[["render",T],["__scopeId","data-v-d0400d02"]]);var N=V;const j=[{path:"/login",name:"Login",component:()=>o.e(964).then(o.bind(o,9964)),meta:{title:"登录"}},{path:"/reset-password",name:"resetPassword",component:C,meta:{title:"重置密码",requiresAuth:!1}},{path:"/",component:E,redirect:"/students/list",children:[{path:"students",name:"Students",redirect:"/students/list",meta:{title:"实习生管理",icon:"User"},children:[{path:"list",name:"StudentList",component:()=>o.e(75).then(o.bind(o,9075)),meta:{title:"实习生列表"}},{path:"detail/:id",name:"StudentDetail",component:()=>o.e(75).then(o.bind(o,9075)),meta:{title:"实习生详情",activeMenu:"/students/list"},hidden:!0}]},{path:"courses",name:"Courses",redirect:"/courses/list",meta:{title:"岗前培训",icon:"Reading"},children:[{path:"list",name:"CourseList",component:()=>o.e(824).then(o.bind(o,2824)),meta:{title:"课程管理"}}]},{path:"exams",name:"Exams",redirect:"/exams/list",meta:{title:"考核管理",icon:"DocumentChecked"},children:[{path:"list",name:"ExamList",component:()=>o.e(404).then(o.bind(o,6404)),meta:{title:"考试列表"}},{path:"questions/:id",name:"ExamQuestions",component:()=>o.e(404).then(o.bind(o,6404)),meta:{title:"试题管理",activeMenu:"/exams/list"},hidden:!0},{path:"results/:id",name:"ExamResults",component:()=>o.e(404).then(o.bind(o,6404)),meta:{title:"考试成绩",activeMenu:"/exams/list"},hidden:!0},{path:"take/:id",name:"ExamTaking",component:()=>o.e(271).then(o.bind(o,2271)),meta:{title:"参加考试",activeMenu:"/exams/list"},hidden:!0},{path:"my-results",name:"MyExamResults",component:()=>o.e(348).then(o.bind(o,8348)),meta:{title:"我的考试成绩",roles:["student"]}}]},{path:"rotations",name:"Rotations",redirect:"/rotations/list",meta:{title:"轮转管理",icon:"Refresh"},children:[{path:"list",name:"RotationList",component:()=>o.e(555).then(o.bind(o,3555)),meta:{title:"轮转记录"}},{path:"graduation",name:"GraduationExams",component:()=>o.e(309).then(o.bind(o,4309)),meta:{title:"结业考核"}}]},{path:"users",name:"Users",redirect:"/users/list",meta:{title:"用户管理",icon:"UserFilled"},children:[{path:"list",name:"UserList",component:()=>o.e(358).then(o.bind(o,3358)),meta:{title:"用户列表"}}]}]},{path:"/:pathMatch(.*)*",name:"NotFound",component:N,meta:{title:"404 - 页面不存在"}}],K=(0,r.aE)({history:(0,r.Bt)("/"),routes:j});function $(e){if(!e)return!0;try{const t=e.split(".")[1],o=JSON.parse(atob(t)),r=1e3*o.exp;return Date.now()>=r}catch(t){return console.error("解析token失败:",t),!0}}K.beforeEach((e,t,o)=>{const r=["/login","/reset-password"],a=!r.includes(e.path),n=localStorage.getItem("token");if(a&&!n)o({name:"Login"});else if(n){const t=$(n);t&&!r.includes(e.path)?(localStorage.removeItem("token"),localStorage.removeItem("userId"),localStorage.removeItem("userRole"),localStorage.removeItem("studentId"),o({name:"Login"})):o()}else o()});var B=K},3153:function(e,t,o){e.exports=o.p+"img/logo.211b096f.png"},5129:function(e,t,o){var r=o(782),a=o(4373);a.A.defaults.baseURL="http://localhost:3000",t.A=(0,r.y$)({state:{user:localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):null,token:localStorage.getItem("token")||"",role:localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")).role:""},getters:{isAuthenticated:e=>!!e.token,isAdmin:e=>"admin"==e.role,isTeacher:e=>"teacher"==e.role,isStudent:e=>"student"==e.role,userRole:e=>e.role,currentUser:e=>e.user},mutations:{SET_TOKEN(e,t){e.token=t},SET_USER(e,t){e.user=t},SET_ROLE(e,t){e.role=t},LOGOUT(e){e.token="",e.user=null,e.role=""}},actions:{async login({commit:e},t){try{const o=await a.A.post("/api/auth/login",t),{token:r,user:n}=o.data;return localStorage.setItem("token",r),localStorage.setItem("userId",n.id),localStorage.setItem("userRole",n.role),e("SET_TOKEN",r),e("SET_USER",n),e("SET_ROLE",n.role),a.A.defaults.headers.common["Authorization"]=`Bearer ${r}`,o}catch(o){throw o}},logout({commit:e}){localStorage.removeItem("token"),localStorage.removeItem("userId"),localStorage.removeItem("userRole"),e("LOGOUT"),delete a.A.defaults.headers.common["Authorization"]},async fetchUserProfile({commit:e}){try{const t=localStorage.getItem("token");if(!t)return;a.A.defaults.headers.common["Authorization"]=`Bearer ${t}`;const o=await a.A.get("/api/auth/me"),{user:r}=o.data;return e("SET_USER",r),e("SET_ROLE",r.role),localStorage.setItem("userRole",r.role),o}catch(t){throw e("LOGOUT"),t}}},modules:{}})}},t={};function o(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r].call(n.exports,n,n.exports,o),n.exports}o.m=e,function(){var e=[];o.O=function(t,r,a,n){if(!r){var l=1/0;for(c=0;c<e.length;c++){r=e[c][0],a=e[c][1],n=e[c][2];for(var s=!0,i=0;i<r.length;i++)(!1&n||l>=n)&&Object.keys(o.O).every(function(e){return o.O[e](r[i])})?r.splice(i--,1):(s=!1,n<l&&(l=n));if(s){e.splice(c--,1);var u=a();void 0!==u&&(t=u)}}return t}n=n||0;for(var c=e.length;c>0&&e[c-1][2]>n;c--)e[c]=e[c-1];e[c]=[r,a,n]}}(),function(){o.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(t,{a:t}),t}}(),function(){o.d=function(e,t){for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}}(),function(){o.f={},o.e=function(e){return Promise.all(Object.keys(o.f).reduce(function(t,r){return o.f[r](e,t),t},[]))}}(),function(){o.u=function(e){return"js/"+e+"."+{75:"9fd0b955",271:"4f68676d",309:"6e698ce7",348:"8800362a",358:"c1e72501",404:"cb454656",555:"03089816",824:"a340c6aa",964:"82ef6469"}[e]+".js"}}(),function(){o.miniCssF=function(e){return"css/"+e+"."+{75:"634141bd",271:"a48391ae",309:"34a24b8f",348:"400aa313",358:"072cc491",404:"9ed596d2",555:"1de23251",824:"c1b248ac",964:"687ff968"}[e]+".css"}}(),function(){o.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="ms:";o.l=function(r,a,n,l){if(e[r])e[r].push(a);else{var s,i;if(void 0!==n)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+n){s=d;break}}s||(i=!0,s=document.createElement("script"),s.charset="utf-8",s.timeout=120,o.nc&&s.setAttribute("nonce",o.nc),s.setAttribute("data-webpack",t+n),s.src=r),e[r]=[a];var m=function(t,o){s.onerror=s.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(function(e){return e(o)}),t)return t(o)},p=setTimeout(m.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=m.bind(null,s.onerror),s.onload=m.bind(null,s.onload),i&&document.head.appendChild(s)}}}(),function(){o.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){o.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,r,a,n){var l=document.createElement("link");l.rel="stylesheet",l.type="text/css",o.nc&&(l.nonce=o.nc);var s=function(o){if(l.onerror=l.onload=null,"load"===o.type)a();else{var r=o&&o.type,s=o&&o.target&&o.target.href||t,i=new Error("Loading CSS chunk "+e+" failed.\n("+r+": "+s+")");i.name="ChunkLoadError",i.code="CSS_CHUNK_LOAD_FAILED",i.type=r,i.request=s,l.parentNode&&l.parentNode.removeChild(l),n(i)}};return l.onerror=l.onload=s,l.href=t,r?r.parentNode.insertBefore(l,r.nextSibling):document.head.appendChild(l),l},t=function(e,t){for(var o=document.getElementsByTagName("link"),r=0;r<o.length;r++){var a=o[r],n=a.getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(n===e||n===t))return a}var l=document.getElementsByTagName("style");for(r=0;r<l.length;r++){a=l[r],n=a.getAttribute("data-href");if(n===e||n===t)return a}},r=function(r){return new Promise(function(a,n){var l=o.miniCssF(r),s=o.p+l;if(t(l,s))return a();e(r,s,null,a,n)})},a={524:0};o.f.miniCss=function(e,t){var o={75:1,271:1,309:1,348:1,358:1,404:1,555:1,824:1,964:1};a[e]?t.push(a[e]):0!==a[e]&&o[e]&&t.push(a[e]=r(e).then(function(){a[e]=0},function(t){throw delete a[e],t}))}}}(),function(){var e={524:0};o.f.j=function(t,r){var a=o.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var n=new Promise(function(o,r){a=e[t]=[o,r]});r.push(a[2]=n);var l=o.p+o.u(t),s=new Error,i=function(r){if(o.o(e,t)&&(a=e[t],0!==a&&(e[t]=void 0),a)){var n=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+n+": "+l+")",s.name="ChunkLoadError",s.type=n,s.request=l,a[1](s)}};o.l(l,i,"chunk-"+t,t)}},o.O.j=function(t){return 0===e[t]};var t=function(t,r){var a,n,l=r[0],s=r[1],i=r[2],u=0;if(l.some(function(t){return 0!==e[t]})){for(a in s)o.o(s,a)&&(o.m[a]=s[a]);if(i)var c=i(o)}for(t&&t(r);u<l.length;u++)n=l[u],o.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return o.O(c)},r=self["webpackChunkms"]=self["webpackChunkms"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var r=o.O(void 0,[504],function(){return o(1927)});r=o.O(r)})();
//# sourceMappingURL=app.ce1d9e18.js.map
{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, Fragment as _Fragment, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives, renderList as _renderList, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"exam-list-container\"\n};\nconst _hoisted_2 = {\n  class: \"filter-container\"\n};\nconst _hoisted_3 = {\n  class: \"card-header\"\n};\nconst _hoisted_4 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_5 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"question-dialog-content\"\n};\nconst _hoisted_7 = {\n  class: \"question-header\"\n};\nconst _hoisted_8 = {\n  class: \"question-actions\"\n};\nconst _hoisted_9 = {\n  class: \"question-table-wrapper\"\n};\nconst _hoisted_10 = [\"innerHTML\"];\nconst _hoisted_11 = {\n  class: \"import-dialog-content\"\n};\nconst _hoisted_12 = {\n  class: \"template-download\"\n};\nconst _hoisted_13 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_14 = {\n  class: \"options-header\"\n};\nconst _hoisted_15 = {\n  class: \"option-item\"\n};\nconst _hoisted_16 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"results-dialog-content\"\n};\nconst _hoisted_18 = {\n  class: \"statistics-items\"\n};\nconst _hoisted_19 = {\n  class: \"statistics-item\"\n};\nconst _hoisted_20 = {\n  class: \"statistics-value\"\n};\nconst _hoisted_21 = {\n  class: \"statistics-item\"\n};\nconst _hoisted_22 = {\n  class: \"statistics-value\"\n};\nconst _hoisted_23 = {\n  class: \"statistics-item\"\n};\nconst _hoisted_24 = {\n  class: \"statistics-value\"\n};\nconst _hoisted_25 = {\n  class: \"statistics-item\"\n};\nconst _hoisted_26 = {\n  class: \"statistics-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"filter-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_form, {\n      model: $setup.filterForm,\n      inline: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"考试标题\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.filterForm.title,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filterForm.title = $event),\n          placeholder: \"请输入考试标题\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSearch\n        }, {\n          default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"搜索\")])),\n          _: 1 /* STABLE */,\n          __: [19]\n        }), _createVNode(_component_el_button, {\n          onClick: $setup.resetFilter\n        }, {\n          default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [20]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])])]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_card, {\n    class: \"table-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"考试列表\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [$setup.canManageExams ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.handleAddExam\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"新增考试\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    })) : _createCommentVNode(\"v-if\", true)])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.examList,\n      stripe: \"\",\n      border: \"\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"index\",\n        width: \"50\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"title\",\n        label: \"考试标题\",\n        \"min-width\": \"200\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"description\",\n        label: \"考试描述\",\n        \"min-width\": \"200\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"duration\",\n        label: \"考试时长(分钟)\",\n        width: \"120\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"pass_score\",\n        label: \"及格分数\",\n        width: \"100\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"total_score\",\n        label: \"总分\",\n        width: \"80\"\n      }), _createCommentVNode(\" Add remaining attempts column for students \"), $setup.isStudent ? (_openBlock(), _createBlock(_component_el_table_column, {\n        key: 0,\n        label: \"考试机会\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.remainingAttempts[scope.row.id] > 0 ? 'success' : 'danger',\n          effect: \"plain\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 已用 \" + _toDisplayString(2 - $setup.remainingAttempts[scope.row.id]) + \"/2 次 \", 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_table_column, {\n        label: \"创建时间\",\n        width: \"160\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.created_at, 'YYYY-MM-DD HH:mm')), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"320\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [$setup.canManageExams ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 0\n        }, [_createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: $event => $setup.handleManageQuestions(scope.row)\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"试题管理\")])),\n          _: 2 /* DYNAMIC */,\n          __: [23]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"success\",\n          size: \"small\",\n          onClick: $event => $setup.handleViewResults(scope.row)\n        }, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"成绩查看\")])),\n          _: 2 /* DYNAMIC */,\n          __: [24]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"warning\",\n          size: \"small\",\n          onClick: $event => $setup.handleEdit(scope.row)\n        }, {\n          default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [25]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.handleDelete(scope.row)\n        }, {\n          default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [26]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          type: \"primary\",\n          size: \"small\",\n          onClick: $event => $setup.handleTakeExam(scope.row),\n          disabled: $setup.remainingAttempts[scope.row.id] === 0,\n          loading: $setup.loading && $setup.currentLoadingExam === scope.row.id\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.remainingAttempts[scope.row.id] === 0 ? '已无机会' : '参加考试'), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"disabled\", \"loading\"]))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_pagination, {\n      background: \"\",\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      \"current-page\": $setup.currentPage,\n      \"page-sizes\": [10, 20, 50, 100],\n      \"page-size\": $setup.pageSize,\n      total: $setup.total,\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 新增/编辑考试对话框 \"), _createVNode(_component_el_dialog, {\n    title: $setup.dialogTitle,\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.dialogVisible = $event),\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_button, {\n      onClick: _cache[6] || (_cache[6] = $event => $setup.dialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitForm\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    })])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.examForm,\n      rules: $setup.examRules,\n      ref: \"examFormRef\",\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"考试标题\",\n        prop: \"title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.examForm.title,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.examForm.title = $event),\n          placeholder: \"请输入考试标题\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"考试描述\",\n        prop: \"description\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          type: \"textarea\",\n          modelValue: $setup.examForm.description,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.examForm.description = $event),\n          placeholder: \"请输入考试描述\",\n          rows: \"3\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_row, {\n        gutter: 20\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 24\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"考试时长\",\n            prop: \"duration\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input_number, {\n              modelValue: $setup.examForm.duration,\n              \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.examForm.duration = $event),\n              min: 1,\n              max: 240,\n              placeholder: \"分钟\",\n              style: {\n                \"width\": \"100%\"\n              }\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_col, {\n          span: 24\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"及格分数\",\n            prop: \"pass_score\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input_number, {\n              modelValue: $setup.examForm.pass_score,\n              \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.examForm.pass_score = $event),\n              min: 1,\n              max: $setup.examForm.total_score,\n              style: {\n                \"width\": \"100%\"\n              }\n            }, null, 8 /* PROPS */, [\"modelValue\", \"max\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_col, {\n          span: 24\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"总分\",\n            prop: \"total_score\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input_number, {\n              modelValue: $setup.examForm.total_score,\n              \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.examForm.total_score = $event),\n              min: 1,\n              max: 1000,\n              style: {\n                \"width\": \"100%\"\n              }\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"modelValue\"]), _createCommentVNode(\" 试题管理对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"试题管理\",\n    modelValue: $setup.questionDialogVisible,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.questionDialogVisible = $event),\n    width: \"850px\",\n    fullscreen: false,\n    \"close-on-click-modal\": false\n  }, {\n    default: _withCtx(() => [$setup.currentExam ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", null, _toDisplayString($setup.currentExam.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleAddQuestion\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"新增试题\")])),\n      _: 1 /* STABLE */,\n      __: [29]\n    }), _createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: $setup.handleImportQuestions\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"批量导入\")])),\n      _: 1 /* STABLE */,\n      __: [30]\n    })])]), _createElementVNode(\"div\", _hoisted_9, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.questionList,\n      stripe: \"\",\n      border: \"\",\n      style: {\n        \"width\": \"100%\",\n        \"margin-top\": \"15px\"\n      },\n      \"max-height\": 550,\n      \"show-header\": true\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"index\",\n        width: \"50\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"题目内容\",\n        \"min-width\": \"300\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"div\", {\n          innerHTML: $setup.formatQuestionContent(scope.row.question)\n        }, null, 8 /* PROPS */, _hoisted_10)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"question_type\",\n        label: \"题型\",\n        width: \"100\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"score\",\n        label: \"分值\",\n        width: \"80\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"150\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          type: \"warning\",\n          size: \"small\",\n          onClick: $event => $setup.handleEditQuestion(scope.row)\n        }, {\n          default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [31]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.handleDeleteQuestion(scope.row)\n        }, {\n          default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [32]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.questionLoading]])]), _createCommentVNode(\" 移除分页，使用滚动条 \")])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 试题导入对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"批量导入试题\",\n    modelValue: $setup.importDialogVisible,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.importDialogVisible = $event),\n    width: \"500px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n      onClick: _cache[9] || (_cache[9] = $event => $setup.importDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[37] || (_cache[37] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [37]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitUpload,\n      loading: $setup.uploadLoading\n    }, {\n      default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\"上传\")])),\n      _: 1 /* STABLE */,\n      __: [38]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_alert, {\n      title: \"请上传Word格式的试题模板文件\",\n      type: \"info\",\n      closable: false,\n      \"show-icon\": \"\",\n      style: {\n        \"margin-bottom\": \"20px\"\n      }\n    }), _createVNode(_component_el_upload, {\n      class: \"upload-demo\",\n      drag: \"\",\n      action: \"#\",\n      \"http-request\": $setup.handleFileUpload,\n      \"before-upload\": $setup.beforeUpload,\n      limit: 1,\n      \"file-list\": $setup.fileList,\n      \"auto-upload\": false,\n      \"on-change\": $setup.handleFileChange,\n      accept: \".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n    }, {\n      tip: _withCtx(() => _cache[33] || (_cache[33] = [_createElementVNode(\"div\", {\n        class: \"el-upload__tip\"\n      }, \" 仅支持 .doc/.docx 格式文件 \", -1 /* CACHED */)])),\n      default: _withCtx(() => [_createVNode(_component_el_icon, {\n        class: \"el-icon--upload\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"UploadFilled\"])]),\n        _: 1 /* STABLE */\n      }), _cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n        class: \"el-upload__text\"\n      }, [_createTextVNode(\" 拖拽文件到此处或 \"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [34]\n    }, 8 /* PROPS */, [\"file-list\"]), _createElementVNode(\"div\", _hoisted_12, [_cache[36] || (_cache[36] = _createElementVNode(\"span\", null, \"没有模板？\", -1 /* CACHED */)), _createVNode(_component_el_button, {\n      type: \"primary\",\n      link: \"\",\n      onClick: $setup.downloadTemplate\n    }, {\n      default: _withCtx(() => _cache[35] || (_cache[35] = [_createTextVNode(\"下载试题模板\")])),\n      _: 1 /* STABLE */,\n      __: [35]\n    })])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 试题编辑对话框 \"), _createVNode(_component_el_dialog, {\n    title: $setup.questionDialogTitle,\n    modelValue: $setup.questionEditDialogVisible,\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $setup.questionEditDialogVisible = $event),\n    width: \"700px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_button, {\n      onClick: _cache[16] || (_cache[16] = $event => $setup.questionEditDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[44] || (_cache[44] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [44]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitQuestionForm\n    }, {\n      default: _withCtx(() => _cache[45] || (_cache[45] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [45]\n    })])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.questionForm,\n      rules: $setup.questionRules,\n      ref: \"questionFormRef\",\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"题目类型\",\n        prop: \"question_type\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.questionForm.question_type,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.questionForm.question_type = $event),\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"单选题\",\n            value: \"单选题\"\n          }), _createVNode(_component_el_option, {\n            label: \"多选题\",\n            value: \"多选题\"\n          }), _createVNode(_component_el_option, {\n            label: \"判断题\",\n            value: \"判断题\"\n          }), _createVNode(_component_el_option, {\n            label: \"简答题\",\n            value: \"简答题\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"分值\",\n        prop: \"score\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.questionForm.score,\n          \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.questionForm.score = $event),\n          min: 1,\n          max: 100,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"题目内容\",\n        prop: \"question\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          type: \"textarea\",\n          modelValue: $setup.questionForm.question,\n          \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.questionForm.question = $event),\n          placeholder: \"请输入题目内容\",\n          rows: \"4\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.questionForm.question_type === '单选题' || $setup.questionForm.question_type === '多选题' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_createElementVNode(\"div\", _hoisted_14, [_cache[40] || (_cache[40] = _createElementVNode(\"h4\", null, \"选项\", -1 /* CACHED */)), _createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: $setup.addOption\n      }, {\n        default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\"添加选项\")])),\n        _: 1 /* STABLE */,\n        __: [39]\n      })]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.questionForm.options, (option, index) => {\n        return _openBlock(), _createBlock(_component_el_form_item, {\n          key: index,\n          label: '选项 ' + String.fromCharCode(65 + index)\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_input, {\n            modelValue: option.text,\n            \"onUpdate:modelValue\": $event => option.text = $event,\n            placeholder: \"请输入选项内容\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createVNode(_component_el_checkbox, {\n            modelValue: option.isCorrect,\n            \"onUpdate:modelValue\": $event => option.isCorrect = $event\n          }, {\n            default: _withCtx(() => [...(_cache[41] || (_cache[41] = [_createTextVNode(\"正确答案\")]))]),\n            _: 2 /* DYNAMIC */,\n            __: [41]\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createVNode(_component_el_button, {\n            type: \"danger\",\n            icon: \"Delete\",\n            circle: \"\",\n            size: \"small\",\n            onClick: $event => $setup.removeOption(index)\n          }, null, 8 /* PROPS */, [\"onClick\"])])]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n      }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */)) : $setup.questionForm.question_type === '判断题' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"正确答案\",\n        prop: \"correct_answer\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n          modelValue: $setup.questionForm.correct_answer,\n          \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $setup.questionForm.correct_answer = $event)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_radio, {\n            label: \"正确\"\n          }, {\n            default: _withCtx(() => _cache[42] || (_cache[42] = [_createTextVNode(\"正确\")])),\n            _: 1 /* STABLE */,\n            __: [42]\n          }), _createVNode(_component_el_radio, {\n            label: \"错误\"\n          }, {\n            default: _withCtx(() => _cache[43] || (_cache[43] = [_createTextVNode(\"错误\")])),\n            _: 1 /* STABLE */,\n            __: [43]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 2,\n        label: \"参考答案\",\n        prop: \"correct_answer\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          type: \"textarea\",\n          modelValue: $setup.questionForm.correct_answer,\n          \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $setup.questionForm.correct_answer = $event),\n          placeholder: \"请输入参考答案\",\n          rows: \"3\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"modelValue\"]), _createCommentVNode(\" 成绩查看对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"考试成绩\",\n    modelValue: $setup.resultsDialogVisible,\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $setup.resultsDialogVisible = $event),\n    width: \"800px\"\n  }, {\n    default: _withCtx(() => [$setup.currentExam ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"h3\", null, _toDisplayString($setup.currentExam.title), 1 /* TEXT */), _createVNode(_component_el_card, {\n      class: \"statistics-card\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_cache[46] || (_cache[46] = _createElementVNode(\"div\", {\n        class: \"statistics-label\"\n      }, \"总人数\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_20, _toDisplayString($setup.examStatistics.total_students || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_21, [_cache[47] || (_cache[47] = _createElementVNode(\"div\", {\n        class: \"statistics-label\"\n      }, \"及格人数\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_22, _toDisplayString($setup.examStatistics.passed_students || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_23, [_cache[48] || (_cache[48] = _createElementVNode(\"div\", {\n        class: \"statistics-label\"\n      }, \"及格率\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_24, _toDisplayString($setup.examStatistics.pass_rate || 0) + \"%\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_25, [_cache[49] || (_cache[49] = _createElementVNode(\"div\", {\n        class: \"statistics-label\"\n      }, \"平均分\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_26, _toDisplayString($setup.examStatistics.average_score || 0), 1 /* TEXT */)])])]),\n      _: 1 /* STABLE */\n    }), _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.resultsList,\n      stripe: \"\",\n      border: \"\",\n      style: {\n        \"width\": \"100%\",\n        \"margin-top\": \"15px\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"index\",\n        width: \"50\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"student_name\",\n        label: \"学生姓名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"score\",\n        label: \"得分\",\n        width: \"100\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"span\", {\n          class: _normalizeClass(scope.row.score >= $setup.currentExam.pass_score ? 'pass-score' : 'fail-score')\n        }, _toDisplayString(scope.row.score), 3 /* TEXT, CLASS */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"考试时间\",\n        width: \"160\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.exam_date, 'YYYY-MM-DD HH:mm')), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"状态\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: scope.row.score >= $setup.currentExam.pass_score ? 'success' : 'danger'\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.score >= $setup.currentExam.pass_score ? '及格' : '不及格'), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"100\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: $event => $setup.handleViewDetail(scope.row)\n        }, {\n          default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\"详情\")])),\n          _: 2 /* DYNAMIC */,\n          __: [50]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.resultsLoading]])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "_createElementVNode", "_hoisted_2", "_component_el_form", "model", "$setup", "filterForm", "inline", "_component_el_form_item", "label", "_component_el_input", "title", "$event", "placeholder", "clearable", "_component_el_button", "type", "onClick", "handleSearch", "_cache", "resetFilter", "header", "_withCtx", "_hoisted_3", "canManageExams", "_createBlock", "handleAddExam", "_component_el_table", "data", "examList", "stripe", "border", "style", "_component_el_table_column", "width", "prop", "_createCommentVNode", "isStudent", "default", "scope", "_component_el_tag", "remainingAttempts", "row", "id", "effect", "_toDisplayString", "formatDate", "created_at", "fixed", "_Fragment", "key", "size", "handleManageQuestions", "handleViewResults", "handleEdit", "handleDelete", "handleTakeExam", "disabled", "loading", "currentLoadingExam", "_hoisted_4", "_component_el_pagination", "background", "layout", "currentPage", "pageSize", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "dialogTitle", "dialogVisible", "footer", "_hoisted_5", "submitForm", "examForm", "rules", "examRules", "ref", "description", "rows", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_input_number", "duration", "min", "max", "pass_score", "total_score", "questionDialogVisible", "fullscreen", "currentExam", "_hoisted_6", "_hoisted_7", "_hoisted_8", "handleAddQuestion", "handleImportQuestions", "_hoisted_9", "questionList", "innerHTML", "formatQuestionContent", "question", "handleEditQuestion", "handleDeleteQuestion", "questionLoading", "importDialogVisible", "_hoisted_13", "submitUpload", "uploadLoading", "_hoisted_11", "_component_el_alert", "closable", "_component_el_upload", "drag", "action", "handleFileUpload", "beforeUpload", "limit", "fileList", "handleFileChange", "accept", "tip", "_component_el_icon", "_hoisted_12", "link", "downloadTemplate", "questionDialogTitle", "questionEditDialogVisible", "_hoisted_16", "submitQuestionForm", "questionForm", "questionRules", "_component_el_select", "question_type", "_component_el_option", "value", "score", "_hoisted_14", "addOption", "_renderList", "options", "option", "index", "String", "fromCharCode", "_hoisted_15", "text", "_component_el_checkbox", "isCorrect", "icon", "circle", "removeOption", "_component_el_radio_group", "correct_answer", "_component_el_radio", "resultsDialogVisible", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "examStatistics", "total_students", "_hoisted_21", "_hoisted_22", "passed_students", "_hoisted_23", "_hoisted_24", "pass_rate", "_hoisted_25", "_hoisted_26", "average_score", "resultsList", "sortable", "_normalizeClass", "exam_date", "handleViewDetail", "resultsLoading"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\views\\exams\\ExamList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"exam-list-container\">\r\n    <el-card class=\"filter-card\">\r\n      <div class=\"filter-container\">\r\n        <el-form :model=\"filterForm\" inline>\r\n          <el-form-item label=\"考试标题\">\r\n            <el-input v-model=\"filterForm.title\" placeholder=\"请输入考试标题\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button @click=\"resetFilter\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n    \r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>考试列表</span>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddExam\" v-if=\"canManageExams\">新增考试</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table :data=\"examList\" stripe border style=\"width: 100%\" v-loading=\"loading\">\r\n        <el-table-column type=\"index\" width=\"50\" />\r\n        <el-table-column prop=\"title\" label=\"考试标题\" min-width=\"200\" />\r\n        <el-table-column prop=\"description\" label=\"考试描述\" min-width=\"200\" show-overflow-tooltip />\r\n        <el-table-column prop=\"duration\" label=\"考试时长(分钟)\" width=\"120\" />\r\n        <el-table-column prop=\"pass_score\" label=\"及格分数\" width=\"100\" />\r\n        <el-table-column prop=\"total_score\" label=\"总分\" width=\"80\" />\r\n        <!-- Add remaining attempts column for students -->\r\n        <el-table-column label=\"考试机会\" width=\"120\" v-if=\"isStudent\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"remainingAttempts[scope.row.id] > 0 ? 'success' : 'danger'\" effect=\"plain\">\r\n              已用 {{ 2 - (remainingAttempts[scope.row.id]) }}/2 次\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n        \r\n            {{ formatDate(scope.row.created_at, 'YYYY-MM-DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"320\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n        \r\n            <template v-if=\"canManageExams\">\r\n           \r\n              <el-button type=\"primary\" size=\"small\" @click=\"handleManageQuestions(scope.row)\">试题管理</el-button>\r\n              <el-button type=\"success\" size=\"small\" @click=\"handleViewResults(scope.row)\">成绩查看</el-button>\r\n              <el-button type=\"warning\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"small\" \r\n                @click=\"handleTakeExam(scope.row)\" \r\n                :disabled=\"remainingAttempts[scope.row.id] === 0\"\r\n                :loading=\"loading && currentLoadingExam === scope.row.id\"\r\n              >\r\n                {{ remainingAttempts[scope.row.id] === 0 ? '已无机会' : '参加考试' }}\r\n              </el-button>\r\n            \r\n            </template>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/编辑考试对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form :model=\"examForm\" :rules=\"examRules\" ref=\"examFormRef\" label-width=\"100px\">\r\n        <el-form-item label=\"考试标题\" prop=\"title\">\r\n          <el-input v-model=\"examForm.title\" placeholder=\"请输入考试标题\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"考试描述\" prop=\"description\">\r\n          <el-input type=\"textarea\" v-model=\"examForm.description\" placeholder=\"请输入考试描述\" rows=\"3\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"考试时长\" prop=\"duration\">\r\n          \r\n              <el-input-number v-model=\"examForm.duration\" :min=\"1\" :max=\"240\" placeholder=\"分钟\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"及格分数\" prop=\"pass_score\">\r\n              <el-input-number v-model=\"examForm.pass_score\" :min=\"1\" :max=\"examForm.total_score\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"总分\" prop=\"total_score\">\r\n              <el-input-number v-model=\"examForm.total_score\" :min=\"1\" :max=\"1000\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题管理对话框 -->\r\n    <el-dialog\r\n      title=\"试题管理\"\r\n      v-model=\"questionDialogVisible\"\r\n      width=\"850px\"\r\n      :fullscreen=\"false\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div v-if=\"currentExam\" class=\"question-dialog-content\">\r\n        <div class=\"question-header\">\r\n          <h3>{{ currentExam.title }}</h3>\r\n          <div class=\"question-actions\">\r\n            <el-button type=\"primary\" @click=\"handleAddQuestion\">新增试题</el-button>\r\n            <el-button type=\"success\" @click=\"handleImportQuestions\">批量导入</el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"question-table-wrapper\">\r\n          <el-table \r\n            :data=\"questionList\" \r\n            stripe \r\n            border \r\n            style=\"width: 100%; margin-top: 15px;\" \r\n            v-loading=\"questionLoading\"\r\n            :max-height=\"550\"\r\n            :show-header=\"true\"\r\n          >\r\n            <el-table-column type=\"index\" width=\"50\" />\r\n            <el-table-column label=\"题目内容\" min-width=\"300\">\r\n              <template #default=\"scope\">\r\n                <div v-html=\"formatQuestionContent(scope.row.question)\"></div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"question_type\" label=\"题型\" width=\"100\" />\r\n            <el-table-column prop=\"score\" label=\"分值\" width=\"80\" />\r\n            <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"warning\" size=\"small\" @click=\"handleEditQuestion(scope.row)\">编辑</el-button>\r\n                <el-button type=\"danger\" size=\"small\" @click=\"handleDeleteQuestion(scope.row)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        \r\n        <!-- 移除分页，使用滚动条 -->\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题导入对话框 -->\r\n    <el-dialog\r\n      title=\"批量导入试题\"\r\n      v-model=\"importDialogVisible\"\r\n      width=\"500px\"\r\n    >\r\n      <div class=\"import-dialog-content\">\r\n        <el-alert\r\n          title=\"请上传Word格式的试题模板文件\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-bottom: 20px;\"\r\n        />\r\n        \r\n        <el-upload\r\n          class=\"upload-demo\"\r\n          drag\r\n          action=\"#\"\r\n          :http-request=\"handleFileUpload\"\r\n          :before-upload=\"beforeUpload\"\r\n          :limit=\"1\"\r\n          :file-list=\"fileList\"\r\n          :auto-upload=\"false\"\r\n          :on-change=\"handleFileChange\"\r\n          accept=\".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\r\n        >\r\n          <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n          <div class=\"el-upload__text\">\r\n            拖拽文件到此处或 <em>点击上传</em>\r\n          </div>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              仅支持 .doc/.docx 格式文件\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n        \r\n        <div class=\"template-download\">\r\n          <span>没有模板？</span>\r\n          <el-button type=\"primary\" link @click=\"downloadTemplate\">下载试题模板</el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploadLoading\">上传</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"questionDialogTitle\"\r\n      v-model=\"questionEditDialogVisible\"\r\n      width=\"700px\"\r\n    >\r\n      <el-form :model=\"questionForm\" :rules=\"questionRules\" ref=\"questionFormRef\" label-width=\"100px\">\r\n        <el-form-item label=\"题目类型\" prop=\"question_type\">\r\n          <el-select v-model=\"questionForm.question_type\" style=\"width: 100%\">\r\n            <el-option label=\"单选题\" value=\"单选题\"></el-option>\r\n            <el-option label=\"多选题\" value=\"多选题\"></el-option>\r\n            <el-option label=\"判断题\" value=\"判断题\"></el-option>\r\n            <el-option label=\"简答题\" value=\"简答题\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"分值\" prop=\"score\">\r\n          <el-input-number v-model=\"questionForm.score\" :min=\"1\" :max=\"100\" style=\"width: 100%\"></el-input-number>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"题目内容\" prop=\"question\">\r\n          <el-input type=\"textarea\" v-model=\"questionForm.question\" placeholder=\"请输入题目内容\" rows=\"4\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <template v-if=\"questionForm.question_type === '单选题' || questionForm.question_type === '多选题'\">\r\n          <div class=\"options-header\">\r\n            <h4>选项</h4>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addOption\">添加选项</el-button>\r\n          </div>\r\n          \r\n          <el-form-item \r\n            v-for=\"(option, index) in questionForm.options\"\r\n            :key=\"index\"\r\n            :label=\"'选项 ' + String.fromCharCode(65 + index)\"\r\n          >\r\n            <div class=\"option-item\">\r\n              <el-input v-model=\"option.text\" placeholder=\"请输入选项内容\"></el-input>\r\n              <el-checkbox v-model=\"option.isCorrect\">正确答案</el-checkbox>\r\n              <el-button type=\"danger\" icon=\"Delete\" circle size=\"small\" @click=\"removeOption(index)\"></el-button>\r\n            </div>\r\n          </el-form-item>\r\n        </template>\r\n        \r\n        <template v-else-if=\"questionForm.question_type === '判断题'\">\r\n          <el-form-item label=\"正确答案\" prop=\"correct_answer\">\r\n            <el-radio-group v-model=\"questionForm.correct_answer\">\r\n              <el-radio label=\"正确\">正确</el-radio>\r\n              <el-radio label=\"错误\">错误</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </template>\r\n        \r\n        <template v-else>\r\n          <el-form-item label=\"参考答案\" prop=\"correct_answer\">\r\n            <el-input type=\"textarea\" v-model=\"questionForm.correct_answer\" placeholder=\"请输入参考答案\" rows=\"3\"></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      \r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"questionEditDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitQuestionForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 成绩查看对话框 -->\r\n    <el-dialog\r\n      title=\"考试成绩\"\r\n      v-model=\"resultsDialogVisible\"\r\n      width=\"800px\"\r\n    >\r\n      <div v-if=\"currentExam\" class=\"results-dialog-content\">\r\n        <h3>{{ currentExam.title }}</h3>\r\n        \r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-items\">\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">总人数</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.total_students || 0 }}</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">及格人数</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.passed_students || 0 }}</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">及格率</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.pass_rate || 0 }}%</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">平均分</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.average_score || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n        \r\n        <el-table :data=\"resultsList\" stripe border style=\"width: 100%; margin-top: 15px;\" v-loading=\"resultsLoading\">\r\n          <el-table-column type=\"index\" width=\"50\" />\r\n          <el-table-column prop=\"student_name\" label=\"学生姓名\" />\r\n          <el-table-column prop=\"score\" label=\"得分\" width=\"100\" sortable>\r\n            <template #default=\"scope\">\r\n              <span :class=\"scope.row.score >= currentExam.pass_score ? 'pass-score' : 'fail-score'\">\r\n                {{ scope.row.score }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"考试时间\" width=\"160\">\r\n            <template #default=\"scope\">\r\n              {{ formatDate(scope.row.exam_date, 'YYYY-MM-DD HH:mm') }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"scope.row.score >= currentExam.pass_score ? 'success' : 'danger'\">\r\n                {{ scope.row.score >= currentExam.pass_score ? '及格' : '不及格' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"100\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"handleViewDetail(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { UploadFilled } from '@element-plus/icons-vue'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter } from 'vue-router'\r\nimport examService from '@/services/examService'\r\nimport { formatDate, formatRelativeTime } from '@/utils/dateFormat'\r\n\r\nconst store = useStore()\r\nconst router = useRouter()\r\nconst loading = ref(true)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst dialogVisible = ref(false)\r\nconst dialogTitle = ref('新增考试')\r\nconst examFormRef = ref(null)\r\nconst currentLoadingExam = ref(null) // 当前正在加载的考试ID\r\nlet userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : {}\r\n// 用户角色\r\nconst userRole = ref('')\r\nconst isAdmin = ref(userInfo?.role == 'admin')\r\nconst isTeacher = ref(userInfo?.role == 'teacher')\r\nconst isStudent = ref(userInfo?.role == 'student')\r\nconsole.log(isStudent, isTeacher, '---123')\r\nconst canManageExams = ref(userInfo?.role !== 'student')\r\n\r\n// 监听用户角色变化\r\nwatch(() => store.state.user, (newUser) => {\r\n  console.log('用户信息变更:', newUser)\r\n  checkUserRole()\r\n}, { deep: true })\r\n\r\n// 检查用户角色\r\nconst checkUserRole = () => {\r\n  // 使用正确的localStorage键名获取角色\r\n  userRole.value = localStorage.getItem('userRole') || ''\r\n  console.log('当前用户角色:', userRole.value)\r\n  console.log('管理员权限:', isAdmin.value)\r\n  console.log('教师权限:', isTeacher.value)\r\n  console.log('学生权限:', isStudent.value)\r\n  console.log('可管理考试:', canManageExams.value)\r\n}\r\n\r\n// 试题管理相关\r\nconst questionDialogVisible = ref(false)\r\nconst questionEditDialogVisible = ref(false)\r\nconst questionDialogTitle = ref('新增试题')\r\nconst questionFormRef = ref(null)\r\nconst currentExam = ref(null)\r\nconst questionList = ref([])\r\nconst questionLoading = ref(false)\r\nconst questionCurrentPage = ref(1)\r\nconst questionTotal = ref(0)\r\n\r\n// 导入试题相关\r\nconst importDialogVisible = ref(false)\r\nconst fileList = ref([])\r\nconst uploadLoading = ref(false)\r\n\r\n// 成绩查看相关\r\nconst resultsDialogVisible = ref(false)\r\nconst resultsList = ref([])\r\nconst resultsLoading = ref(false)\r\nconst examStatistics = reactive({\r\n  total_students: 0,\r\n  passed_students: 0,\r\n  pass_rate: 0,\r\n  average_score: 0\r\n})\r\n\r\n// Add remaining attempts tracking\r\nconst remainingAttempts = ref({});\r\nconst attemptedExams = ref({}); // 记录已参加过的考试\r\n\r\n// 检查学生是否参加过某个考试\r\nconst hasAttemptedExam = (examId) => {\r\n  if (!examId) return false;\r\n  return attemptedExams.value[examId] === true;\r\n};\r\n\r\n// 过滤条件\r\nconst filterForm = reactive({\r\n  title: ''\r\n})\r\n\r\n// 考试表单\r\nconst examForm = reactive({\r\n  id: null,\r\n  title: '',\r\n  description: '',\r\n  duration: 60,\r\n  pass_score: 60,\r\n  total_score: 100\r\n})\r\n\r\n// 验证规则\r\nconst examRules = {\r\n  title: [\r\n    { required: true, message: '请输入考试标题', trigger: 'blur' }\r\n  ],\r\n  duration: [\r\n    { required: true, message: '请输入考试时长', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '考试时长必须大于0', trigger: 'blur' }\r\n  ],\r\n  pass_score: [\r\n    { required: true, message: '请输入及格分数', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '及格分数必须大于0', trigger: 'blur' }\r\n  ],\r\n  total_score: [\r\n    { required: true, message: '请输入总分', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '总分必须大于0', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 试题表单\r\nconst questionForm = reactive({\r\n  id: null,\r\n  question: '',\r\n  question_type: '单选题',\r\n  correct_answer: '',\r\n  score: 5,\r\n  options: [\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false }\r\n  ]\r\n})\r\n\r\n// 试题验证规则\r\nconst questionRules = {\r\n  question: [\r\n    { required: true, message: '请输入题目内容', trigger: 'blur' }\r\n  ],\r\n  question_type: [\r\n    { required: true, message: '请选择题目类型', trigger: 'change' }\r\n  ],\r\n  score: [\r\n    { required: true, message: '请输入分值', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '分值必须大于0', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 模拟考试数据\r\nconst examList = ref([])\r\n\r\n// 模拟试题数据\r\nconst mockQuestions = [\r\n  {\r\n    id: 1,\r\n    question: '公司的上班时间是几点到几点？',\r\n    options: [\r\n      { text: 'A. 8:30-17:30', isCorrect: true },\r\n      { text: 'B. 9:00-18:00', isCorrect: false },\r\n      { text: 'C. 9:30-18:30', isCorrect: false },\r\n      { text: 'D. 8:00-17:00', isCorrect: false }\r\n    ],\r\n    correct_answer: 'A',\r\n    question_type: '单选题',\r\n    score: 5,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 2,\r\n    question: '公司允许员工在工作时间做以下哪些事情？（多选）',\r\n    options: [\r\n      { text: 'A. 喝水', isCorrect: true },\r\n      { text: 'B. 短暂休息', isCorrect: true },\r\n      { text: 'C. 玩游戏', isCorrect: false },\r\n      { text: 'D. 睡觉', isCorrect: false }\r\n    ],\r\n    correct_answer: 'AB',\r\n    question_type: '多选题',\r\n    score: 10,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 3,\r\n    question: '公司规定必须遵守考勤制度。',\r\n    options: [],\r\n    correct_answer: '正确',\r\n    question_type: '判断题',\r\n    score: 5,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 4,\r\n    question: '请简述公司的请假流程。',\r\n    options: [],\r\n    correct_answer: '1. 提前向部门负责人口头说明\\n2. 在OA系统填写请假申请\\n3. 等待审批通过\\n4. 销假时提交相关证明',\r\n    question_type: '简答题',\r\n    score: 15,\r\n    exam_id: 1\r\n  }\r\n]\r\n\r\n// 模拟成绩数据\r\nconst mockResults = [\r\n  {\r\n    id: 1,\r\n    student_id: 1,\r\n    student_name: '张三',\r\n    exam_id: 1,\r\n    score: 85,\r\n    exam_date: '2023-01-20 10:30:00'\r\n  },\r\n  {\r\n    id: 2,\r\n    student_id: 2,\r\n    student_name: '李四',\r\n    exam_id: 1,\r\n    score: 75,\r\n    exam_date: '2023-01-20 10:45:00'\r\n  },\r\n  {\r\n    id: 3,\r\n    student_id: 3,\r\n    student_name: '王五',\r\n    exam_id: 1,\r\n    score: 55,\r\n    exam_date: '2023-01-20 11:00:00'\r\n  },\r\n  {\r\n    id: 4,\r\n    student_id: 4,\r\n    student_name: '赵六',\r\n    exam_id: 1,\r\n    score: 92,\r\n    exam_date: '2023-01-20 10:15:00'\r\n  }\r\n]\r\n\r\n// 设置总数\r\ntotal.value = examList.length\r\n\r\n// 获取所有考试\r\nconst fetchExams = async () => {\r\n  loading.value = true;\r\n  \r\n  // 重新检查用户角色和权限\r\n  checkUserRole();\r\n  \r\n  // 确保vuex中的状态是最新的\r\n  if (localStorage.getItem('userRole') && store.state.role !== localStorage.getItem('userRole')) {\r\n    store.commit('SET_ROLE', localStorage.getItem('userRole'));\r\n  }\r\n  \r\n  try {\r\n    const response = await examService.getExams({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      title: filterForm.title\r\n    });\r\n    \r\n    examList.value = response.data.data;\r\n    total.value = response.data.total || examList.value.length;\r\n    \r\n    // If student, check attempts for each exam\r\n    if (isStudent.value) {\r\n      await checkRemainingAttempts();\r\n    }\r\n    \r\n    loading.value = false;\r\n  } catch (error) {\r\n    console.error('获取考试列表失败', error);\r\n    ElMessage.error('获取考试列表失败');\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// Check remaining attempts for each exam\r\nconst checkRemainingAttempts = async () => {\r\n \r\n  \r\n  \r\n  try {\r\n    console.log('正在获取所有考试的尝试次数...学生ID:', studentId);\r\n    // 创建一个Promise数组，同时请求所有考试的尝试次数\r\n    const attemptPromises = examList.value.map(async (exam) => {\r\n      try {\r\n        console.log(`正在查询考试 ${exam.id} (${exam.title}) 的尝试记录...`);\r\n        const response = await examService.getExamResults(exam.id, { student_id: studentId });\r\n        console.log(`考试 ${exam.id} 返回数据:`, response.data);\r\n        \r\n        // 确保我们有结果数据\r\n        if (response.data && response.data.data) {\r\n          const results = response.data.data.results || [];\r\n          const attempts = results.length;\r\n          const remaining = Math.max(0, 2 - attempts);\r\n          \r\n          console.log(`考试 ${exam.id} (${exam.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\r\n          remainingAttempts.value[exam.id] = remaining;\r\n          attemptedExams.value[exam.id] = attempts > 0; // 只有实际参加过才标记为true\r\n          \r\n          // 记录每次尝试的详情，便于调试\r\n          if (results.length > 0) {\r\n            results.forEach((result, index) => {\r\n              console.log(`  尝试 ${index + 1}: 得分 ${result.score}，时间 ${result.exam_date}`);\r\n            });\r\n          }\r\n        } else {\r\n          console.warn(`考试 ${exam.id} 未返回有效数据，设置默认剩余次数为2`);\r\n          remainingAttempts.value[exam.id] = 2;\r\n          attemptedExams.value[exam.id] = false; // 没有数据，标记为未参加\r\n        }\r\n      } catch (err) {\r\n        console.error(`获取考试 ${exam.id} 尝试次数失败:`, err);\r\n        remainingAttempts.value[exam.id] = 2;\r\n        attemptedExams.value[exam.id] = false; // 出错，标记为未参加\r\n      }\r\n    });\r\n    \r\n    // 等待所有请求完成\r\n    await Promise.all(attemptPromises);\r\n    console.log('所有考试尝试次数获取完成:', remainingAttempts.value);\r\n    console.log('已参加过的考试:', attemptedExams.value);\r\n  } catch (error) {\r\n    console.error('获取考试尝试次数失败:', error);\r\n    // 设置默认值\r\n    examList.value.forEach(exam => {\r\n      remainingAttempts.value[exam.id] = 2;\r\n      attemptedExams.value[exam.id] = false; // 出错，标记为未参加\r\n    });\r\n  }\r\n};\r\n\r\n// 参加考试\r\nconst handleTakeExam = (row) => {\r\n  // 设置当前正在加载的考试ID\r\n  currentLoadingExam.value = row.id;\r\n  \r\n  // 确保已经加载了尝试次数\r\n  if (!remainingAttempts.value[row.id] && remainingAttempts.value[row.id] !== 0) {\r\n    // 如果还没加载尝试次数信息，先加载\r\n    const studentId = userInfo.student_id;\r\n    if (!studentId) {\r\n      ElMessage.warning('未找到有效的学生ID，请重新登录');\r\n      currentLoadingExam.value = null;\r\n      return;\r\n    }\r\n    \r\n    ElMessage.info('正在获取考试尝试信息，请稍候...');\r\n    \r\n    // 立即获取该考试的尝试次数\r\n    examService.getExamResults(row.id, { student_id: studentId })\r\n      .then(response => {\r\n        console.log(`考试 ${row.id} 尝试信息:`, response.data);\r\n        \r\n        if (response.data && response.data.data) {\r\n          const results = response.data.data.results || [];\r\n          const attempts = results.length;\r\n          const remaining = Math.max(0, 2 - attempts);\r\n          \r\n          console.log(`考试 ${row.id} (${row.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\r\n          remainingAttempts.value[row.id] = remaining;\r\n          attemptedExams.value[row.id] = attempts > 0;\r\n          \r\n          // 获取到尝试次数后，继续参加考试逻辑\r\n          currentLoadingExam.value = null;\r\n          continueToTakeExam(row);\r\n        } else {\r\n          console.warn(`考试 ${row.id} 未返回有效数据，设置默认剩余次数为2`);\r\n          remainingAttempts.value[row.id] = 2;\r\n          attemptedExams.value[row.id] = false;\r\n          currentLoadingExam.value = null;\r\n          continueToTakeExam(row);\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('获取考试尝试次数失败', error);\r\n        ElMessage.error('获取考试尝试次数失败，请刷新页面重试');\r\n        currentLoadingExam.value = null;\r\n      });\r\n    return;\r\n  }\r\n  \r\n  currentLoadingExam.value = null;\r\n  continueToTakeExam(row);\r\n};\r\n\r\n// 继续参加考试流程\r\nconst continueToTakeExam = (row) => {\r\n  // Check if student still has attempts left\r\n  if (remainingAttempts.value[row.id] === 0) {\r\n    ElMessage.warning('您已达到该考试的最大尝试次数（2次）');\r\n    return;\r\n  }\r\n  \r\n  // Confirm before taking the exam\r\n  ElMessageBox.confirm(\r\n    `您总共有2次尝试机会，已使用 ${2 - remainingAttempts.value[row.id]} 次，还剩 ${remainingAttempts.value[row.id]} 次机会。确定要参加考试吗？`,\r\n    '参加考试',\r\n    {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }\r\n  ).then(() => {\r\n    // 记录考试ID和剩余次数到localStorage，以便考试页面使用\r\n    localStorage.setItem('currentExamId', row.id);\r\n    localStorage.setItem('currentExamTitle', row.title);\r\n    localStorage.setItem('currentExamRemainingAttempts', remainingAttempts.value[row.id]);\r\n    \r\n    // 跳转到考试页面\r\n    router.push(`/exams/take/${row.id}`);\r\n  }).catch(() => {\r\n    console.log('用户取消参加考试');\r\n  });\r\n};\r\n\r\n// 查看我的成绩\r\nconst handleViewMyResult = async (row) => {\r\n  try {\r\n    const studentId = localStorage.getItem('studentId');\r\n    if (!studentId) {\r\n      ElMessage.error('未找到有效的学生ID，请重新登录');\r\n      return;\r\n    }\r\n    \r\n    console.log('查询成绩 - 学生ID:', studentId, '考试ID:', row.id);\r\n    \r\n    const response = await examService.getExamResults(row.id, { student_id: studentId });\r\n    console.log('查询成绩 - 返回数据:', response.data);\r\n    \r\n    // 检查是否有考试记录\r\n    if (response.data.data.results && response.data.data.results.length > 0) {\r\n      // Get all attempts sorted by score (highest first)\r\n      const myResults = response.data.data.results\r\n        .filter(r => r.student_id === parseInt(studentId))\r\n        .sort((a, b) => b.score - a.score);\r\n      \r\n      console.log('查询成绩 - 过滤后的结果:', myResults);\r\n      \r\n      if (myResults.length > 0) {\r\n        // Format results to show all attempts\r\n        const attemptsList = myResults.map((result, index) => {\r\n          return `<div class=\"result-item ${result.score >= row.pass_score ? 'pass' : 'fail'}\">\r\n            <h4>尝试 ${index + 1}</h4>\r\n            <p><strong>得分:</strong> ${result.score} / ${row.total_score}</p>\r\n            <p><strong>考试时间:</strong> ${formatDate(result.exam_date, 'YYYY-MM-DD HH:mm:ss')}</p>\r\n            <p><strong>状态:</strong> ${result.score >= row.pass_score ? '通过' : '未通过'}</p>\r\n          </div>`;\r\n        }).join('<hr>');\r\n        \r\n        ElMessageBox.alert(\r\n          `<div class=\"results-container\">\r\n            <h3>您的考试成绩</h3>\r\n            <div class=\"results-list\">${attemptsList}</div>\r\n            <div class=\"attempts-info\">\r\n              <p>总尝试次数: ${myResults.length}/2</p>\r\n              <p>剩余次数: ${Math.max(0, 2 - myResults.length)}</p>\r\n              <p class=\"view-all-results\"><a href=\"#/exams/my-results\">查看我的所有考试成绩 &raquo;</a></p>\r\n            </div>\r\n          </div>`,\r\n          '考试成绩',\r\n          {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定',\r\n            customClass: 'result-dialog'\r\n          }\r\n        );\r\n        return;\r\n      }\r\n    }\r\n    \r\n    ElMessage.info('您暂无该考试的成绩记录');\r\n  } catch (error) {\r\n    console.error('获取成绩失败', error);\r\n    ElMessage.error('获取成绩失败: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nonMounted(() => {\r\n  // 确保先检查用户角色并强制刷新store状态\r\n  reloadUserRoleState();\r\n  fetchExams();\r\n})\r\n\r\n// 强制重新加载用户角色状态\r\nconst reloadUserRoleState = () => {\r\n  // 从localStorage获取最新角色\r\n  const currentUserRole = localStorage.getItem('userRole');\r\n  \r\n  // 确保store中的角色是最新的\r\n  if (currentUserRole) {\r\n    console.log('从localStorage加载角色:', currentUserRole);\r\n    store.commit('SET_ROLE', currentUserRole);\r\n    \r\n    // 如果有用户ID，确保用户信息也是最新的\r\n    const userId = localStorage.getItem('userId');\r\n    if (userId) {\r\n      // 可以选择是否强制刷新用户信息\r\n      store.dispatch('fetchUserProfile').catch(err => {\r\n        console.error('获取用户信息失败:', err);\r\n      });\r\n    }\r\n  } else {\r\n    console.warn('未找到用户角色信息');\r\n  }\r\n  \r\n  // 执行常规角色检查\r\n  checkUserRole();\r\n}\r\n\r\n// 重置过滤条件\r\nconst resetFilter = () => {\r\n  filterForm.title = ''\r\n  handleSearch()\r\n}\r\n\r\n// 搜索\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchExams()\r\n}\r\n\r\n// 新增考试\r\nconst handleAddExam = () => {\r\n  dialogTitle.value = '新增考试'\r\n  dialogVisible.value = true\r\n  // 重置表单\r\n  examForm.id = null\r\n  examForm.title = ''\r\n  examForm.description = ''\r\n  examForm.duration = 60\r\n  examForm.pass_score = 60\r\n  examForm.total_score = 100\r\n}\r\n\r\n// 编辑考试\r\nconst handleEdit = (row) => {\r\n  dialogTitle.value = '编辑考试'\r\n  dialogVisible.value = true\r\n  \r\n  // 查询完整的考试信息\r\n  examService.getExam(row.id)\r\n    .then(response => {\r\n      // 填充表单数据\r\n      const examData = response.data.data;\r\n      Object.keys(examForm).forEach(key => {\r\n        // 确保数值型字段为数字类型\r\n        if (['duration', 'pass_score', 'total_score'].includes(key)) {\r\n          examForm[key] = Number(examData[key] || 0);\r\n        } else {\r\n          examForm[key] = examData[key];\r\n        }\r\n      });\r\n    })\r\n    .catch(error => {\r\n      console.error('获取考试详情失败', error);\r\n      ElMessage.error('获取考试详情失败');\r\n      \r\n      // 如果获取详情失败，仍然使用表格行数据填充\r\n      Object.keys(examForm).forEach(key => {\r\n        examForm[key] = row[key];\r\n      });\r\n    });\r\n}\r\n\r\n// 删除考试\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除考试 ${row.title} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await examService.deleteExam(row.id)\r\n      ElMessage.success(`考试 ${row.title} 已删除`)\r\n      fetchExams() // 重新加载列表\r\n    } catch (error) {\r\n      console.error('删除考试失败', error)\r\n      ElMessage.error('删除考试失败，请重试')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!examFormRef.value) return\r\n  \r\n  await examFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (examForm.id) {\r\n          // 编辑模式\r\n          await examService.updateExam(examForm.id, examForm)\r\n          ElMessage.success(`考试 ${examForm.title} 信息已更新`)\r\n        } else {\r\n          // 新增模式\r\n          await examService.createExam(examForm)\r\n          ElMessage.success(`考试 ${examForm.title} 添加成功`)\r\n        }\r\n        dialogVisible.value = false\r\n        fetchExams() // 重新加载列表\r\n      } catch (error) {\r\n        console.error('保存考试失败', error)\r\n        ElMessage.error('保存考试失败，请重试')\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 试题管理\r\nconst handleManageQuestions = (row) => {\r\n  currentExam.value = row\r\n  questionDialogVisible.value = true\r\n  // 设置为第一页（即使我们不再使用分页）\r\n  questionCurrentPage.value = 1\r\n  \r\n  // 加载试题数据\r\n  loadQuestions(row.id)\r\n  \r\n  // 确保对话框完全打开后调整表格高度\r\n  setTimeout(() => {\r\n    const questionTable = document.querySelector('.question-table-wrapper .el-table')\r\n    if (questionTable) {\r\n      questionTable.style.height = '550px'\r\n    }\r\n  }, 100)\r\n}\r\n\r\n// 加载试题数据\r\nconst loadQuestions = async (examId) => {\r\n  questionLoading.value = true\r\n  \r\n  try {\r\n    // 不使用分页参数，一次性获取所有试题\r\n    const response = await examService.getExamQuestions(examId)\r\n    \r\n    // 检查返回数据结构\r\n    console.log('加载试题数据返回:', response.data)\r\n    \r\n    // 确保获取完整的数据列表\r\n    if (response.data && response.data.data) {\r\n      questionList.value = response.data.data\r\n      // 依然保存总数，用于展示\r\n      questionTotal.value = response.data.count || questionList.value.length\r\n      console.log(`加载了 ${questionList.value.length} 道试题`)\r\n    } else {\r\n      questionList.value = []\r\n      questionTotal.value = 0\r\n      console.warn('未找到试题数据')\r\n    }\r\n    \r\n    questionLoading.value = false\r\n  } catch (error) {\r\n    console.error('获取试题失败', error)\r\n    ElMessage.error('获取试题失败')\r\n    questionLoading.value = false\r\n  }\r\n}\r\n\r\n// 新增试题\r\nconst handleAddQuestion = () => {\r\n  questionDialogTitle.value = '新增试题'\r\n  questionEditDialogVisible.value = true\r\n  // 重置表单\r\n  questionForm.id = null\r\n  questionForm.question = ''\r\n  questionForm.question_type = '单选题'\r\n  questionForm.correct_answer = ''\r\n  questionForm.score = 5\r\n  questionForm.options = [\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false }\r\n  ]\r\n}\r\n\r\n// 编辑试题\r\nconst handleEditQuestion = (row) => {\r\n  questionDialogTitle.value = '编辑试题'\r\n  questionEditDialogVisible.value = true\r\n  \r\n  // 填充表单数据\r\n  questionForm.id = row.id\r\n  questionForm.question = row.question\r\n  questionForm.question_type = row.question_type\r\n  questionForm.correct_answer = row.correct_answer\r\n  questionForm.score = row.score\r\n  \r\n  // 根据题型处理选项\r\n  if (row.question_type === '单选题' || row.question_type === '多选题') {\r\n    questionForm.options = row.options ? [...row.options] : []\r\n  } else if (row.question_type === '判断题') {\r\n    questionForm.correct_answer = row.correct_answer\r\n  }\r\n}\r\n\r\n// 删除试题\r\nconst handleDeleteQuestion = (row) => {\r\n  ElMessageBox.confirm(`确定要删除该试题吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await examService.deleteQuestion(row.id)\r\n      ElMessage.success('试题已删除')\r\n      // 重新加载试题列表\r\n      loadQuestions(currentExam.value.id)\r\n    } catch (error) {\r\n      console.error('删除试题失败', error)\r\n      ElMessage.error('删除试题失败，请重试')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量导入试题\r\nconst handleImportQuestions = () => {\r\n  importDialogVisible.value = true\r\n  fileList.value = []\r\n}\r\n\r\n// 文件上传前检查\r\nconst beforeUpload = (file) => {\r\n  const isWordDoc = \r\n    file.type === 'application/msword' || \r\n    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\r\n  \r\n  if (!isWordDoc) {\r\n    ElMessage.error('请上传Word格式的文件!')\r\n    return false\r\n  }\r\n  \r\n  const isLt10M = file.size / 1024 / 1024 < 10\r\n  \r\n  if (!isLt10M) {\r\n    ElMessage.error('文件大小不能超过10MB!')\r\n    return false\r\n  }\r\n  \r\n  return true\r\n}\r\n\r\n// 处理文件选择\r\nconst handleFileChange = (file, uploadFileList) => {\r\n  // 记录最新选择的文件\r\n  console.log('文件选择变化:', file, uploadFileList)\r\n  fileList.value = uploadFileList\r\n}\r\n\r\n// 处理文件上传\r\nconst handleFileUpload = (options) => {\r\n  const { file } = options\r\n  // 这个函数只在auto-upload=true时调用\r\n  // 我们设置为false，所以这里只需返回false\r\n  return false // 阻止默认上传行为，使用我们自己的提交方式\r\n}\r\n\r\n// 提交上传\r\nconst submitUpload = async () => {\r\n  console.log('当前文件列表:', fileList.value)\r\n  if (!fileList.value || fileList.value.length === 0) {\r\n    ElMessage.warning('请选择要上传的文件')\r\n    return\r\n  }\r\n  \r\n  uploadLoading.value = true\r\n  \r\n  try {\r\n    // 创建FormData对象\r\n    const formData = new FormData()\r\n    // 获取原始文件对象\r\n    const fileObject = fileList.value[0]\r\n    const rawFile = fileObject.raw || fileObject\r\n    console.log('上传文件:', rawFile)\r\n    \r\n    // 使用field name 'file' 匹配后端控制器\r\n    formData.append('template', rawFile)\r\n    \r\n    // 调用实际API\r\n    const response = await examService.importQuestionsFromWord(currentExam.value.id, formData)\r\n    \r\n    // 处理成功响应\r\n    ElMessage.success(`试题导入成功，共导入${response.data.count || 0}道题目`)\r\n    importDialogVisible.value = false\r\n    \r\n    // 重新加载试题列表\r\n    loadQuestions(currentExam.value.id)\r\n  } catch (error) {\r\n    console.error('导入试题失败', error)\r\n    let errorMsg = '导入试题失败'\r\n    \r\n    // 获取详细错误信息\r\n    if (error.response && error.response.data && error.response.data.message) {\r\n      errorMsg += `：${error.response.data.message}`\r\n    }\r\n    \r\n    ElMessage.error(errorMsg)\r\n  } finally {\r\n    uploadLoading.value = false\r\n  }\r\n}\r\n\r\n// 下载模板\r\nconst downloadTemplate = async () => {\r\n  try {\r\n    ElMessage.success('模板下载中...')\r\n    \r\n    // 创建下载链接\r\n    const link = document.createElement('a')\r\n    // 设置下载链接为后端API地址\r\n    link.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/exams/template/download`\r\n    \r\n    // 添加token到URL，以便通过授权\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      link.href += `?token=${token}`\r\n    }\r\n    \r\n    link.download = '试题导入模板.docx'\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    document.body.removeChild(link)\r\n  } catch (error) {\r\n    console.error('下载模板失败', error)\r\n    ElMessage.error('下载模板失败，请重试')\r\n  }\r\n}\r\n\r\n// 添加选项\r\nconst addOption = () => {\r\n  questionForm.options.push({ text: '', isCorrect: false })\r\n}\r\n\r\n// 移除选项\r\nconst removeOption = (index) => {\r\n  if (questionForm.options.length <= 2) {\r\n    ElMessage.warning('至少需要2个选项')\r\n    return\r\n  }\r\n  questionForm.options.splice(index, 1)\r\n}\r\n\r\n// 提交试题表单\r\nconst submitQuestionForm = async () => {\r\n  if (!questionFormRef.value) return\r\n  \r\n  await questionFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      // 选择题验证选项\r\n      if ((questionForm.question_type === '单选题' || questionForm.question_type === '多选题')) {\r\n        // 验证选项内容\r\n        const emptyOption = questionForm.options.find(opt => !opt.text.trim())\r\n        if (emptyOption) {\r\n          ElMessage.error('选项内容不能为空')\r\n          return\r\n        }\r\n        \r\n        // 验证是否选择了正确答案\r\n        const hasCorrect = questionForm.options.some(opt => opt.isCorrect)\r\n        if (!hasCorrect) {\r\n          ElMessage.error('请至少选择一个正确答案')\r\n          return\r\n        }\r\n        \r\n        // 单选题只能有一个正确答案\r\n        if (questionForm.question_type === '单选题') {\r\n          const correctCount = questionForm.options.filter(opt => opt.isCorrect).length\r\n          if (correctCount > 1) {\r\n            ElMessage.error('单选题只能有一个正确答案')\r\n            return\r\n          }\r\n        }\r\n      }\r\n      \r\n      try {\r\n        // 准备提交的数据\r\n        const submitData = { ...questionForm }\r\n        \r\n        // 处理正确答案\r\n        if (questionForm.question_type === '单选题') {\r\n          const correctOption = questionForm.options.find(opt => opt.isCorrect)\r\n          if (correctOption) {\r\n            // 获取正确选项的索引，转换为A、B、C...\r\n            const index = questionForm.options.findIndex(opt => opt.isCorrect)\r\n            submitData.correct_answer = String.fromCharCode(65 + index) // A, B, C...\r\n          }\r\n        } else if (questionForm.question_type === '多选题') {\r\n          // 将所有正确选项索引转换为字符串，如\"ABC\"\r\n          const correctAnswers = questionForm.options\r\n            .map((opt, index) => opt.isCorrect ? String.fromCharCode(65 + index) : null)\r\n            .filter(Boolean)\r\n            .join('')\r\n          submitData.correct_answer = correctAnswers\r\n        }\r\n        \r\n        // 提交表单\r\n        if (questionForm.id) {\r\n          // 编辑模式\r\n          await examService.updateQuestion(questionForm.id, submitData)\r\n          ElMessage.success('试题更新成功')\r\n        } else {\r\n          // 新增模式\r\n          await examService.createQuestion(currentExam.value.id, submitData)\r\n          ElMessage.success('试题添加成功')\r\n        }\r\n        \r\n        questionEditDialogVisible.value = false\r\n        // 重新加载试题列表\r\n        loadQuestions(currentExam.value.id)\r\n      } catch (error) {\r\n        console.error('保存试题失败', error)\r\n        ElMessage.error('保存试题失败，请重试')\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 格式化试题内容\r\nconst formatQuestionContent = (content) => {\r\n  if (!content) return ''\r\n  // 最多显示100个字符\r\n  return content.length > 100 ? content.substring(0, 100) + '...' : content\r\n}\r\n\r\n// 试题不再使用分页\r\n\r\n// 查看考试成绩\r\nconst handleViewResults = async (row) => {\r\n  currentExam.value = row\r\n  resultsDialogVisible.value = true\r\n  resultsLoading.value = true\r\n  \r\n  try {\r\n    const response = await examService.getExamResults(row.id)\r\n    const data = response.data.data\r\n    \r\n    resultsList.value = data.results || []\r\n    \r\n    // 设置统计数据\r\n    examStatistics.total_students = data.summary.total_students || 0\r\n    examStatistics.passed_students = data.summary.passed_students || 0\r\n    examStatistics.pass_rate = data.summary.pass_rate || 0\r\n    examStatistics.average_score = data.summary.average_score || 0\r\n    \r\n    resultsLoading.value = false\r\n  } catch (error) {\r\n    console.error('获取成绩列表失败', error)\r\n    ElMessage.error('获取成绩列表失败')\r\n    resultsLoading.value = false\r\n  }\r\n}\r\n\r\n// 查看考试详情\r\nconst handleViewDetail = (row) => {\r\n  ElMessage.success(`查看 ${row.student_name} 的考试详情`)\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  fetchExams()\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchExams()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 试题管理样式 */\r\n.question-dialog-content {\r\n  min-height: 300px;\r\n}\r\n\r\n.question-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 添加表格滚动容器样式 */\r\n.question-table-wrapper {\r\n  max-height: 600px; /* 增加高度以显示更多内容 */\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.question-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.options-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.options-header h4 {\r\n  margin: 0;\r\n}\r\n\r\n.option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* 试题导入样式 */\r\n.import-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n.upload-demo {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.template-download {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  gap: 5px;\r\n}\r\n\r\n/* 成绩查看样式 */\r\n.results-dialog-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 15px;\r\n  font-size: 18px;\r\n}\r\n\r\n.statistics-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.statistics-items {\r\n  display: flex;\r\n  justify-content: space-around;\r\n}\r\n\r\n.statistics-item {\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.statistics-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.pass-score {\r\n  color: #67C23A;\r\n  font-weight: bold;\r\n}\r\n\r\n.fail-score {\r\n  color: #F56C6C;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n}\r\n\r\n:deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n}\r\n\r\n/* Exam results styles */\r\n:deep(.result-dialog) {\r\n  min-width: 400px;\r\n}\r\n\r\n:deep(.results-container) {\r\n  padding: 10px;\r\n}\r\n\r\n:deep(.result-item) {\r\n  padding: 10px;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.result-item.pass) {\r\n  background-color: rgba(103, 194, 58, 0.1);\r\n  border-left: 3px solid #67C23A;\r\n}\r\n\r\n:deep(.result-item.fail) {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border-left: 3px solid #F56C6C;\r\n}\r\n\r\n:deep(.results-list) {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n:deep(.attempts-info) {\r\n  font-weight: bold;\r\n  border-top: 1px solid #eee;\r\n  padding-top: 10px;\r\n}\r\n\r\n:deep(.view-all-results) {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n}\r\n\r\n:deep(.view-all-results a) {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.view-all-results a:hover) {\r\n  text-decoration: underline;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAkB;;EAetBA,KAAK,EAAC;AAAa;;EAuDrBA,KAAK,EAAC;AAAsB;;EAiD1BA,KAAK,EAAC;AAAe;;;EAeJA,KAAK,EAAC;;;EACvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAkB;;EAM1BA,KAAK,EAAC;AAAwB;;;EAqChCA,KAAK,EAAC;AAAuB;;EAgC3BA,KAAK,EAAC;AAAmB;;EAOzBA,KAAK,EAAC;AAAe;;EAgCnBA,KAAK,EAAC;AAAgB;;EAUpBA,KAAK,EAAC;AAAa;;EAyBvBA,KAAK,EAAC;AAAe;;;EAaJA,KAAK,EAAC;;;EAIrBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAkB;;EAE1BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAkB;;EAE1BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAkB;;EAE1BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;;;;;;;;;uBAhUzCC,mBAAA,CAmWM,OAnWNC,UAmWM,GAlWJC,YAAA,CAYUC,kBAAA;IAZDJ,KAAK,EAAC;EAAa;sBAC1B,MAUM,CAVNK,mBAAA,CAUM,OAVNC,UAUM,GATJH,YAAA,CAQUI,kBAAA;MARAC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAEC,MAAM,EAAN;;wBAC3B,MAEe,CAFfR,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC;MAAM;0BACxB,MAAgF,CAAhFV,YAAA,CAAgFW,mBAAA;sBAA7DL,MAAA,CAAAC,UAAU,CAACK,KAAK;qEAAhBN,MAAA,CAAAC,UAAU,CAACK,KAAK,GAAAC,MAAA;UAAEC,WAAW,EAAC,SAAS;UAACC,SAAS,EAAT;;;UAE7Df,YAAA,CAGeS,uBAAA;0BAFb,MAA8D,CAA9DT,YAAA,CAA8DgB,oBAAA;UAAnDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEZ,MAAA,CAAAa;;4BAAc,MAAEC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;YAClDpB,YAAA,CAA8CgB,oBAAA;UAAlCE,OAAK,EAAEZ,MAAA,CAAAe;QAAW;4BAAE,MAAED,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;;;MAM1CpB,YAAA,CAqEUC,kBAAA;IArEDJ,KAAK,EAAC;EAAY;IACdyB,MAAM,EAAAC,QAAA,CACf,MAKM,CALNrB,mBAAA,CAKM,OALNsB,UAKM,G,4BAJJtB,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAEM,cADmDI,MAAA,CAAAmB,cAAc,I,cAArEC,YAAA,CAAuFV,oBAAA;;MAA5EC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAqB;;wBAAqC,MAAIP,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;sBAKjF,MA6CW,C,+BA7CXM,YAAA,CA6CWE,mBAAA;MA7CAC,IAAI,EAAEvB,MAAA,CAAAwB,QAAQ;MAAEC,MAAM,EAAN,EAAM;MAACC,MAAM,EAAN,EAAM;MAACC,KAAmB,EAAnB;QAAA;MAAA;;wBACvC,MAA2C,CAA3CjC,YAAA,CAA2CkC,0BAAA;QAA1BjB,IAAI,EAAC,OAAO;QAACkB,KAAK,EAAC;UACpCnC,YAAA,CAA6DkC,0BAAA;QAA5CE,IAAI,EAAC,OAAO;QAAC1B,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;UACrDV,YAAA,CAAyFkC,0BAAA;QAAxEE,IAAI,EAAC,aAAa;QAAC1B,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;UACjEV,YAAA,CAAgEkC,0BAAA;QAA/CE,IAAI,EAAC,UAAU;QAAC1B,KAAK,EAAC,UAAU;QAACyB,KAAK,EAAC;UACxDnC,YAAA,CAA8DkC,0BAAA;QAA7CE,IAAI,EAAC,YAAY;QAAC1B,KAAK,EAAC,MAAM;QAACyB,KAAK,EAAC;UACtDnC,YAAA,CAA4DkC,0BAAA;QAA3CE,IAAI,EAAC,aAAa;QAAC1B,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC;UACrDE,mBAAA,gDAAmD,EACH/B,MAAA,CAAAgC,SAAS,I,cAAzDZ,YAAA,CAMkBQ,0BAAA;;QANDxB,KAAK,EAAC,MAAM;QAACyB,KAAK,EAAC;;QACvBI,OAAO,EAAAhB,QAAA,CAGPiB,KAHc,KACvBxC,YAAA,CAESyC,iBAAA;UAFAxB,IAAI,EAAEX,MAAA,CAAAoC,iBAAiB,CAACF,KAAK,CAACG,GAAG,CAACC,EAAE;UAA8BC,MAAM,EAAC;;4BAAQ,MACrF,C,iBADqF,MACrF,GAAAC,gBAAA,KAAQxC,MAAA,CAAAoC,iBAAiB,CAACF,KAAK,CAACG,GAAG,CAACC,EAAE,KAAK,OAChD,gB;;;;+CAGJ5C,YAAA,CAKkBkC,0BAAA;QALDxB,KAAK,EAAC,MAAM;QAACyB,KAAK,EAAC;;QACvBI,OAAO,EAAAhB,QAAA,CAE0CiB,KAFnC,K,kCAEpBlC,MAAA,CAAAyC,UAAU,CAACP,KAAK,CAACG,GAAG,CAACK,UAAU,sC;;UAGtChD,YAAA,CAuBkBkC,0BAAA;QAvBDxB,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC,KAAK;QAACc,KAAK,EAAC;;QACjCV,OAAO,EAAAhB,QAAA,CAkBxBiB,KAlB+B,KAEPlC,MAAA,CAAAmB,cAAc,I,cAA9B3B,mBAAA,CAMWoD,SAAA;UAAAC,GAAA;QAAA,IAJTnD,YAAA,CAAiGgB,oBAAA;UAAtFC,IAAI,EAAC,SAAS;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAA+C,qBAAqB,CAACb,KAAK,CAACG,GAAG;;4BAAG,MAAIvB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;0DACrFpB,YAAA,CAA6FgB,oBAAA;UAAlFC,IAAI,EAAC,SAAS;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAgD,iBAAiB,CAACd,KAAK,CAACG,GAAG;;4BAAG,MAAIvB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;0DACjFpB,YAAA,CAAoFgB,oBAAA;UAAzEC,IAAI,EAAC,SAAS;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAiD,UAAU,CAACf,KAAK,CAACG,GAAG;;4BAAG,MAAEvB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DACxEpB,YAAA,CAAqFgB,oBAAA;UAA1EC,IAAI,EAAC,QAAQ;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAkD,YAAY,CAAChB,KAAK,CAACG,GAAG;;4BAAG,MAAEvB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;uGAGzEM,YAAA,CAQYV,oBAAA;;UAPVC,IAAI,EAAC,SAAS;UACdmC,IAAI,EAAC,OAAO;UACXlC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAmD,cAAc,CAACjB,KAAK,CAACG,GAAG;UAC/Be,QAAQ,EAAEpD,MAAA,CAAAoC,iBAAiB,CAACF,KAAK,CAACG,GAAG,CAACC,EAAE;UACxCe,OAAO,EAAErD,MAAA,CAAAqD,OAAO,IAAIrD,MAAA,CAAAsD,kBAAkB,KAAKpB,KAAK,CAACG,GAAG,CAACC;;4BAEtD,MAA6D,C,kCAA1DtC,MAAA,CAAAoC,iBAAiB,CAACF,KAAK,CAACG,GAAG,CAACC,EAAE,0C;;;;;;wDAvC6BtC,MAAA,CAAAqD,OAAO,E,GA+C/EzD,mBAAA,CAWM,OAXN2D,UAWM,GAVJ7D,YAAA,CASE8D,wBAAA;MARAC,UAAU,EAAV,EAAU;MACVC,MAAM,EAAC,yCAAyC;MAC/C,cAAY,EAAE1D,MAAA,CAAA2D,WAAW;MACzB,YAAU,EAAE,iBAAiB;MAC7B,WAAS,EAAE3D,MAAA,CAAA4D,QAAQ;MACnBC,KAAK,EAAE7D,MAAA,CAAA6D,KAAK;MACZC,YAAW,EAAE9D,MAAA,CAAA+D,gBAAgB;MAC7BC,eAAc,EAAEhE,MAAA,CAAAiE;;;MAKvBlC,mBAAA,gBAAmB,EACnBrC,YAAA,CAuCYwE,oBAAA;IAtCT5D,KAAK,EAAEN,MAAA,CAAAmE,WAAW;gBACVnE,MAAA,CAAAoE,aAAa;+DAAbpE,MAAA,CAAAoE,aAAa,GAAA7D,MAAA;IACtBsB,KAAK,EAAC;;IA8BKwC,MAAM,EAAApD,QAAA,CACf,MAGM,CAHNrB,mBAAA,CAGM,OAHN0E,UAGM,GAFJ5E,YAAA,CAAwDgB,oBAAA;MAA5CE,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAAP,MAAA,IAAEP,MAAA,CAAAoE,aAAa;;wBAAU,MAAEtD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CpB,YAAA,CAA4DgB,oBAAA;MAAjDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAuE;;wBAAY,MAAEzD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBA/BpD,MA2BU,CA3BVpB,YAAA,CA2BUI,kBAAA;MA3BAC,KAAK,EAAEC,MAAA,CAAAwE,QAAQ;MAAGC,KAAK,EAAEzE,MAAA,CAAA0E,SAAS;MAAEC,GAAG,EAAC,aAAa;MAAC,aAAW,EAAC;;wBAC1E,MAEe,CAFfjF,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;;0BAC9B,MAAoE,CAApEpC,YAAA,CAAoEW,mBAAA;sBAAjDL,MAAA,CAAAwE,QAAQ,CAAClE,KAAK;qEAAdN,MAAA,CAAAwE,QAAQ,CAAClE,KAAK,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAGjDd,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;;0BAC9B,MAAmG,CAAnGpC,YAAA,CAAmGW,mBAAA;UAAzFM,IAAI,EAAC,UAAU;sBAAUX,MAAA,CAAAwE,QAAQ,CAACI,WAAW;qEAApB5E,MAAA,CAAAwE,QAAQ,CAACI,WAAW,GAAArE,MAAA;UAAEC,WAAW,EAAC,SAAS;UAACqE,IAAI,EAAC;;;UAGtFnF,YAAA,CAiBSoF,iBAAA;QAjBAC,MAAM,EAAE;MAAE;0BACjB,MAKS,CALTrF,YAAA,CAKSsF,iBAAA;UALAC,IAAI,EAAE;QAAE;4BACf,MAGe,CAHfvF,YAAA,CAGeS,uBAAA;YAHDC,KAAK,EAAC,MAAM;YAAC0B,IAAI,EAAC;;8BAE9B,MAAwH,CAAxHpC,YAAA,CAAwHwF,0BAAA;0BAA9FlF,MAAA,CAAAwE,QAAQ,CAACW,QAAQ;yEAAjBnF,MAAA,CAAAwE,QAAQ,CAACW,QAAQ,GAAA5E,MAAA;cAAG6E,GAAG,EAAE,CAAC;cAAGC,GAAG,EAAE,GAAG;cAAE7E,WAAW,EAAC,IAAI;cAACmB,KAAmB,EAAnB;gBAAA;cAAA;;;;;YAGtFjC,YAAA,CAISsF,iBAAA;UAJAC,IAAI,EAAE;QAAE;4BACf,MAEe,CAFfvF,YAAA,CAEeS,uBAAA;YAFDC,KAAK,EAAC,MAAM;YAAC0B,IAAI,EAAC;;8BAC9B,MAA0H,CAA1HpC,YAAA,CAA0HwF,0BAAA;0BAAhGlF,MAAA,CAAAwE,QAAQ,CAACc,UAAU;yEAAnBtF,MAAA,CAAAwE,QAAQ,CAACc,UAAU,GAAA/E,MAAA;cAAG6E,GAAG,EAAE,CAAC;cAAGC,GAAG,EAAErF,MAAA,CAAAwE,QAAQ,CAACe,WAAW;cAAE5D,KAAmB,EAAnB;gBAAA;cAAA;;;;;YAGxFjC,YAAA,CAISsF,iBAAA;UAJAC,IAAI,EAAE;QAAE;4BACf,MAEe,CAFfvF,YAAA,CAEeS,uBAAA;YAFDC,KAAK,EAAC,IAAI;YAAC0B,IAAI,EAAC;;8BAC5B,MAA2G,CAA3GpC,YAAA,CAA2GwF,0BAAA;0BAAjFlF,MAAA,CAAAwE,QAAQ,CAACe,WAAW;yEAApBvF,MAAA,CAAAwE,QAAQ,CAACe,WAAW,GAAAhF,MAAA;cAAG6E,GAAG,EAAE,CAAC;cAAGC,GAAG,EAAE,IAAI;cAAE1D,KAAmB,EAAnB;gBAAA;cAAA;;;;;;;;;;;8CAa/EI,mBAAA,aAAgB,EAChBrC,YAAA,CA6CYwE,oBAAA;IA5CV5D,KAAK,EAAC,MAAM;gBACHN,MAAA,CAAAwF,qBAAqB;+DAArBxF,MAAA,CAAAwF,qBAAqB,GAAAjF,MAAA;IAC9BsB,KAAK,EAAC,OAAO;IACZ4D,UAAU,EAAE,KAAK;IACjB,sBAAoB,EAAE;;sBAAgC,MAuC1D,CArCczF,MAAA,CAAA0F,WAAW,I,cAAtBlG,mBAAA,CAqCM,OArCNmG,UAqCM,GApCJ/F,mBAAA,CAMM,OANNgG,UAMM,GALJhG,mBAAA,CAAgC,YAAA4C,gBAAA,CAAzBxC,MAAA,CAAA0F,WAAW,CAACpF,KAAK,kBACxBV,mBAAA,CAGM,OAHNiG,UAGM,GAFJnG,YAAA,CAAqEgB,oBAAA;MAA1DC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAA8F;;wBAAmB,MAAIhF,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;QACzDpB,YAAA,CAAyEgB,oBAAA;MAA9DC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAA+F;;wBAAuB,MAAIjF,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;YAIjElB,mBAAA,CAyBM,OAzBNoG,UAyBM,G,+BAxBJ5E,YAAA,CAuBWE,mBAAA;MAtBRC,IAAI,EAAEvB,MAAA,CAAAiG,YAAY;MACnBxE,MAAM,EAAN,EAAM;MACNC,MAAM,EAAN,EAAM;MACNC,KAAsC,EAAtC;QAAA;QAAA;MAAA,CAAsC;MAErC,YAAU,EAAE,GAAG;MACf,aAAW,EAAE;;wBAEd,MAA2C,CAA3CjC,YAAA,CAA2CkC,0BAAA;QAA1BjB,IAAI,EAAC,OAAO;QAACkB,KAAK,EAAC;UACpCnC,YAAA,CAIkBkC,0BAAA;QAJDxB,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3B6B,OAAO,EAAAhB,QAAA,CAC8CiB,KADvC,KACvBtC,mBAAA,CAA8D;UAAzDsG,SAAkD,EAA1ClG,MAAA,CAAAmG,qBAAqB,CAACjE,KAAK,CAACG,GAAG,CAAC+D,QAAQ;;;UAGzD1G,YAAA,CAA+DkC,0BAAA;QAA9CE,IAAI,EAAC,eAAe;QAAC1B,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC;UACvDnC,YAAA,CAAsDkC,0BAAA;QAArCE,IAAI,EAAC,OAAO;QAAC1B,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC;UAC/CnC,YAAA,CAKkBkC,0BAAA;QALDxB,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC,KAAK;QAACc,KAAK,EAAC;;QACjCV,OAAO,EAAAhB,QAAA,CAC4EiB,KADrE,KACvBxC,YAAA,CAA4FgB,oBAAA;UAAjFC,IAAI,EAAC,SAAS;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAqG,kBAAkB,CAACnE,KAAK,CAACG,GAAG;;4BAAG,MAAEvB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DAChFpB,YAAA,CAA6FgB,oBAAA;UAAlFC,IAAI,EAAC,QAAQ;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAsG,oBAAoB,CAACpE,KAAK,CAACG,GAAG;;4BAAG,MAAEvB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDAf1Ed,MAAA,CAAAuG,eAAe,E,KAqB9BxE,mBAAA,gBAAmB,C;;qCAIvBA,mBAAA,aAAgB,EAChBrC,YAAA,CAiDYwE,oBAAA;IAhDV5D,KAAK,EAAC,QAAQ;gBACLN,MAAA,CAAAwG,mBAAmB;iEAAnBxG,MAAA,CAAAwG,mBAAmB,GAAAjG,MAAA;IAC5BsB,KAAK,EAAC;;IAwCKwC,MAAM,EAAApD,QAAA,CACf,MAGM,CAHNrB,mBAAA,CAGM,OAHN6G,WAGM,GAFJ/G,YAAA,CAA8DgB,oBAAA;MAAlDE,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAAP,MAAA,IAAEP,MAAA,CAAAwG,mBAAmB;;wBAAU,MAAE1F,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAClDpB,YAAA,CAAuFgB,oBAAA;MAA5EC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAA0G,YAAY;MAAGrD,OAAO,EAAErD,MAAA,CAAA2G;;wBAAe,MAAE7F,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAzC/E,MAoCM,CApCNlB,mBAAA,CAoCM,OApCNgH,WAoCM,GAnCJlH,YAAA,CAMEmH,mBAAA;MALAvG,KAAK,EAAC,kBAAkB;MACxBK,IAAI,EAAC,MAAM;MACVmG,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT,EAAS;MACTnF,KAA4B,EAA5B;QAAA;MAAA;QAGFjC,YAAA,CAqBYqH,oBAAA;MApBVxH,KAAK,EAAC,aAAa;MACnByH,IAAI,EAAJ,EAAI;MACJC,MAAM,EAAC,GAAG;MACT,cAAY,EAAEjH,MAAA,CAAAkH,gBAAgB;MAC9B,eAAa,EAAElH,MAAA,CAAAmH,YAAY;MAC3BC,KAAK,EAAE,CAAC;MACR,WAAS,EAAEpH,MAAA,CAAAqH,QAAQ;MACnB,aAAW,EAAE,KAAK;MAClB,WAAS,EAAErH,MAAA,CAAAsH,gBAAgB;MAC5BC,MAAM,EAAC;;MAMIC,GAAG,EAAAvG,QAAA,CACZ,MAEMH,MAAA,SAAAA,MAAA,QAFNlB,mBAAA,CAEM;QAFDL,KAAK,EAAC;MAAgB,GAAC,uBAE5B,mB;wBAPF,MAA4D,CAA5DG,YAAA,CAA4D+H,kBAAA;QAAnDlI,KAAK,EAAC;MAAiB;0BAAC,MAAiB,CAAjBG,YAAA,CAAiBM,MAAA,kB;;sCAClDJ,mBAAA,CAEM;QAFDL,KAAK,EAAC;MAAiB,I,iBAAC,YAClB,GAAAK,mBAAA,CAAa,YAAT,MAAI,E;;;sCASrBA,mBAAA,CAGM,OAHN8H,WAGM,G,4BAFJ9H,mBAAA,CAAkB,cAAZ,OAAK,qBACXF,YAAA,CAA2EgB,oBAAA;MAAhEC,IAAI,EAAC,SAAS;MAACgH,IAAI,EAAJ,EAAI;MAAE/G,OAAK,EAAEZ,MAAA,CAAA4H;;wBAAkB,MAAM9G,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;;;qCAYrEiB,mBAAA,aAAgB,EAChBrC,YAAA,CAgEYwE,oBAAA;IA/DT5D,KAAK,EAAEN,MAAA,CAAA6H,mBAAmB;gBAClB7H,MAAA,CAAA8H,yBAAyB;iEAAzB9H,MAAA,CAAA8H,yBAAyB,GAAAvH,MAAA;IAClCsB,KAAK,EAAC;;IAuDKwC,MAAM,EAAApD,QAAA,CACf,MAGM,CAHNrB,mBAAA,CAGM,OAHNmI,WAGM,GAFJrI,YAAA,CAAoEgB,oBAAA;MAAxDE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAP,MAAA,IAAEP,MAAA,CAAA8H,yBAAyB;;wBAAU,MAAEhH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACxDpB,YAAA,CAAoEgB,oBAAA;MAAzDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAgI;;wBAAoB,MAAElH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAxD5D,MAmDU,CAnDVpB,YAAA,CAmDUI,kBAAA;MAnDAC,KAAK,EAAEC,MAAA,CAAAiI,YAAY;MAAGxD,KAAK,EAAEzE,MAAA,CAAAkI,aAAa;MAAEvD,GAAG,EAAC,iBAAiB;MAAC,aAAW,EAAC;;wBACtF,MAOe,CAPfjF,YAAA,CAOeS,uBAAA;QAPDC,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;;0BAC9B,MAKY,CALZpC,YAAA,CAKYyI,oBAAA;sBALQnI,MAAA,CAAAiI,YAAY,CAACG,aAAa;uEAA1BpI,MAAA,CAAAiI,YAAY,CAACG,aAAa,GAAA7H,MAAA;UAAEoB,KAAmB,EAAnB;YAAA;UAAA;;4BAC9C,MAA+C,CAA/CjC,YAAA,CAA+C2I,oBAAA;YAApCjI,KAAK,EAAC,KAAK;YAACkI,KAAK,EAAC;cAC7B5I,YAAA,CAA+C2I,oBAAA;YAApCjI,KAAK,EAAC,KAAK;YAACkI,KAAK,EAAC;cAC7B5I,YAAA,CAA+C2I,oBAAA;YAApCjI,KAAK,EAAC,KAAK;YAACkI,KAAK,EAAC;cAC7B5I,YAAA,CAA+C2I,oBAAA;YAApCjI,KAAK,EAAC,KAAK;YAACkI,KAAK,EAAC;;;;;UAIjC5I,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAAC0B,IAAI,EAAC;;0BAC5B,MAAwG,CAAxGpC,YAAA,CAAwGwF,0BAAA;sBAA9ElF,MAAA,CAAAiI,YAAY,CAACM,KAAK;uEAAlBvI,MAAA,CAAAiI,YAAY,CAACM,KAAK,GAAAhI,MAAA;UAAG6E,GAAG,EAAE,CAAC;UAAGC,GAAG,EAAE,GAAG;UAAE1D,KAAmB,EAAnB;YAAA;UAAA;;;UAGpEjC,YAAA,CAEeS,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;;0BAC9B,MAAoG,CAApGpC,YAAA,CAAoGW,mBAAA;UAA1FM,IAAI,EAAC,UAAU;sBAAUX,MAAA,CAAAiI,YAAY,CAAC7B,QAAQ;uEAArBpG,MAAA,CAAAiI,YAAY,CAAC7B,QAAQ,GAAA7F,MAAA;UAAEC,WAAW,EAAC,SAAS;UAACqE,IAAI,EAAC;;;UAGvE7E,MAAA,CAAAiI,YAAY,CAACG,aAAa,cAAcpI,MAAA,CAAAiI,YAAY,CAACG,aAAa,c,cAAlF5I,mBAAA,CAiBWoD,SAAA;QAAAC,GAAA;MAAA,IAhBTjD,mBAAA,CAGM,OAHN4I,WAGM,G,4BAFJ5I,mBAAA,CAAW,YAAP,IAAE,qBACNF,YAAA,CAA0EgB,oBAAA;QAA/DC,IAAI,EAAC,SAAS;QAACmC,IAAI,EAAC,OAAO;QAAElC,OAAK,EAAEZ,MAAA,CAAAyI;;0BAAW,MAAI3H,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;+BAGhEtB,mBAAA,CAUeoD,SAAA,QAAA8F,WAAA,CATa1I,MAAA,CAAAiI,YAAY,CAACU,OAAO,GAAtCC,MAAM,EAAEC,KAAK;6BADvBzH,YAAA,CAUejB,uBAAA;UARZ0C,GAAG,EAAEgG,KAAK;UACVzI,KAAK,UAAU0I,MAAM,CAACC,YAAY,MAAMF,KAAK;;4BAE9C,MAIM,CAJNjJ,mBAAA,CAIM,OAJNoJ,WAIM,GAHJtJ,YAAA,CAAiEW,mBAAA;wBAA9CuI,MAAM,CAACK,IAAI;6CAAXL,MAAM,CAACK,IAAI,GAAA1I,MAAA;YAAEC,WAAW,EAAC;0EAC5Cd,YAAA,CAA0DwJ,sBAAA;wBAApCN,MAAM,CAACO,SAAS;6CAAhBP,MAAM,CAACO,SAAS,GAAA5I;;8BAAE,MAAI,KAAAO,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;sFAC5CpB,YAAA,CAAoGgB,oBAAA;YAAzFC,IAAI,EAAC,QAAQ;YAACyI,IAAI,EAAC,QAAQ;YAACC,MAAM,EAAN,EAAM;YAACvG,IAAI,EAAC,OAAO;YAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAAsJ,YAAY,CAACT,KAAK;;;;oEAKtE7I,MAAA,CAAAiI,YAAY,CAACG,aAAa,c,cAC7ChH,YAAA,CAKejB,uBAAA;;QALDC,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;;0BAC9B,MAGiB,CAHjBpC,YAAA,CAGiB6J,yBAAA;sBAHQvJ,MAAA,CAAAiI,YAAY,CAACuB,cAAc;uEAA3BxJ,MAAA,CAAAiI,YAAY,CAACuB,cAAc,GAAAjJ,MAAA;;4BAClD,MAAkC,CAAlCb,YAAA,CAAkC+J,mBAAA;YAAxBrJ,KAAK,EAAC;UAAI;8BAAC,MAAEU,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;cACvBpB,YAAA,CAAkC+J,mBAAA;YAAxBrJ,KAAK,EAAC;UAAI;8BAAC,MAAEU,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;2BAM3BM,YAAA,CAEejB,uBAAA;;QAFDC,KAAK,EAAC,MAAM;QAAC0B,IAAI,EAAC;;0BAC9B,MAA0G,CAA1GpC,YAAA,CAA0GW,mBAAA;UAAhGM,IAAI,EAAC,UAAU;sBAAUX,MAAA,CAAAiI,YAAY,CAACuB,cAAc;uEAA3BxJ,MAAA,CAAAiI,YAAY,CAACuB,cAAc,GAAAjJ,MAAA;UAAEC,WAAW,EAAC,SAAS;UAACqE,IAAI,EAAC;;;;;;;8CAanG9C,mBAAA,aAAgB,EAChBrC,YAAA,CA0DYwE,oBAAA;IAzDV5D,KAAK,EAAC,MAAM;gBACHN,MAAA,CAAA0J,oBAAoB;iEAApB1J,MAAA,CAAA0J,oBAAoB,GAAAnJ,MAAA;IAC7BsB,KAAK,EAAC;;sBAmB6B,MAyF9B,CA1GM7B,MAAA,CAAA0F,WAAW,I,cAAtBlG,mBAAA,CAoDM,OApDNmK,WAoDM,GAnDJ/J,mBAAA,CAAgC,YAAA4C,gBAAA,CAAzBxC,MAAA,CAAA0F,WAAW,CAACpF,KAAK,kBAExBZ,YAAA,CAmBUC,kBAAA;MAnBDJ,KAAK,EAAC;IAAiB;wBAC9B,MAiBM,CAjBNK,mBAAA,CAiBM,OAjBNgK,WAiBM,GAhBJhK,mBAAA,CAGM,OAHNiK,WAGM,G,4BAFJjK,mBAAA,CAAuC;QAAlCL,KAAK,EAAC;MAAkB,GAAC,KAAG,qBACjCK,mBAAA,CAA4E,OAA5EkK,WAA4E,EAAAtH,gBAAA,CAA3CxC,MAAA,CAAA+J,cAAc,CAACC,cAAc,sB,GAEhEpK,mBAAA,CAGM,OAHNqK,WAGM,G,4BAFJrK,mBAAA,CAAwC;QAAnCL,KAAK,EAAC;MAAkB,GAAC,MAAI,qBAClCK,mBAAA,CAA6E,OAA7EsK,WAA6E,EAAA1H,gBAAA,CAA5CxC,MAAA,CAAA+J,cAAc,CAACI,eAAe,sB,GAEjEvK,mBAAA,CAGM,OAHNwK,WAGM,G,4BAFJxK,mBAAA,CAAuC;QAAlCL,KAAK,EAAC;MAAkB,GAAC,KAAG,qBACjCK,mBAAA,CAAwE,OAAxEyK,WAAwE,EAAA7H,gBAAA,CAAvCxC,MAAA,CAAA+J,cAAc,CAACO,SAAS,SAAQ,GAAC,gB,GAEpE1K,mBAAA,CAGM,OAHN2K,WAGM,G,4BAFJ3K,mBAAA,CAAuC;QAAlCL,KAAK,EAAC;MAAkB,GAAC,KAAG,qBACjCK,mBAAA,CAA2E,OAA3E4K,WAA2E,EAAAhI,gBAAA,CAA1CxC,MAAA,CAAA+J,cAAc,CAACU,aAAa,sB;;uCAKnErJ,YAAA,CA2BWE,mBAAA;MA3BAC,IAAI,EAAEvB,MAAA,CAAA0K,WAAW;MAAEjJ,MAAM,EAAN,EAAM;MAACC,MAAM,EAAN,EAAM;MAACC,KAAsC,EAAtC;QAAA;QAAA;MAAA;;wBAC1C,MAA2C,CAA3CjC,YAAA,CAA2CkC,0BAAA;QAA1BjB,IAAI,EAAC,OAAO;QAACkB,KAAK,EAAC;UACpCnC,YAAA,CAAoDkC,0BAAA;QAAnCE,IAAI,EAAC,cAAc;QAAC1B,KAAK,EAAC;UAC3CV,YAAA,CAMkBkC,0BAAA;QANDE,IAAI,EAAC,OAAO;QAAC1B,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC,KAAK;QAAC8I,QAAQ,EAAR;;QACxC1I,OAAO,EAAAhB,QAAA,CAGTiB,KAHgB,KACvBtC,mBAAA,CAEO;UAFAL,KAAK,EAAAqL,eAAA,CAAE1I,KAAK,CAACG,GAAG,CAACkG,KAAK,IAAIvI,MAAA,CAAA0F,WAAW,CAACJ,UAAU;4BAClDpD,KAAK,CAACG,GAAG,CAACkG,KAAK,wB;;UAIxB7I,YAAA,CAIkBkC,0BAAA;QAJDxB,KAAK,EAAC,MAAM;QAACyB,KAAK,EAAC;;QACvBI,OAAO,EAAAhB,QAAA,CACyCiB,KADlC,K,kCACpBlC,MAAA,CAAAyC,UAAU,CAACP,KAAK,CAACG,GAAG,CAACwI,SAAS,sC;;UAGrCnL,YAAA,CAMkBkC,0BAAA;QANDxB,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC;;QACrBI,OAAO,EAAAhB,QAAA,CAGPiB,KAHc,KACvBxC,YAAA,CAESyC,iBAAA;UAFAxB,IAAI,EAAEuB,KAAK,CAACG,GAAG,CAACkG,KAAK,IAAIvI,MAAA,CAAA0F,WAAW,CAACJ,UAAU;;4BACtD,MAA8D,C,kCAA3DpD,KAAK,CAACG,GAAG,CAACkG,KAAK,IAAIvI,MAAA,CAAA0F,WAAW,CAACJ,UAAU,gC;;;;UAIlD5F,YAAA,CAIkBkC,0BAAA;QAJDxB,KAAK,EAAC,IAAI;QAACyB,KAAK,EAAC,KAAK;QAACc,KAAK,EAAC;;QACjCV,OAAO,EAAAhB,QAAA,CAC0EiB,KADnE,KACvBxC,YAAA,CAA0FgB,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACmC,IAAI,EAAC,OAAO;UAAElC,OAAK,EAAAL,MAAA,IAAEP,MAAA,CAAA8K,gBAAgB,CAAC5I,KAAK,CAACG,GAAG;;4BAAG,MAAEvB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDAxBUd,MAAA,CAAA+K,cAAc,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
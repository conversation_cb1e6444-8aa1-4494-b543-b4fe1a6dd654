{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { reactive, ref, onMounted } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { Lock } from '@element-plus/icons-vue';\nimport axios from 'axios';\nexport default {\n  __name: 'ResetPasswordView',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const route = useRoute();\n    const router = useRouter();\n    const resetFormRef = ref(null);\n    const loading = ref(false);\n    const token = ref('');\n    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n\n    // 表单数据\n    const resetForm = reactive({\n      newPassword: '',\n      confirmPassword: ''\n    });\n\n    // 自定义验证规则\n    const validatePass2 = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入密码'));\n      } else if (value !== resetForm.newPassword) {\n        callback(new Error('两次输入密码不一致!'));\n      } else {\n        callback();\n      }\n    };\n\n    // 表单验证规则\n    const resetRules = {\n      newPassword: [{\n        required: true,\n        message: '请输入新密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }],\n      confirmPassword: [{\n        required: true,\n        message: '请确认密码',\n        trigger: 'blur'\n      }, {\n        validator: validatePass2,\n        trigger: 'blur'\n      }]\n    };\n\n    // 处理重置密码\n    const handleReset = async () => {\n      if (!resetFormRef.value) return;\n      try {\n        await resetFormRef.value.validate(async valid => {\n          if (valid) {\n            loading.value = true;\n            try {\n              const response = await axios.post(`${API_URL}/auth/reset-password`, {\n                token: token.value,\n                newPassword: resetForm.newPassword\n              });\n              ElMessage.success('密码重置成功，请使用新密码登录');\n              router.push('/login');\n            } catch (error) {\n              console.error('重置密码失败:', error);\n              ElMessage.error(error.response?.data?.message || '重置密码失败，请检查链接是否有效');\n            } finally {\n              loading.value = false;\n            }\n          }\n        });\n      } catch (error) {\n        loading.value = false;\n        ElMessage.error('表单验证失败');\n      }\n    };\n\n    // 组件加载时从URL参数中获取token\n    onMounted(() => {\n      token.value = route.query.token;\n      if (!token.value) {\n        ElMessage.error('无效的重置链接，请重新获取');\n        router.push('/login');\n      }\n    });\n    const __returned__ = {\n      route,\n      router,\n      resetFormRef,\n      loading,\n      token,\n      API_URL,\n      resetForm,\n      validatePass2,\n      resetRules,\n      handleReset,\n      reactive,\n      ref,\n      onMounted,\n      get useRoute() {\n        return useRoute;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get Lock() {\n        return Lock;\n      },\n      get axios() {\n        return axios;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["reactive", "ref", "onMounted", "useRoute", "useRouter", "ElMessage", "Lock", "axios", "route", "router", "resetFormRef", "loading", "token", "API_URL", "process", "env", "VUE_APP_API_URL", "resetForm", "newPassword", "confirmPassword", "validatePass2", "rule", "value", "callback", "Error", "resetRules", "required", "message", "trigger", "min", "max", "validator", "handleReset", "validate", "valid", "response", "post", "success", "push", "error", "console", "data", "query"], "sources": ["D:/admin/202506/实习生管理系统/后台管理系统v2/后台管理系统/ms/src/views/ResetPasswordView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"reset-password-container\">\r\n    <div class=\"reset-password-card\">\r\n      <div class=\"logo-wrapper\">\r\n        <div class=\"logo-icon\">\r\n          <i class=\"el-icon-key\"></i>\r\n        </div>\r\n        <div class=\"logo-text\">\r\n          重置密码\r\n        </div>\r\n      </div>\r\n      \r\n      <el-form :model=\"resetForm\" :rules=\"resetRules\" ref=\"resetFormRef\" class=\"reset-form\">\r\n        <p class=\"form-subtitle\">请输入新密码</p>\r\n        \r\n        <el-form-item prop=\"newPassword\">\r\n          <el-input \r\n            v-model=\"resetForm.newPassword\" \r\n            type=\"password\" \r\n            placeholder=\"新密码\" \r\n            :prefix-icon=\"Lock\"\r\n            show-password>\r\n          </el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item prop=\"confirmPassword\">\r\n          <el-input \r\n            v-model=\"resetForm.confirmPassword\" \r\n            type=\"password\" \r\n            placeholder=\"确认新密码\" \r\n            :prefix-icon=\"Lock\"\r\n            show-password>\r\n          </el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item>\r\n          <el-button type=\"primary\" :loading=\"loading\" @click=\"handleReset\" class=\"reset-button\">\r\n            重置密码\r\n          </el-button>\r\n        </el-form-item>\r\n        \r\n        <div class=\"login-link\">\r\n          <span>记住密码了？</span>\r\n          <router-link to=\"/login\">返回登录</router-link>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Lock } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nconst route = useRoute()\r\nconst router = useRouter()\r\nconst resetFormRef = ref(null)\r\nconst loading = ref(false)\r\nconst token = ref('')\r\n\r\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\r\n\r\n// 表单数据\r\nconst resetForm = reactive({\r\n  newPassword: '',\r\n  confirmPassword: ''\r\n})\r\n\r\n// 自定义验证规则\r\nconst validatePass2 = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入密码'))\r\n  } else if (value !== resetForm.newPassword) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\n// 表单验证规则\r\nconst resetRules = {\r\n  newPassword: [\r\n    { required: true, message: '请输入新密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  confirmPassword: [\r\n    { required: true, message: '请确认密码', trigger: 'blur' },\r\n    { validator: validatePass2, trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 处理重置密码\r\nconst handleReset = async () => {\r\n  if (!resetFormRef.value) return\r\n  \r\n  try {\r\n    await resetFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        loading.value = true\r\n        \r\n        try {\r\n          const response = await axios.post(`${API_URL}/auth/reset-password`, {\r\n            token: token.value,\r\n            newPassword: resetForm.newPassword\r\n          })\r\n          \r\n          ElMessage.success('密码重置成功，请使用新密码登录')\r\n          router.push('/login')\r\n        } catch (error) {\r\n          console.error('重置密码失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '重置密码失败，请检查链接是否有效')\r\n        } finally {\r\n          loading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    loading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 组件加载时从URL参数中获取token\r\nonMounted(() => {\r\n  token.value = route.query.token\r\n  \r\n  if (!token.value) {\r\n    ElMessage.error('无效的重置链接，请重新获取')\r\n    router.push('/login')\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.reset-password-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: rgb(124, 181, 239);\r\n}\r\n\r\n.reset-password-card {\r\n  width: 400px;\r\n  background-color: white;\r\n  border-radius: 12px;\r\n  padding: 40px;\r\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  justify-content: center;\r\n}\r\n\r\n.logo-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background-color: #409EFF;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.form-subtitle {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.reset-form :deep(.el-input__wrapper) {\r\n  padding: 0 15px;\r\n  height: 50px;\r\n  box-shadow: 0 0 0 1px #e4e7ed inset;\r\n}\r\n\r\n.reset-form :deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n.reset-button {\r\n  width: 100%;\r\n  height: 50px;\r\n  border-radius: 6px;\r\n  font-size: 16px;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.login-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.login-link a {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n</style> "], "mappings": ";AAmDA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,SAAS,QAAQ,KAAK;AAC9C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAChD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,OAAOC,KAAK,MAAM,OAAO;;;;;;;IAEzB,MAAMC,KAAK,GAAGL,QAAQ,CAAC,CAAC;IACxB,MAAMM,MAAM,GAAGL,SAAS,CAAC,CAAC;IAC1B,MAAMM,YAAY,GAAGT,GAAG,CAAC,IAAI,CAAC;IAC9B,MAAMU,OAAO,GAAGV,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMW,KAAK,GAAGX,GAAG,CAAC,EAAE,CAAC;IAErB,MAAMY,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;;IAE1E;IACA,MAAMC,SAAS,GAAGjB,QAAQ,CAAC;MACzBkB,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;MAC/C,IAAID,KAAK,KAAK,EAAE,EAAE;QAChBC,QAAQ,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;MAChC,CAAC,MAAM,IAAIF,KAAK,KAAKL,SAAS,CAACC,WAAW,EAAE;QAC1CK,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;MACnC,CAAC,MAAM;QACLD,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;;IAED;IACA,MAAME,UAAU,GAAG;MACjBP,WAAW,EAAE,CACX;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDT,eAAe,EAAE,CACf;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEG,SAAS,EAAEX,aAAa;QAAEQ,OAAO,EAAE;MAAO,CAAC;IAEjD,CAAC;;IAED;IACA,MAAMI,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAACtB,YAAY,CAACY,KAAK,EAAE;MAEzB,IAAI;QACF,MAAMZ,YAAY,CAACY,KAAK,CAACW,QAAQ,CAAC,MAAOC,KAAK,IAAK;UACjD,IAAIA,KAAK,EAAE;YACTvB,OAAO,CAACW,KAAK,GAAG,IAAI;YAEpB,IAAI;cACF,MAAMa,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,IAAI,CAAC,GAAGvB,OAAO,sBAAsB,EAAE;gBAClED,KAAK,EAAEA,KAAK,CAACU,KAAK;gBAClBJ,WAAW,EAAED,SAAS,CAACC;cACzB,CAAC,CAAC;cAEFb,SAAS,CAACgC,OAAO,CAAC,iBAAiB,CAAC;cACpC5B,MAAM,CAAC6B,IAAI,CAAC,QAAQ,CAAC;YACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;cAC/BlC,SAAS,CAACkC,KAAK,CAACA,KAAK,CAACJ,QAAQ,EAAEM,IAAI,EAAEd,OAAO,IAAI,kBAAkB,CAAC;YACtE,CAAC,SAAS;cACRhB,OAAO,CAACW,KAAK,GAAG,KAAK;YACvB;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACd5B,OAAO,CAACW,KAAK,GAAG,KAAK;QACrBjB,SAAS,CAACkC,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC;;IAED;IACArC,SAAS,CAAC,MAAM;MACdU,KAAK,CAACU,KAAK,GAAGd,KAAK,CAACkC,KAAK,CAAC9B,KAAK;MAE/B,IAAI,CAACA,KAAK,CAACU,KAAK,EAAE;QAChBjB,SAAS,CAACkC,KAAK,CAAC,eAAe,CAAC;QAChC9B,MAAM,CAAC6B,IAAI,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
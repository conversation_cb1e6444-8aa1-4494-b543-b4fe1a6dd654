<template>
  <div class="app-container">
    <el-container class="layout-container">
      <!-- 左侧菜单 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo">
          <img src="../assets/logo.png" alt="logo" />
          <h1 v-show="!isCollapse">实习生学籍管理系统</h1>
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
            :collapse="isCollapse"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
            router
            :collapse-transition="false"
          >            
            <el-sub-menu index="/students" v-if="!isStudent">
              <template #title>
                <el-icon><User /></el-icon>
                <span>实习生管理</span>
              </template>
              <el-menu-item index="/students/list">实习生列表</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/courses">
              <template #title>
                <el-icon><Reading /></el-icon>
                <span>岗前培训</span>
              </template>
              <el-menu-item index="/courses/list">课程管理</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/exams">
              <template #title>
                <el-icon><DocumentChecked /></el-icon>
                <span>考核管理</span>
              </template>
              <el-menu-item index="/exams/list">考试列表</el-menu-item>
              <el-menu-item index="/exams/my-results" v-if="isStudent">我的考试成绩</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/rotations" v-if="!isStudent">
              <template #title>
                <el-icon><Refresh /></el-icon>
                <span>轮转管理</span>
              </template>
              <el-menu-item index="/rotations/list">轮转记录</el-menu-item>
              <el-menu-item index="/rotations/graduation">结业考核</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/users" v-if="!isStudent">
              <template #title>
                <el-icon><UserFilled /></el-icon>
                <span>用户管理</span>
              </template>
              <el-menu-item index="/users/list">用户列表</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      
      <!-- 右侧内容 -->
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="fold-icon" @click="toggleSidebar">
              <component :is="isCollapse ? 'Expand' : 'Fold'"></component>
            </el-icon>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="user-info">
                <el-avatar :size="30" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
                <span>{{ username }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="showChangePassword">修改密码</el-dropdown-item>
                  <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    <el-dialog
  title="修改密码"
  v-model="changePasswordVisible"
  width="400px"
  center
  destroy-on-close
>
  <el-form :model="changePasswordForm" :rules="changePasswordRules" ref="changePasswordFormRef" label-width="80px">
    <el-form-item label="手机号" prop="phone">
      <el-input v-model="changePasswordForm.phone" placeholder="请输入注册时的手机号"></el-input>
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword">
      <el-input v-model="changePasswordForm.newPassword" type="password" placeholder="请输入新密码" show-password></el-input>
    </el-form-item>
    <el-form-item label="确认密码" prop="confirmPassword">
      <el-input v-model="changePasswordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password></el-input>
    </el-form-item>
  </el-form>
  <template #footer>
    <span class="dialog-footer">
      <el-button @click="changePasswordVisible = false">取消</el-button>
      <el-button type="primary" :loading="changePasswordLoading" @click="handleChangePassword">确定</el-button>
    </span>
  </template>
</el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, ElDialog, ElForm, ElFormItem, ElInput, ElButton } from 'element-plus'
import axios from 'axios'

const API_URL = 'http://localhost:3000/api'

// 修改密码相关
const changePasswordVisible = ref(false)
const changePasswordFormRef = ref(null)
const changePasswordLoading = ref(false)

const changePasswordForm = reactive({
  phone: '',
  newPassword: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== changePasswordForm.newPassword) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const changePasswordRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const showChangePassword = () => {
  changePasswordVisible.value = true
  // 重置表单
  Object.assign(changePasswordForm, {
    phone: '',
    newPassword: '',
    confirmPassword: ''
  })
}

const handleChangePassword = async () => {
  if (!changePasswordFormRef.value) return
  
  try {
    await changePasswordFormRef.value.validate(async (valid) => {
      if (valid) {
        changePasswordLoading.value = true
        
        try {
          // 先验证手机号是否存在
          const checkResponse = await axios.get(`${API_URL}/auth/check-phone/${changePasswordForm.phone}`)
          
          if (!checkResponse.data.success) {
            ElMessage.error('该手机号未注册或不存在')
            return
          }
          
          // 修改密码
          const response = await axios.post(`${API_URL}/auth/change-password-by-phone`, {
            phone: changePasswordForm.phone,
            newPassword: changePasswordForm.newPassword
          })
          
          ElMessage.success('密码修改成功')
          changePasswordVisible.value = false
        } catch (error) {
          console.error('修改密码失败:', error)
          ElMessage.error(error.response?.data?.message || '修改密码失败，请稍后重试')
        } finally {
          changePasswordLoading.value = false
        }
      }
    })
  } catch (error) {
    changePasswordLoading.value = false
    ElMessage.error('表单验证失败')
  }
}

const router = useRouter()
const route = useRoute()
const isCollapse = ref(false)
const username = ref(localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).name : '用户')

const activeMenu = computed(() => {
  return route.path
})

const currentRoute = computed(() => {
  return route.meta.title || '实习生列表'
})

const isStudent = computed(() => {
  const userRole = localStorage.getItem('userRole')
  return userRole === 'student'
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 退出登录逻辑
    localStorage.clear()
    router.push('/login')
    ElMessage.success('已退出登录')
  }).catch(() => {})
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3649;
  color: #fff;
}

.logo img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.logo h1 {
  display: inline-block;
  margin: 0;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
}

.el-menu-vertical {
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 220px;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.fold-icon {
  font-size: 20px;
  cursor: pointer;
  margin-right: 10px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 20px;
  padding: 0 10px;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 10px;
}

.user-info span {
  margin: 0 5px;
}

.main {
  padding: 0 !important;
  background-color: #f0f2f5;
}
</style> 
<!-- 修改密码对话框 -->

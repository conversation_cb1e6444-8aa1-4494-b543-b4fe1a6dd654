{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card\"\n};\nconst _hoisted_3 = {\n  class: \"login-info\"\n};\nconst _hoisted_4 = {\n  class: \"feature-list\"\n};\nconst _hoisted_5 = {\n  class: \"feature-item\"\n};\nconst _hoisted_6 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_7 = {\n  class: \"feature-item\"\n};\nconst _hoisted_8 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_9 = {\n  class: \"feature-item\"\n};\nconst _hoisted_10 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_11 = {\n  class: \"login-form-wrapper\"\n};\nconst _hoisted_12 = {\n  class: \"login-form-container\"\n};\nconst _hoisted_13 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_14 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" Left side \"), _createElementVNode(\"div\", _hoisted_3, [_cache[17] || (_cache[17] = _createStaticVNode(\"<div class=\\\"logo-wrapper\\\" data-v-5c6101e4><div class=\\\"logo-icon\\\" data-v-5c6101e4><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"Logo\\\" class=\\\"logo-img\\\" data-v-5c6101e4><i class=\\\"el-icon-monitor\\\" data-v-5c6101e4></i></div><div class=\\\"logo-text\\\" data-v-5c6101e4> 实习生学籍管理系统 </div></div><div class=\\\"welcome-text\\\" data-v-5c6101e4><h2 data-v-5c6101e4>欢迎回来</h2><p data-v-5c6101e4>登录您的账户以继续访问系统</p></div>\", 2)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Check\"])]),\n    _: 1 /* STABLE */\n  })]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"feature-text\"\n  }, \"现代化的管理界面\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Check\"])]),\n    _: 1 /* STABLE */\n  })]), _cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"feature-text\"\n  }, \"强大的功能模块\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Check\"])]),\n    _: 1 /* STABLE */\n  })]), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"feature-text\"\n  }, \"安全可靠的数据保护\", -1 /* CACHED */))])])]), _createCommentVNode(\" Right side \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[19] || (_cache[19] = _createElementVNode(\"h2\", {\n    class: \"form-title\"\n  }, \"用户登录\", -1 /* CACHED */)), _cache[20] || (_cache[20] = _createElementVNode(\"p\", {\n    class: \"form-subtitle\"\n  }, \"请输入您的账户信息\", -1 /* CACHED */)), _createVNode(_component_el_form, {\n    model: $setup.loginForm,\n    rules: $setup.loginRules,\n    ref: \"loginFormRef\",\n    class: \"login-form\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      prop: \"username\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.loginForm.username = $event),\n        placeholder: \"输入账号\",\n        \"prefix-icon\": $setup.User\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.loginForm.password = $event),\n        type: \"password\",\n        placeholder: \"输入密码\",\n        \"prefix-icon\": $setup.Lock,\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        loading: $setup.loading,\n        onClick: $setup.handleLogin,\n        class: \"login-button\"\n      }, {\n        default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\" 登录 \")])),\n        _: 1 /* STABLE */,\n        __: [18]\n      }, 8 /* PROPS */, [\"loading\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])])])]), _createCommentVNode(\" 注册对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"用户注册\",\n    modelValue: $setup.registerDialogVisible,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.registerDialogVisible = $event),\n    width: \"400px\",\n    center: \"\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_13, [_createVNode(_component_el_button, {\n      onClick: _cache[8] || (_cache[8] = $event => $setup.registerDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.registerLoading,\n      onClick: $setup.handleRegister\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"注册\")])),\n      _: 1 /* STABLE */,\n      __: [22]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.registerForm,\n      rules: $setup.registerRules,\n      ref: \"registerFormRef\",\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.username,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.registerForm.username = $event),\n          placeholder: \"请输入用户名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"密码\",\n        prop: \"password\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.password,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.registerForm.password = $event),\n          type: \"password\",\n          placeholder: \"请输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"确认密码\",\n        prop: \"confirmPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.confirmPassword,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.registerForm.confirmPassword = $event),\n          type: \"password\",\n          placeholder: \"请再次输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"姓名\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.name,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.registerForm.name = $event),\n          placeholder: \"请输入姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.email,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.registerForm.email = $event),\n          placeholder: \"请输入邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.phone,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.registerForm.phone = $event),\n          placeholder: \"请输入手机号\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 忘记密码对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"忘记密码\",\n    modelValue: $setup.forgotPasswordDialogVisible,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.forgotPasswordDialogVisible = $event),\n    width: \"400px\",\n    center: \"\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_14, [_createVNode(_component_el_button, {\n      onClick: _cache[12] || (_cache[12] = $event => $setup.forgotPasswordDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.forgotPasswordLoading,\n      onClick: $setup.handleForgotPassword\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"提交\")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.forgotPasswordForm,\n      rules: $setup.forgotPasswordRules,\n      ref: \"forgotPasswordFormRef\",\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.forgotPasswordForm.username,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.forgotPasswordForm.username = $event),\n          placeholder: \"请输入用户名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.forgotPasswordForm.email,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.forgotPasswordForm.email = $event),\n          placeholder: \"请输入注册时的邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_icon", "$setup", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_component_el_form", "model", "loginForm", "rules", "loginRules", "ref", "_component_el_form_item", "prop", "_component_el_input", "username", "$event", "placeholder", "User", "password", "type", "Lock", "_component_el_button", "loading", "onClick", "handleLogin", "_cache", "_component_el_dialog", "title", "registerDialogVisible", "width", "center", "footer", "_withCtx", "_hoisted_13", "registerLoading", "handleRegister", "registerForm", "registerRules", "label", "confirmPassword", "name", "email", "phone", "forgotPasswordDialogVisible", "_hoisted_14", "forgotPasswordLoading", "handleForgotPassword", "forgotPasswordForm", "forgotPasswordRules"], "sources": ["D:\\admin\\202506\\实习生管理系统\\后台管理系统v2\\后台管理系统\\ms\\src\\views\\LoginView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card\">\r\n      <!-- Left side -->\r\n      <div class=\"login-info\">\r\n        <div class=\"logo-wrapper\">\r\n          <div class=\"logo-icon\">\r\n            <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo-img\" />\r\n            <i class=\"el-icon-monitor\"></i>\r\n          </div>\r\n          <div class=\"logo-text\">\r\n            实习生学籍管理系统\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"welcome-text\">\r\n          <h2>欢迎回来</h2>\r\n          <p>登录您的账户以继续访问系统</p>\r\n        </div>\r\n        \r\n        <div class=\"feature-list\">\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">现代化的管理界面</div>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">强大的功能模块</div>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <div class=\"feature-icon\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n            <div class=\"feature-text\">安全可靠的数据保护</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- Right side -->\r\n      <div class=\"login-form-wrapper\">\r\n        <div class=\"login-form-container\">\r\n          <h2 class=\"form-title\">用户登录</h2>\r\n          <p class=\"form-subtitle\">请输入您的账户信息</p>\r\n          \r\n          <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" class=\"login-form\">\r\n            <el-form-item prop=\"username\">\r\n              <el-input \r\n                v-model=\"loginForm.username\" \r\n                placeholder=\"输入账号\" \r\n                :prefix-icon=\"User\">\r\n              </el-input>\r\n            </el-form-item>\r\n            \r\n            <el-form-item prop=\"password\">\r\n              <el-input \r\n                v-model=\"loginForm.password\" \r\n                type=\"password\" \r\n                placeholder=\"输入密码\" \r\n                :prefix-icon=\"Lock\"\r\n                show-password>\r\n              </el-input>\r\n            </el-form-item>\r\n            \r\n           \r\n            \r\n            <el-form-item>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"handleLogin\" class=\"login-button\">\r\n                登录\r\n              </el-button>\r\n            </el-form-item>\r\n            \r\n           \r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 注册对话框 -->\r\n    <el-dialog\r\n      title=\"用户注册\"\r\n      v-model=\"registerDialogVisible\"\r\n      width=\"400px\"\r\n      center\r\n      destroy-on-close\r\n    >\r\n      <el-form :model=\"registerForm\" :rules=\"registerRules\" ref=\"registerFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"registerForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" :loading=\"registerLoading\" @click=\"handleRegister\">注册</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 忘记密码对话框 -->\r\n    <el-dialog\r\n      title=\"忘记密码\"\r\n      v-model=\"forgotPasswordDialogVisible\"\r\n      width=\"400px\"\r\n      center\r\n      destroy-on-close\r\n    >\r\n      <el-form :model=\"forgotPasswordForm\" :rules=\"forgotPasswordRules\" ref=\"forgotPasswordFormRef\" label-width=\"80px\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"forgotPasswordForm.username\" placeholder=\"请输入用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"forgotPasswordForm.email\" placeholder=\"请输入注册时的邮箱\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"forgotPasswordDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" :loading=\"forgotPasswordLoading\" @click=\"handleForgotPassword\">提交</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { User, Lock, Check } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\n// 配置全局API请求拦截器，自动添加token\r\naxios.interceptors.request.use(\r\n  config => {\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器，处理401错误\r\naxios.interceptors.response.use(\r\n  response => response,\r\n  error => {\r\n    if (error.response && error.response.status === 401) {\r\n      // 清除本地存储的token\r\n      localStorage.removeItem('token')\r\n      localStorage.removeItem('userInfo')\r\n      // 如果用户不在登录页，重定向到登录页\r\n      if (window.location.pathname !== '/login') {\r\n        ElMessage.error('登录已过期，请重新登录')\r\n        window.location.href = '/#/login'\r\n      }\r\n    }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nconst router = useRouter()\r\nconst loginFormRef = ref(null)\r\nconst loading = ref(false)\r\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\r\n\r\n// 登录相关\r\nconst loginForm = reactive({\r\n  username: '',\r\n  password: '',\r\n  remember: false\r\n})\r\n\r\nconst loginRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst handleLogin = async () => {\r\n  if (!loginFormRef.value) return\r\n  \r\n  try {\r\n    await loginFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        loading.value = true\r\n        \r\n        try {\r\n          const response = await axios.post(`${API_URL}/auth/login`, {\r\n            username: loginForm.username,\r\n            password: loginForm.password\r\n          })\r\n          \r\n          // 登录成功，保存token和用户信息\r\n          const { token, data } = response.data\r\n          localStorage.setItem('token', token)\r\n\r\n          // 如果选择记住我，保存用户名\r\n          if (loginForm.remember) {\r\n            localStorage.setItem('rememberedUsername', loginForm.username)\r\n          } else {\r\n            localStorage.removeItem('rememberedUsername')\r\n          }\r\n\r\n          // 存储用户信息\r\n          localStorage.setItem('userInfo', JSON.stringify(data))\r\n\r\n          // 单独保存关键信息，方便使用\r\n          localStorage.setItem('userId', data.id)\r\n          localStorage.setItem('userRole', data.role)\r\n          if (data.student_id) {\r\n            localStorage.setItem('studentId', data.student_id)\r\n            console.log('保存学生ID:', data.student_id)\r\n          } else if (data.role === 'student') {\r\n            // 如果是学生但没有student_id，尝试从其他地方获取\r\n            try {\r\n              const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`)\r\n              if (studentResponse.data.success && studentResponse.data.data) {\r\n                localStorage.setItem('studentId', studentResponse.data.data.id)\r\n                console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id)\r\n              } else {\r\n                console.error('无法获取学生ID，但用户角色为学生')\r\n                ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用')\r\n              }\r\n            } catch (err) {\r\n              console.error('获取学生信息失败:', err)\r\n            }\r\n          }\r\n\r\n          console.log('登录成功，用户信息:', data)\r\n          \r\n          ElMessage.success('登录成功')\r\n          \r\n          // 根据用户角色跳转到不同页面\r\n          if (data.role === 'student') {\r\n            router.push('/courses/list') // 学生跳转到课程管理\r\n          } else if (data.role === 'admin' || data.role === 'teacher') {\r\n            router.push('/students/list') // 管理员和教师跳转到实习生管理\r\n          } else {\r\n            router.push('/dashboard') // 其他角色跳转到首页\r\n          }\r\n        } catch (error) {\r\n          console.error('登录失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码')\r\n        } finally {\r\n          loading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    loading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 注册相关\r\nconst registerDialogVisible = ref(false)\r\nconst registerFormRef = ref(null)\r\nconst registerLoading = ref(false)\r\n\r\nconst registerForm = reactive({\r\n  username: '',\r\n  password: '',\r\n  confirmPassword: '',\r\n  name: '',\r\n  email: '',\r\n  phone: '',\r\n  student_id: '',\r\n  role: 'user'  // 默认注册为普通用户\r\n})\r\n\r\nconst validatePass = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入密码'))\r\n  } else if (value !== registerForm.password) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\nconst registerRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  confirmPassword: [\r\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\r\n    { validator: validatePass, trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showRegister = () => {\r\n  registerDialogVisible.value = true\r\n}\r\n\r\nconst handleRegister = async () => {\r\n  if (!registerFormRef.value) return\r\n  \r\n  try {\r\n    await registerFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        registerLoading.value = true\r\n        \r\n        try {\r\n          const { confirmPassword, ...registerData } = registerForm\r\n          \r\n          const response = await axios.post(`${API_URL}/auth/register`, registerData)\r\n          \r\n          ElMessage.success('注册成功，请登录')\r\n          registerDialogVisible.value = false\r\n          \r\n          // 可选：自动填充登录表单\r\n          loginForm.username = registerForm.username\r\n          loginForm.password = ''\r\n        } catch (error) {\r\n          console.error('注册失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')\r\n        } finally {\r\n          registerLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    registerLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 忘记密码相关\r\nconst forgotPasswordDialogVisible = ref(false)\r\nconst forgotPasswordFormRef = ref(null)\r\nconst forgotPasswordLoading = ref(false)\r\n\r\nconst forgotPasswordForm = reactive({\r\n  username: '',\r\n  email: ''\r\n})\r\n\r\nconst forgotPasswordRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\nconst showForgotPassword = () => {\r\n  forgotPasswordDialogVisible.value = true\r\n}\r\n\r\nconst handleForgotPassword = async () => {\r\n  if (!forgotPasswordFormRef.value) return\r\n  \r\n  try {\r\n    await forgotPasswordFormRef.value.validate(async (valid) => {\r\n      if (valid) {\r\n        forgotPasswordLoading.value = true\r\n        \r\n        try {\r\n          // 注意：需要在后端实现忘记密码API\r\n          const response = await axios.post(`${API_URL}/auth/forgot-password`, forgotPasswordForm)\r\n          \r\n          ElMessage.success('重置密码链接已发送到您的邮箱')\r\n          forgotPasswordDialogVisible.value = false\r\n        } catch (error) {\r\n          console.error('忘记密码请求失败:', error)\r\n          ElMessage.error(error.response?.data?.message || '操作失败，请稍后重试')\r\n        } finally {\r\n          forgotPasswordLoading.value = false\r\n        }\r\n      }\r\n    })\r\n  } catch (error) {\r\n    forgotPasswordLoading.value = false\r\n    ElMessage.error('表单验证失败')\r\n  }\r\n}\r\n\r\n// 检查是否有记住的用户名\r\nconst checkRememberedUsername = () => {\r\n  const rememberedUsername = localStorage.getItem('rememberedUsername')\r\n  if (rememberedUsername) {\r\n    loginForm.username = rememberedUsername\r\n    loginForm.remember = true\r\n  }\r\n}\r\n\r\n// 组件挂载时检查记住的用户名\r\ncheckRememberedUsername()\r\n</script>\r\n\r\n<style scoped>\r\n.login-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color:rgb(124, 181, 239);\r\n}\r\n\r\n.login-card {\r\n  width: 1000px;\r\n  height: 600px;\r\n  display: flex;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Left side */\r\n.login-info {\r\n  width: 50%;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  color: white;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.logo-icon {\r\n  width: 120px;\r\n  height: 120px;\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 24px;\r\n  color: #409EFF;\r\n}\r\n\r\n.logo-img {\r\n  width: 96px;\r\n  height: 96px;\r\n  margin-right: 4px;\r\n  object-fit: contain;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-text {\r\n  margin-bottom: 60px;\r\n}\r\n\r\n.welcome-text h2 {\r\n  font-size: 32px;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-text p {\r\n  font-size: 16px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.feature-list {\r\n  margin-top: auto;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.feature-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.feature-text {\r\n  font-size: 16px;\r\n}\r\n\r\n/* Right side */\r\n.login-form-wrapper {\r\n  width: 50%;\r\n  background-color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n}\r\n\r\n.login-form-container {\r\n  width: 100%;\r\n  max-width: 320px;\r\n}\r\n\r\n.form-title {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  text-align: center;\r\n}\r\n\r\n.form-subtitle {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-bottom: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.login-form :deep(.el-input__wrapper) {\r\n  padding: 0 15px;\r\n  height: 50px;\r\n  box-shadow: 0 0 0 1px #e4e7ed inset;\r\n}\r\n\r\n.login-form :deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n.form-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.forgot-link {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-button {\r\n  width: 100%;\r\n  height: 50px;\r\n  border-radius: 6px;\r\n  font-size: 16px;\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.register-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.register-link a {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n\r\n/* Responsive */\r\n@media (max-width: 992px) {\r\n  .login-card {\r\n    width: 90%;\r\n    height: auto;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .login-info,\r\n  .login-form-wrapper {\r\n    width: 100%;\r\n    padding: 30px;\r\n  }\r\n  \r\n  .login-info {\r\n    padding-bottom: 40px;\r\n  }\r\n  \r\n  .welcome-text {\r\n    margin-bottom: 30px;\r\n  }\r\n  \r\n  .feature-list {\r\n    margin-top: 0;\r\n  }\r\n}\r\n</style> "], "mappings": ";OAOiBA,UAAuB;;EANjCC,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAgBhBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAS1BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAkE3BA,KAAK,EAAC;AAAe;;EAwBrBA,KAAK,EAAC;AAAe;;;;;;;;uBArIjCC,mBAAA,CA2IM,OA3INC,UA2IM,GA1IJC,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJC,mBAAA,eAAkB,EAClBF,mBAAA,CAoCM,OApCNG,UAoCM,G,ucApBJH,mBAAA,CAmBM,OAnBNI,UAmBM,GAlBJJ,mBAAA,CAKM,OALNK,UAKM,GAJJL,mBAAA,CAEM,OAFNM,UAEM,GADJC,YAAA,CAA4BC,kBAAA;sBAAnB,MAAS,CAATD,YAAA,CAASE,MAAA,W;;oCAEpBT,mBAAA,CAAwC;IAAnCH,KAAK,EAAC;EAAc,GAAC,UAAQ,oB,GAEpCG,mBAAA,CAKM,OALNU,UAKM,GAJJV,mBAAA,CAEM,OAFNW,UAEM,GADJJ,YAAA,CAA4BC,kBAAA;sBAAnB,MAAS,CAATD,YAAA,CAASE,MAAA,W;;oCAEpBT,mBAAA,CAAuC;IAAlCH,KAAK,EAAC;EAAc,GAAC,SAAO,oB,GAEnCG,mBAAA,CAKM,OALNY,UAKM,GAJJZ,mBAAA,CAEM,OAFNa,WAEM,GADJN,YAAA,CAA4BC,kBAAA;sBAAnB,MAAS,CAATD,YAAA,CAASE,MAAA,W;;oCAEpBT,mBAAA,CAAyC;IAApCH,KAAK,EAAC;EAAc,GAAC,WAAS,oB,OAKzCK,mBAAA,gBAAmB,EACnBF,mBAAA,CAmCM,OAnCNc,WAmCM,GAlCJd,mBAAA,CAiCM,OAjCNe,WAiCM,G,4BAhCJf,mBAAA,CAAgC;IAA5BH,KAAK,EAAC;EAAY,GAAC,MAAI,qB,4BAC3BG,mBAAA,CAAsC;IAAnCH,KAAK,EAAC;EAAe,GAAC,WAAS,qBAElCU,YAAA,CA4BUS,kBAAA;IA5BAC,KAAK,EAAER,MAAA,CAAAS,SAAS;IAAGC,KAAK,EAAEV,MAAA,CAAAW,UAAU;IAAEC,GAAG,EAAC,cAAc;IAACxB,KAAK,EAAC;;sBACvE,MAMe,CANfU,YAAA,CAMee,uBAAA;MANDC,IAAI,EAAC;IAAU;wBAC3B,MAIW,CAJXhB,YAAA,CAIWiB,mBAAA;oBAHAf,MAAA,CAAAS,SAAS,CAACO,QAAQ;mEAAlBhB,MAAA,CAAAS,SAAS,CAACO,QAAQ,GAAAC,MAAA;QAC3BC,WAAW,EAAC,MAAM;QACjB,aAAW,EAAElB,MAAA,CAAAmB;;;QAIlBrB,YAAA,CAQee,uBAAA;MARDC,IAAI,EAAC;IAAU;wBAC3B,MAMW,CANXhB,YAAA,CAMWiB,mBAAA;oBALAf,MAAA,CAAAS,SAAS,CAACW,QAAQ;mEAAlBpB,MAAA,CAAAS,SAAS,CAACW,QAAQ,GAAAH,MAAA;QAC3BI,IAAI,EAAC,UAAU;QACfH,WAAW,EAAC,MAAM;QACjB,aAAW,EAAElB,MAAA,CAAAsB,IAAI;QAClB,eAAa,EAAb;;;QAMJxB,YAAA,CAIee,uBAAA;wBAHb,MAEY,CAFZf,YAAA,CAEYyB,oBAAA;QAFDF,IAAI,EAAC,SAAS;QAAEG,OAAO,EAAExB,MAAA,CAAAwB,OAAO;QAAGC,OAAK,EAAEzB,MAAA,CAAA0B,WAAW;QAAEtC,KAAK,EAAC;;0BAAe,MAEvFuC,MAAA,SAAAA,MAAA,Q,iBAFuF,MAEvF,E;;;;;;;sCASVlC,mBAAA,WAAc,EACdK,YAAA,CAiCY8B,oBAAA;IAhCVC,KAAK,EAAC,MAAM;gBACH7B,MAAA,CAAA8B,qBAAqB;+DAArB9B,MAAA,CAAA8B,qBAAqB,GAAAb,MAAA;IAC9Bc,KAAK,EAAC,OAAO;IACbC,MAAM,EAAN,EAAM;IACN,kBAAgB,EAAhB;;IAsBWC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHP3C,mBAAA,CAGO,QAHP4C,WAGO,GAFLrC,YAAA,CAAgEyB,oBAAA;MAApDE,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAAV,MAAA,IAAEjB,MAAA,CAAA8B,qBAAqB;;wBAAU,MAAEH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACpD7B,YAAA,CAA2FyB,oBAAA;MAAhFF,IAAI,EAAC,SAAS;MAAEG,OAAO,EAAExB,MAAA,CAAAoC,eAAe;MAAGX,OAAK,EAAEzB,MAAA,CAAAqC;;wBAAgB,MAAEV,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAvBnF,MAmBU,CAnBV7B,YAAA,CAmBUS,kBAAA;MAnBAC,KAAK,EAAER,MAAA,CAAAsC,YAAY;MAAG5B,KAAK,EAAEV,MAAA,CAAAuC,aAAa;MAAE3B,GAAG,EAAC,iBAAiB;MAAC,aAAW,EAAC;;wBACtF,MAEe,CAFfd,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,KAAK;QAAC1B,IAAI,EAAC;;0BAC7B,MAA0E,CAA1EhB,YAAA,CAA0EiB,mBAAA;sBAAvDf,MAAA,CAAAsC,YAAY,CAACtB,QAAQ;qEAArBhB,MAAA,CAAAsC,YAAY,CAACtB,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAExDpB,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,IAAI;QAAC1B,IAAI,EAAC;;0BAC5B,MAAuG,CAAvGhB,YAAA,CAAuGiB,mBAAA;sBAApFf,MAAA,CAAAsC,YAAY,CAAClB,QAAQ;qEAArBpB,MAAA,CAAAsC,YAAY,CAAClB,QAAQ,GAAAH,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACH,WAAW,EAAC,OAAO;UAAC,eAAa,EAAb;;;UAEhFpB,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,MAAM;QAAC1B,IAAI,EAAC;;0BAC9B,MAAgH,CAAhHhB,YAAA,CAAgHiB,mBAAA;sBAA7Ff,MAAA,CAAAsC,YAAY,CAACG,eAAe;qEAA5BzC,MAAA,CAAAsC,YAAY,CAACG,eAAe,GAAAxB,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACH,WAAW,EAAC,SAAS;UAAC,eAAa,EAAb;;;UAEzFpB,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,IAAI;QAAC1B,IAAI,EAAC;;0BAC5B,MAAqE,CAArEhB,YAAA,CAAqEiB,mBAAA;sBAAlDf,MAAA,CAAAsC,YAAY,CAACI,IAAI;qEAAjB1C,MAAA,CAAAsC,YAAY,CAACI,IAAI,GAAAzB,MAAA;UAAEC,WAAW,EAAC;;;UAEpDpB,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,IAAI;QAAC1B,IAAI,EAAC;;0BAC5B,MAAsE,CAAtEhB,YAAA,CAAsEiB,mBAAA;sBAAnDf,MAAA,CAAAsC,YAAY,CAACK,KAAK;qEAAlB3C,MAAA,CAAAsC,YAAY,CAACK,KAAK,GAAA1B,MAAA;UAAEC,WAAW,EAAC;;;UAErDpB,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,KAAK;QAAC1B,IAAI,EAAC;;0BAC7B,MAAuE,CAAvEhB,YAAA,CAAuEiB,mBAAA;sBAApDf,MAAA,CAAAsC,YAAY,CAACM,KAAK;qEAAlB5C,MAAA,CAAAsC,YAAY,CAACM,KAAK,GAAA3B,MAAA;UAAEC,WAAW,EAAC;;;;;;;qCAWzDzB,mBAAA,aAAgB,EAChBK,YAAA,CAqBY8B,oBAAA;IApBVC,KAAK,EAAC,MAAM;gBACH7B,MAAA,CAAA6C,2BAA2B;iEAA3B7C,MAAA,CAAA6C,2BAA2B,GAAA5B,MAAA;IACpCc,KAAK,EAAC,OAAO;IACbC,MAAM,EAAN,EAAM;IACN,kBAAgB,EAAhB;;IAUWC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHP3C,mBAAA,CAGO,QAHPuD,WAGO,GAFLhD,YAAA,CAAsEyB,oBAAA;MAA1DE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAV,MAAA,IAAEjB,MAAA,CAAA6C,2BAA2B;;wBAAU,MAAElB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC1D7B,YAAA,CAAuGyB,oBAAA;MAA5FF,IAAI,EAAC,SAAS;MAAEG,OAAO,EAAExB,MAAA,CAAA+C,qBAAqB;MAAGtB,OAAK,EAAEzB,MAAA,CAAAgD;;wBAAsB,MAAErB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAX/F,MAOU,CAPV7B,YAAA,CAOUS,kBAAA;MAPAC,KAAK,EAAER,MAAA,CAAAiD,kBAAkB;MAAGvC,KAAK,EAAEV,MAAA,CAAAkD,mBAAmB;MAAEtC,GAAG,EAAC,uBAAuB;MAAC,aAAW,EAAC;;wBACxG,MAEe,CAFfd,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,KAAK;QAAC1B,IAAI,EAAC;;0BAC7B,MAAgF,CAAhFhB,YAAA,CAAgFiB,mBAAA;sBAA7Df,MAAA,CAAAiD,kBAAkB,CAACjC,QAAQ;uEAA3BhB,MAAA,CAAAiD,kBAAkB,CAACjC,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAE9DpB,YAAA,CAEee,uBAAA;QAFD2B,KAAK,EAAC,IAAI;QAAC1B,IAAI,EAAC;;0BAC5B,MAAgF,CAAhFhB,YAAA,CAAgFiB,mBAAA;sBAA7Df,MAAA,CAAAiD,kBAAkB,CAACN,KAAK;uEAAxB3C,MAAA,CAAAiD,kBAAkB,CAACN,KAAK,GAAA1B,MAAA;UAAEC,WAAW,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
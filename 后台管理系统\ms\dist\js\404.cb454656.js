"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[404],{6404:function(e,t,l){l.r(t),l.d(t,{default:function(){return T}});l(4114),l(8111),l(2489),l(116),l(7588),l(1701),l(3579);var a=l(6768),o=l(4232),s=l(144),r=l(1219),i=l(2933),n=l(7477),u=l(782),d=l(1387),c=l(6434),p=l(9634);const m={class:"exam-list-container"},_={class:"filter-container"},v={class:"card-header"},g={class:"pagination-container"},k={class:"dialog-footer"},b={key:0,class:"question-dialog-content"},f={class:"question-header"},h={class:"question-actions"},y={class:"question-table-wrapper"},w=["innerHTML"],x={class:"import-dialog-content"},F={class:"template-download"},C={class:"dialog-footer"},V={class:"options-header"},R={class:"option-item"},q={class:"dialog-footer"},$={key:0,class:"results-dialog-content"},L={class:"statistics-items"},E={class:"statistics-item"},W={class:"statistics-value"},K={class:"statistics-item"},A={class:"statistics-value"},I={class:"statistics-item"},S={class:"statistics-value"},U={class:"statistics-item"},M={class:"statistics-value"};var Y={__name:"ExamList",setup(e){const t=(0,u.Pj)(),l=(0,d.rd)(),Y=(0,s.KR)(!0),z=(0,s.KR)(1),Q=(0,s.KR)(10),T=(0,s.KR)(0),X=(0,s.KR)(!1),D=(0,s.KR)("新增考试"),H=(0,s.KR)(null),B=(0,s.KR)(null);let j=localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):{};const N=(0,s.KR)(""),O=(0,s.KR)("admin"==j?.role),P=(0,s.KR)("teacher"==j?.role),J=(0,s.KR)("student"==j?.role);console.log(J,P,"---123");const G=(0,s.KR)("student"!==j?.role);(0,a.wB)(()=>t.state.user,e=>{console.log("用户信息变更:",e),Z()},{deep:!0});const Z=()=>{N.value=localStorage.getItem("userRole")||"",console.log("当前用户角色:",N.value),console.log("管理员权限:",O.value),console.log("教师权限:",P.value),console.log("学生权限:",J.value),console.log("可管理考试:",G.value)},ee=(0,s.KR)(!1),te=(0,s.KR)(!1),le=(0,s.KR)("新增试题"),ae=(0,s.KR)(null),oe=(0,s.KR)(null),se=(0,s.KR)([]),re=(0,s.KR)(!1),ie=(0,s.KR)(1),ne=(0,s.KR)(0),ue=(0,s.KR)(!1),de=(0,s.KR)([]),ce=(0,s.KR)(!1),pe=(0,s.KR)(!1),me=(0,s.KR)([]),_e=(0,s.KR)(!1),ve=(0,s.Kh)({total_students:0,passed_students:0,pass_rate:0,average_score:0}),ge=(0,s.KR)({}),ke=(0,s.KR)({}),be=(0,s.Kh)({title:""}),fe=(0,s.Kh)({id:null,title:"",description:"",duration:60,pass_score:60,total_score:100}),he={title:[{required:!0,message:"请输入考试标题",trigger:"blur"}],duration:[{required:!0,message:"请输入考试时长",trigger:"blur"},{type:"number",min:1,message:"考试时长必须大于0",trigger:"blur"}],pass_score:[{required:!0,message:"请输入及格分数",trigger:"blur"},{type:"number",min:1,message:"及格分数必须大于0",trigger:"blur"}],total_score:[{required:!0,message:"请输入总分",trigger:"blur"},{type:"number",min:1,message:"总分必须大于0",trigger:"blur"}]},ye=(0,s.Kh)({id:null,question:"",question_type:"单选题",correct_answer:"",score:5,options:[{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1}]}),we={question:[{required:!0,message:"请输入题目内容",trigger:"blur"}],question_type:[{required:!0,message:"请选择题目类型",trigger:"change"}],score:[{required:!0,message:"请输入分值",trigger:"blur"},{type:"number",min:1,message:"分值必须大于0",trigger:"blur"}]},xe=(0,s.KR)([]);T.value=xe.length;const Fe=async()=>{Y.value=!0,Z(),localStorage.getItem("userRole")&&t.state.role!==localStorage.getItem("userRole")&&t.commit("SET_ROLE",localStorage.getItem("userRole"));try{const e=await c.A.getExams({page:z.value,limit:Q.value,title:be.title});xe.value=e.data.data,T.value=e.data.total||xe.value.length,J.value&&await Ce(),Y.value=!1}catch(e){console.error("获取考试列表失败",e),r.nk.error("获取考试列表失败"),Y.value=!1}},Ce=async()=>{try{console.log("正在获取所有考试的尝试次数...学生ID:",studentId);const e=xe.value.map(async e=>{try{console.log(`正在查询考试 ${e.id} (${e.title}) 的尝试记录...`);const t=await c.A.getExamResults(e.id,{student_id:studentId});if(console.log(`考试 ${e.id} 返回数据:`,t.data),t.data&&t.data.data){const l=t.data.data.results||[],a=l.length,o=Math.max(0,2-a);console.log(`考试 ${e.id} (${e.title}): 已尝试 ${a} 次，剩余 ${o} 次`),ge.value[e.id]=o,ke.value[e.id]=a>0,l.length>0&&l.forEach((e,t)=>{console.log(`  尝试 ${t+1}: 得分 ${e.score}，时间 ${e.exam_date}`)})}else console.warn(`考试 ${e.id} 未返回有效数据，设置默认剩余次数为2`),ge.value[e.id]=2,ke.value[e.id]=!1}catch(t){console.error(`获取考试 ${e.id} 尝试次数失败:`,t),ge.value[e.id]=2,ke.value[e.id]=!1}});await Promise.all(e),console.log("所有考试尝试次数获取完成:",ge.value),console.log("已参加过的考试:",ke.value)}catch(e){console.error("获取考试尝试次数失败:",e),xe.value.forEach(e=>{ge.value[e.id]=2,ke.value[e.id]=!1})}},Ve=e=>{if(B.value=e.id,!ge.value[e.id]&&0!==ge.value[e.id]){const t=j.student_id;return t?(r.nk.info("正在获取考试尝试信息，请稍候..."),void c.A.getExamResults(e.id,{student_id:t}).then(t=>{if(console.log(`考试 ${e.id} 尝试信息:`,t.data),t.data&&t.data.data){const l=t.data.data.results||[],a=l.length,o=Math.max(0,2-a);console.log(`考试 ${e.id} (${e.title}): 已尝试 ${a} 次，剩余 ${o} 次`),ge.value[e.id]=o,ke.value[e.id]=a>0,B.value=null,Re(e)}else console.warn(`考试 ${e.id} 未返回有效数据，设置默认剩余次数为2`),ge.value[e.id]=2,ke.value[e.id]=!1,B.value=null,Re(e)}).catch(e=>{console.error("获取考试尝试次数失败",e),r.nk.error("获取考试尝试次数失败，请刷新页面重试"),B.value=null})):(r.nk.warning("未找到有效的学生ID，请重新登录"),void(B.value=null))}B.value=null,Re(e)},Re=e=>{0!==ge.value[e.id]?i.s.confirm(`您总共有2次尝试机会，已使用 ${2-ge.value[e.id]} 次，还剩 ${ge.value[e.id]} 次机会。确定要参加考试吗？`,"参加考试",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.setItem("currentExamId",e.id),localStorage.setItem("currentExamTitle",e.title),localStorage.setItem("currentExamRemainingAttempts",ge.value[e.id]),l.push(`/exams/take/${e.id}`)}).catch(()=>{console.log("用户取消参加考试")}):r.nk.warning("您已达到该考试的最大尝试次数（2次）")};(0,a.sV)(()=>{qe(),Fe()});const qe=()=>{const e=localStorage.getItem("userRole");if(e){console.log("从localStorage加载角色:",e),t.commit("SET_ROLE",e);const l=localStorage.getItem("userId");l&&t.dispatch("fetchUserProfile").catch(e=>{console.error("获取用户信息失败:",e)})}else console.warn("未找到用户角色信息");Z()},$e=()=>{be.title="",Le()},Le=()=>{z.value=1,Fe()},Ee=()=>{D.value="新增考试",X.value=!0,fe.id=null,fe.title="",fe.description="",fe.duration=60,fe.pass_score=60,fe.total_score=100},We=e=>{D.value="编辑考试",X.value=!0,c.A.getExam(e.id).then(e=>{const t=e.data.data;Object.keys(fe).forEach(e=>{["duration","pass_score","total_score"].includes(e)?fe[e]=Number(t[e]||0):fe[e]=t[e]})}).catch(t=>{console.error("获取考试详情失败",t),r.nk.error("获取考试详情失败"),Object.keys(fe).forEach(t=>{fe[t]=e[t]})})},Ke=e=>{i.s.confirm(`确定要删除考试 ${e.title} 吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await c.A.deleteExam(e.id),r.nk.success(`考试 ${e.title} 已删除`),Fe()}catch(t){console.error("删除考试失败",t),r.nk.error("删除考试失败，请重试")}}).catch(()=>{})},Ae=async()=>{H.value&&await H.value.validate(async e=>{if(!e)return!1;try{fe.id?(await c.A.updateExam(fe.id,fe),r.nk.success(`考试 ${fe.title} 信息已更新`)):(await c.A.createExam(fe),r.nk.success(`考试 ${fe.title} 添加成功`)),X.value=!1,Fe()}catch(t){console.error("保存考试失败",t),r.nk.error("保存考试失败，请重试")}})},Ie=e=>{oe.value=e,ee.value=!0,ie.value=1,Se(e.id),setTimeout(()=>{const e=document.querySelector(".question-table-wrapper .el-table");e&&(e.style.height="550px")},100)},Se=async e=>{re.value=!0;try{const t=await c.A.getExamQuestions(e);console.log("加载试题数据返回:",t.data),t.data&&t.data.data?(se.value=t.data.data,ne.value=t.data.count||se.value.length,console.log(`加载了 ${se.value.length} 道试题`)):(se.value=[],ne.value=0,console.warn("未找到试题数据")),re.value=!1}catch(t){console.error("获取试题失败",t),r.nk.error("获取试题失败"),re.value=!1}},Ue=()=>{le.value="新增试题",te.value=!0,ye.id=null,ye.question="",ye.question_type="单选题",ye.correct_answer="",ye.score=5,ye.options=[{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1}]},Me=e=>{le.value="编辑试题",te.value=!0,ye.id=e.id,ye.question=e.question,ye.question_type=e.question_type,ye.correct_answer=e.correct_answer,ye.score=e.score,"单选题"===e.question_type||"多选题"===e.question_type?ye.options=e.options?[...e.options]:[]:"判断题"===e.question_type&&(ye.correct_answer=e.correct_answer)},Ye=e=>{i.s.confirm("确定要删除该试题吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await c.A.deleteQuestion(e.id),r.nk.success("试题已删除"),Se(oe.value.id)}catch(t){console.error("删除试题失败",t),r.nk.error("删除试题失败，请重试")}}).catch(()=>{})},ze=()=>{ue.value=!0,de.value=[]},Qe=e=>{const t="application/msword"===e.type||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type;if(!t)return r.nk.error("请上传Word格式的文件!"),!1;const l=e.size/1024/1024<10;return!!l||(r.nk.error("文件大小不能超过10MB!"),!1)},Te=(e,t)=>{console.log("文件选择变化:",e,t),de.value=t},Xe=e=>{const{file:t}=e;return!1},De=async()=>{if(console.log("当前文件列表:",de.value),de.value&&0!==de.value.length){ce.value=!0;try{const e=new FormData,t=de.value[0],l=t.raw||t;console.log("上传文件:",l),e.append("template",l);const a=await c.A.importQuestionsFromWord(oe.value.id,e);r.nk.success(`试题导入成功，共导入${a.data.count||0}道题目`),ue.value=!1,Se(oe.value.id)}catch(e){console.error("导入试题失败",e);let t="导入试题失败";e.response&&e.response.data&&e.response.data.message&&(t+=`：${e.response.data.message}`),r.nk.error(t)}finally{ce.value=!1}}else r.nk.warning("请选择要上传的文件")},He=async()=>{try{r.nk.success("模板下载中...");const e=document.createElement("a");e.href=`${(void 0).VITE_API_URL||"http://localhost:3000"}/api/exams/template/download`;const t=localStorage.getItem("token");t&&(e.href+=`?token=${t}`),e.download="试题导入模板.docx",document.body.appendChild(e),e.click(),document.body.removeChild(e)}catch(e){console.error("下载模板失败",e),r.nk.error("下载模板失败，请重试")}},Be=()=>{ye.options.push({text:"",isCorrect:!1})},je=e=>{ye.options.length<=2?r.nk.warning("至少需要2个选项"):ye.options.splice(e,1)},Ne=async()=>{ae.value&&await ae.value.validate(async e=>{if(!e)return!1;if("单选题"===ye.question_type||"多选题"===ye.question_type){const e=ye.options.find(e=>!e.text.trim());if(e)return void r.nk.error("选项内容不能为空");const t=ye.options.some(e=>e.isCorrect);if(!t)return void r.nk.error("请至少选择一个正确答案");if("单选题"===ye.question_type){const e=ye.options.filter(e=>e.isCorrect).length;if(e>1)return void r.nk.error("单选题只能有一个正确答案")}}try{const e={...ye};if("单选题"===ye.question_type){const t=ye.options.find(e=>e.isCorrect);if(t){const t=ye.options.findIndex(e=>e.isCorrect);e.correct_answer=String.fromCharCode(65+t)}}else if("多选题"===ye.question_type){const t=ye.options.map((e,t)=>e.isCorrect?String.fromCharCode(65+t):null).filter(Boolean).join("");e.correct_answer=t}ye.id?(await c.A.updateQuestion(ye.id,e),r.nk.success("试题更新成功")):(await c.A.createQuestion(oe.value.id,e),r.nk.success("试题添加成功")),te.value=!1,Se(oe.value.id)}catch(t){console.error("保存试题失败",t),r.nk.error("保存试题失败，请重试")}})},Oe=e=>e?e.length>100?e.substring(0,100)+"...":e:"",Pe=async e=>{oe.value=e,pe.value=!0,_e.value=!0;try{const t=await c.A.getExamResults(e.id),l=t.data.data;me.value=l.results||[],ve.total_students=l.summary.total_students||0,ve.passed_students=l.summary.passed_students||0,ve.pass_rate=l.summary.pass_rate||0,ve.average_score=l.summary.average_score||0,_e.value=!1}catch(t){console.error("获取成绩列表失败",t),r.nk.error("获取成绩列表失败"),_e.value=!1}},Je=e=>{r.nk.success(`查看 ${e.student_name} 的考试详情`)},Ge=e=>{Q.value=e,z.value=1,Fe()},Ze=e=>{z.value=e,Fe()};return(e,t)=>{const l=(0,a.g2)("el-input"),r=(0,a.g2)("el-form-item"),i=(0,a.g2)("el-button"),u=(0,a.g2)("el-form"),d=(0,a.g2)("el-card"),c=(0,a.g2)("el-table-column"),j=(0,a.g2)("el-tag"),N=(0,a.g2)("el-table"),O=(0,a.g2)("el-pagination"),P=(0,a.g2)("el-input-number"),Z=(0,a.g2)("el-col"),ie=(0,a.g2)("el-row"),ne=(0,a.g2)("el-dialog"),ke=(0,a.g2)("el-alert"),Fe=(0,a.g2)("el-icon"),Ce=(0,a.g2)("el-upload"),Re=(0,a.g2)("el-option"),qe=(0,a.g2)("el-select"),Se=(0,a.g2)("el-checkbox"),et=(0,a.g2)("el-radio"),tt=(0,a.g2)("el-radio-group"),lt=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",m,[(0,a.bF)(d,{class:"filter-card"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",_,[(0,a.bF)(u,{model:be,inline:""},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"考试标题"},{default:(0,a.k6)(()=>[(0,a.bF)(l,{modelValue:be.title,"onUpdate:modelValue":t[0]||(t[0]=e=>be.title=e),placeholder:"请输入考试标题",clearable:""},null,8,["modelValue"])]),_:1}),(0,a.bF)(r,null,{default:(0,a.k6)(()=>[(0,a.bF)(i,{type:"primary",onClick:Le},{default:(0,a.k6)(()=>t[19]||(t[19]=[(0,a.eW)("搜索")])),_:1,__:[19]}),(0,a.bF)(i,{onClick:$e},{default:(0,a.k6)(()=>t[20]||(t[20]=[(0,a.eW)("重置")])),_:1,__:[20]})]),_:1})]),_:1},8,["model"])])]),_:1}),(0,a.bF)(d,{class:"table-card"},{header:(0,a.k6)(()=>[(0,a.Lk)("div",v,[t[22]||(t[22]=(0,a.Lk)("span",null,"考试列表",-1)),(0,a.Lk)("div",null,[G.value?((0,a.uX)(),(0,a.Wv)(i,{key:0,type:"primary",onClick:Ee},{default:(0,a.k6)(()=>t[21]||(t[21]=[(0,a.eW)("新增考试")])),_:1,__:[21]})):(0,a.Q3)("",!0)])])]),default:(0,a.k6)(()=>[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(N,{data:xe.value,stripe:"",border:"",style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(c,{type:"index",width:"50"}),(0,a.bF)(c,{prop:"title",label:"考试标题","min-width":"200"}),(0,a.bF)(c,{prop:"description",label:"考试描述","min-width":"200","show-overflow-tooltip":""}),(0,a.bF)(c,{prop:"duration",label:"考试时长(分钟)",width:"120"}),(0,a.bF)(c,{prop:"pass_score",label:"及格分数",width:"100"}),(0,a.bF)(c,{prop:"total_score",label:"总分",width:"80"}),J.value?((0,a.uX)(),(0,a.Wv)(c,{key:0,label:"考试机会",width:"120"},{default:(0,a.k6)(e=>[(0,a.bF)(j,{type:ge.value[e.row.id]>0?"success":"danger",effect:"plain"},{default:(0,a.k6)(()=>[(0,a.eW)(" 已用 "+(0,o.v_)(2-ge.value[e.row.id])+"/2 次 ",1)]),_:2},1032,["type"])]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(c,{label:"创建时间",width:"160"},{default:(0,a.k6)(e=>[(0,a.eW)((0,o.v_)((0,s.R1)(p.Y)(e.row.created_at,"YYYY-MM-DD HH:mm")),1)]),_:1}),(0,a.bF)(c,{label:"操作",width:"320",fixed:"right"},{default:(0,a.k6)(e=>[G.value?((0,a.uX)(),(0,a.CE)(a.FK,{key:0},[(0,a.bF)(i,{type:"primary",size:"small",onClick:t=>Ie(e.row)},{default:(0,a.k6)(()=>t[23]||(t[23]=[(0,a.eW)("试题管理")])),_:2,__:[23]},1032,["onClick"]),(0,a.bF)(i,{type:"success",size:"small",onClick:t=>Pe(e.row)},{default:(0,a.k6)(()=>t[24]||(t[24]=[(0,a.eW)("成绩查看")])),_:2,__:[24]},1032,["onClick"]),(0,a.bF)(i,{type:"warning",size:"small",onClick:t=>We(e.row)},{default:(0,a.k6)(()=>t[25]||(t[25]=[(0,a.eW)("编辑")])),_:2,__:[25]},1032,["onClick"]),(0,a.bF)(i,{type:"danger",size:"small",onClick:t=>Ke(e.row)},{default:(0,a.k6)(()=>t[26]||(t[26]=[(0,a.eW)("删除")])),_:2,__:[26]},1032,["onClick"])],64)):((0,a.uX)(),(0,a.Wv)(i,{key:1,type:"primary",size:"small",onClick:t=>Ve(e.row),disabled:0===ge.value[e.row.id],loading:Y.value&&B.value===e.row.id},{default:(0,a.k6)(()=>[(0,a.eW)((0,o.v_)(0===ge.value[e.row.id]?"已无机会":"参加考试"),1)]),_:2},1032,["onClick","disabled","loading"]))]),_:1})]),_:1},8,["data"])),[[lt,Y.value]]),(0,a.Lk)("div",g,[(0,a.bF)(O,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":z.value,"page-sizes":[10,20,50,100],"page-size":Q.value,total:T.value,onSizeChange:Ge,onCurrentChange:Ze},null,8,["current-page","page-size","total"])])]),_:1}),(0,a.bF)(ne,{title:D.value,modelValue:X.value,"onUpdate:modelValue":t[7]||(t[7]=e=>X.value=e),width:"600px"},{footer:(0,a.k6)(()=>[(0,a.Lk)("div",k,[(0,a.bF)(i,{onClick:t[6]||(t[6]=e=>X.value=!1)},{default:(0,a.k6)(()=>t[27]||(t[27]=[(0,a.eW)("取消")])),_:1,__:[27]}),(0,a.bF)(i,{type:"primary",onClick:Ae},{default:(0,a.k6)(()=>t[28]||(t[28]=[(0,a.eW)("确定")])),_:1,__:[28]})])]),default:(0,a.k6)(()=>[(0,a.bF)(u,{model:fe,rules:he,ref_key:"examFormRef",ref:H,"label-width":"100px"},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"考试标题",prop:"title"},{default:(0,a.k6)(()=>[(0,a.bF)(l,{modelValue:fe.title,"onUpdate:modelValue":t[1]||(t[1]=e=>fe.title=e),placeholder:"请输入考试标题"},null,8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"考试描述",prop:"description"},{default:(0,a.k6)(()=>[(0,a.bF)(l,{type:"textarea",modelValue:fe.description,"onUpdate:modelValue":t[2]||(t[2]=e=>fe.description=e),placeholder:"请输入考试描述",rows:"3"},null,8,["modelValue"])]),_:1}),(0,a.bF)(ie,{gutter:20},{default:(0,a.k6)(()=>[(0,a.bF)(Z,{span:24},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"考试时长",prop:"duration"},{default:(0,a.k6)(()=>[(0,a.bF)(P,{modelValue:fe.duration,"onUpdate:modelValue":t[3]||(t[3]=e=>fe.duration=e),min:1,max:240,placeholder:"分钟",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,a.bF)(Z,{span:24},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"及格分数",prop:"pass_score"},{default:(0,a.k6)(()=>[(0,a.bF)(P,{modelValue:fe.pass_score,"onUpdate:modelValue":t[4]||(t[4]=e=>fe.pass_score=e),min:1,max:fe.total_score,style:{width:"100%"}},null,8,["modelValue","max"])]),_:1})]),_:1}),(0,a.bF)(Z,{span:24},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"总分",prop:"total_score"},{default:(0,a.k6)(()=>[(0,a.bF)(P,{modelValue:fe.total_score,"onUpdate:modelValue":t[5]||(t[5]=e=>fe.total_score=e),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),(0,a.bF)(ne,{title:"试题管理",modelValue:ee.value,"onUpdate:modelValue":t[8]||(t[8]=e=>ee.value=e),width:"850px",fullscreen:!1,"close-on-click-modal":!1},{default:(0,a.k6)(()=>[oe.value?((0,a.uX)(),(0,a.CE)("div",b,[(0,a.Lk)("div",f,[(0,a.Lk)("h3",null,(0,o.v_)(oe.value.title),1),(0,a.Lk)("div",h,[(0,a.bF)(i,{type:"primary",onClick:Ue},{default:(0,a.k6)(()=>t[29]||(t[29]=[(0,a.eW)("新增试题")])),_:1,__:[29]}),(0,a.bF)(i,{type:"success",onClick:ze},{default:(0,a.k6)(()=>t[30]||(t[30]=[(0,a.eW)("批量导入")])),_:1,__:[30]})])]),(0,a.Lk)("div",y,[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(N,{data:se.value,stripe:"",border:"",style:{width:"100%","margin-top":"15px"},"max-height":550,"show-header":!0},{default:(0,a.k6)(()=>[(0,a.bF)(c,{type:"index",width:"50"}),(0,a.bF)(c,{label:"题目内容","min-width":"300"},{default:(0,a.k6)(e=>[(0,a.Lk)("div",{innerHTML:Oe(e.row.question)},null,8,w)]),_:1}),(0,a.bF)(c,{prop:"question_type",label:"题型",width:"100"}),(0,a.bF)(c,{prop:"score",label:"分值",width:"80"}),(0,a.bF)(c,{label:"操作",width:"150",fixed:"right"},{default:(0,a.k6)(e=>[(0,a.bF)(i,{type:"warning",size:"small",onClick:t=>Me(e.row)},{default:(0,a.k6)(()=>t[31]||(t[31]=[(0,a.eW)("编辑")])),_:2,__:[31]},1032,["onClick"]),(0,a.bF)(i,{type:"danger",size:"small",onClick:t=>Ye(e.row)},{default:(0,a.k6)(()=>t[32]||(t[32]=[(0,a.eW)("删除")])),_:2,__:[32]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[lt,re.value]])])])):(0,a.Q3)("",!0)]),_:1},8,["modelValue"]),(0,a.bF)(ne,{title:"批量导入试题",modelValue:ue.value,"onUpdate:modelValue":t[10]||(t[10]=e=>ue.value=e),width:"500px"},{footer:(0,a.k6)(()=>[(0,a.Lk)("div",C,[(0,a.bF)(i,{onClick:t[9]||(t[9]=e=>ue.value=!1)},{default:(0,a.k6)(()=>t[37]||(t[37]=[(0,a.eW)("取消")])),_:1,__:[37]}),(0,a.bF)(i,{type:"primary",onClick:De,loading:ce.value},{default:(0,a.k6)(()=>t[38]||(t[38]=[(0,a.eW)("上传")])),_:1,__:[38]},8,["loading"])])]),default:(0,a.k6)(()=>[(0,a.Lk)("div",x,[(0,a.bF)(ke,{title:"请上传Word格式的试题模板文件",type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}}),(0,a.bF)(Ce,{class:"upload-demo",drag:"",action:"#","http-request":Xe,"before-upload":Qe,limit:1,"file-list":de.value,"auto-upload":!1,"on-change":Te,accept:".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"},{tip:(0,a.k6)(()=>t[33]||(t[33]=[(0,a.Lk)("div",{class:"el-upload__tip"}," 仅支持 .doc/.docx 格式文件 ",-1)])),default:(0,a.k6)(()=>[(0,a.bF)(Fe,{class:"el-icon--upload"},{default:(0,a.k6)(()=>[(0,a.bF)((0,s.R1)(n.UploadFilled))]),_:1}),t[34]||(t[34]=(0,a.Lk)("div",{class:"el-upload__text"},[(0,a.eW)(" 拖拽文件到此处或 "),(0,a.Lk)("em",null,"点击上传")],-1))]),_:1,__:[34]},8,["file-list"]),(0,a.Lk)("div",F,[t[36]||(t[36]=(0,a.Lk)("span",null,"没有模板？",-1)),(0,a.bF)(i,{type:"primary",link:"",onClick:He},{default:(0,a.k6)(()=>t[35]||(t[35]=[(0,a.eW)("下载试题模板")])),_:1,__:[35]})])])]),_:1},8,["modelValue"]),(0,a.bF)(ne,{title:le.value,modelValue:te.value,"onUpdate:modelValue":t[17]||(t[17]=e=>te.value=e),width:"700px"},{footer:(0,a.k6)(()=>[(0,a.Lk)("div",q,[(0,a.bF)(i,{onClick:t[16]||(t[16]=e=>te.value=!1)},{default:(0,a.k6)(()=>t[44]||(t[44]=[(0,a.eW)("取消")])),_:1,__:[44]}),(0,a.bF)(i,{type:"primary",onClick:Ne},{default:(0,a.k6)(()=>t[45]||(t[45]=[(0,a.eW)("确定")])),_:1,__:[45]})])]),default:(0,a.k6)(()=>[(0,a.bF)(u,{model:ye,rules:we,ref_key:"questionFormRef",ref:ae,"label-width":"100px"},{default:(0,a.k6)(()=>[(0,a.bF)(r,{label:"题目类型",prop:"question_type"},{default:(0,a.k6)(()=>[(0,a.bF)(qe,{modelValue:ye.question_type,"onUpdate:modelValue":t[11]||(t[11]=e=>ye.question_type=e),style:{width:"100%"}},{default:(0,a.k6)(()=>[(0,a.bF)(Re,{label:"单选题",value:"单选题"}),(0,a.bF)(Re,{label:"多选题",value:"多选题"}),(0,a.bF)(Re,{label:"判断题",value:"判断题"}),(0,a.bF)(Re,{label:"简答题",value:"简答题"})]),_:1},8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"分值",prop:"score"},{default:(0,a.k6)(()=>[(0,a.bF)(P,{modelValue:ye.score,"onUpdate:modelValue":t[12]||(t[12]=e=>ye.score=e),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),(0,a.bF)(r,{label:"题目内容",prop:"question"},{default:(0,a.k6)(()=>[(0,a.bF)(l,{type:"textarea",modelValue:ye.question,"onUpdate:modelValue":t[13]||(t[13]=e=>ye.question=e),placeholder:"请输入题目内容",rows:"4"},null,8,["modelValue"])]),_:1}),"单选题"===ye.question_type||"多选题"===ye.question_type?((0,a.uX)(),(0,a.CE)(a.FK,{key:0},[(0,a.Lk)("div",V,[t[40]||(t[40]=(0,a.Lk)("h4",null,"选项",-1)),(0,a.bF)(i,{type:"primary",size:"small",onClick:Be},{default:(0,a.k6)(()=>t[39]||(t[39]=[(0,a.eW)("添加选项")])),_:1,__:[39]})]),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ye.options,(e,o)=>((0,a.uX)(),(0,a.Wv)(r,{key:o,label:"选项 "+String.fromCharCode(65+o)},{default:(0,a.k6)(()=>[(0,a.Lk)("div",R,[(0,a.bF)(l,{modelValue:e.text,"onUpdate:modelValue":t=>e.text=t,placeholder:"请输入选项内容"},null,8,["modelValue","onUpdate:modelValue"]),(0,a.bF)(Se,{modelValue:e.isCorrect,"onUpdate:modelValue":t=>e.isCorrect=t},{default:(0,a.k6)(()=>t[41]||(t[41]=[(0,a.eW)("正确答案")])),_:2,__:[41]},1032,["modelValue","onUpdate:modelValue"]),(0,a.bF)(i,{type:"danger",icon:"Delete",circle:"",size:"small",onClick:e=>je(o)},null,8,["onClick"])])]),_:2},1032,["label"]))),128))],64)):"判断题"===ye.question_type?((0,a.uX)(),(0,a.Wv)(r,{key:1,label:"正确答案",prop:"correct_answer"},{default:(0,a.k6)(()=>[(0,a.bF)(tt,{modelValue:ye.correct_answer,"onUpdate:modelValue":t[14]||(t[14]=e=>ye.correct_answer=e)},{default:(0,a.k6)(()=>[(0,a.bF)(et,{label:"正确"},{default:(0,a.k6)(()=>t[42]||(t[42]=[(0,a.eW)("正确")])),_:1,__:[42]}),(0,a.bF)(et,{label:"错误"},{default:(0,a.k6)(()=>t[43]||(t[43]=[(0,a.eW)("错误")])),_:1,__:[43]})]),_:1},8,["modelValue"])]),_:1})):((0,a.uX)(),(0,a.Wv)(r,{key:2,label:"参考答案",prop:"correct_answer"},{default:(0,a.k6)(()=>[(0,a.bF)(l,{type:"textarea",modelValue:ye.correct_answer,"onUpdate:modelValue":t[15]||(t[15]=e=>ye.correct_answer=e),placeholder:"请输入参考答案",rows:"3"},null,8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),(0,a.bF)(ne,{title:"考试成绩",modelValue:pe.value,"onUpdate:modelValue":t[18]||(t[18]=e=>pe.value=e),width:"800px"},{default:(0,a.k6)(()=>[oe.value?((0,a.uX)(),(0,a.CE)("div",$,[(0,a.Lk)("h3",null,(0,o.v_)(oe.value.title),1),(0,a.bF)(d,{class:"statistics-card"},{default:(0,a.k6)(()=>[(0,a.Lk)("div",L,[(0,a.Lk)("div",E,[t[46]||(t[46]=(0,a.Lk)("div",{class:"statistics-label"},"总人数",-1)),(0,a.Lk)("div",W,(0,o.v_)(ve.total_students||0),1)]),(0,a.Lk)("div",K,[t[47]||(t[47]=(0,a.Lk)("div",{class:"statistics-label"},"及格人数",-1)),(0,a.Lk)("div",A,(0,o.v_)(ve.passed_students||0),1)]),(0,a.Lk)("div",I,[t[48]||(t[48]=(0,a.Lk)("div",{class:"statistics-label"},"及格率",-1)),(0,a.Lk)("div",S,(0,o.v_)(ve.pass_rate||0)+"%",1)]),(0,a.Lk)("div",U,[t[49]||(t[49]=(0,a.Lk)("div",{class:"statistics-label"},"平均分",-1)),(0,a.Lk)("div",M,(0,o.v_)(ve.average_score||0),1)])])]),_:1}),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(N,{data:me.value,stripe:"",border:"",style:{width:"100%","margin-top":"15px"}},{default:(0,a.k6)(()=>[(0,a.bF)(c,{type:"index",width:"50"}),(0,a.bF)(c,{prop:"student_name",label:"学生姓名"}),(0,a.bF)(c,{prop:"score",label:"得分",width:"100",sortable:""},{default:(0,a.k6)(e=>[(0,a.Lk)("span",{class:(0,o.C4)(e.row.score>=oe.value.pass_score?"pass-score":"fail-score")},(0,o.v_)(e.row.score),3)]),_:1}),(0,a.bF)(c,{label:"考试时间",width:"160"},{default:(0,a.k6)(e=>[(0,a.eW)((0,o.v_)((0,s.R1)(p.Y)(e.row.exam_date,"YYYY-MM-DD HH:mm")),1)]),_:1}),(0,a.bF)(c,{label:"状态",width:"100"},{default:(0,a.k6)(e=>[(0,a.bF)(j,{type:e.row.score>=oe.value.pass_score?"success":"danger"},{default:(0,a.k6)(()=>[(0,a.eW)((0,o.v_)(e.row.score>=oe.value.pass_score?"及格":"不及格"),1)]),_:2},1032,["type"])]),_:1}),(0,a.bF)(c,{label:"操作",width:"100",fixed:"right"},{default:(0,a.k6)(e=>[(0,a.bF)(i,{type:"primary",size:"small",onClick:t=>Je(e.row)},{default:(0,a.k6)(()=>t[50]||(t[50]=[(0,a.eW)("详情")])),_:2,__:[50]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[lt,_e.value]])])):(0,a.Q3)("",!0)]),_:1},8,["modelValue"])])}}},z=l(1241);const Q=(0,z.A)(Y,[["__scopeId","data-v-69cdaa63"]]);var T=Q},6434:function(e,t,l){var a=l(653);t.A={getExams(e){return a.A.get("/api/exams",{params:e})},getExam(e){return a.A.get(`/api/exams/${e}`)},createExam(e){return a.A.post("/api/exams",e)},updateExam(e,t){return a.A.put(`/api/exams/${e}`,t)},deleteExam(e){return a.A.delete(`/api/exams/${e}`)},getExamQuestions(e){return a.A.get(`/api/exams/${e}/questions`)},createQuestion(e,t){return a.A.post(`/api/exams/${e}/questions`,t)},updateQuestion(e,t){return a.A.put(`/api/exams/questions/${e}`,t)},deleteQuestion(e){return a.A.delete(`/api/exams/questions/${e}`)},importQuestions(e,t){return a.A.post(`/api/exams/${e}/questions/import`,t)},importQuestionsFromWord(e,t){return a.A.post(`/api/exams/${e}/questions/import-word`,t,{headers:{"Content-Type":"multipart/form-data"}})},submitExam(e){return a.A.post("/api/exams/submit",e)},getExamResults(e,t){return a.A.get(`/api/exams/${e}/results`)},getStudentResults(e){return a.A.get(`/api/exams/student/${e}/results`)}}},9634:function(e,t,l){function a(e,t="YYYY-MM-DD HH:mm:ss"){if(console.log(e),!e)return"";const l=new Date(e);if(console.log(l),isNaN(l.getTime()))return"";const a={"M+":l.getMonth()+1,"D+":l.getDate(),"H+":l.getHours(),"m+":l.getMinutes(),"s+":l.getSeconds(),"q+":Math.floor((l.getMonth()+3)/3),S:l.getMilliseconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(l.getFullYear()+"").substring(4-RegExp.$1.length)));for(let o in a)new RegExp("("+o+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?a[o]:("00"+a[o]).substring((""+a[o]).length)));return console.log(t),t}l.d(t,{Y:function(){return a}})}}]);
//# sourceMappingURL=404.cb454656.js.map
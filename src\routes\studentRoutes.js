const express = require('express');
const router = express.Router();
const studentController = require('../controllers/studentController');
const { protect, authorize } = require('../controllers/authController');

// 所有路由都需要授权
router.use(protect);

// 获取所有学生 - 管理员和教师可访问
router.get('/', authorize('admin', 'teacher'), studentController.getAllStudents);

// 搜索学生 - 管理员和教师可访问
router.get('/search', authorize('admin', 'teacher'), studentController.searchStudents);

// 根据用户ID查找学生
router.get('/by-user/:userId', studentController.getStudentByUserId);

// 获取单个学生信息 - 管理员、教师和学生本人可访问
router.get('/:id', studentController.getStudentById);

// 创建学生信息 - 仅管理员可访问
router.post('/', authorize('admin'), studentController.createStudent);

// 更新学生信息 - 仅管理员可访问
router.put('/:id', authorize('admin'), studentController.updateStudent);

// 删除学生信息 - 仅管理员可访问
router.delete('/:id', authorize('admin'), studentController.deleteStudent);

// 批量导入学生信息 - 仅管理员可访问
router.post('/import', authorize('admin'), studentController.upload.single('file'), studentController.importStudents);

module.exports = router;